#!/bin/bash

# 启动所有服务的函数
start_all() {
    echo "开始启动所有服务..."
    
    # 启动 Docker 容器
    docker start mysql-cmdb
    docker start redis-cmdb
    
    # 进入 cmdb-api 目录并初始化
    cd cmdb-api
    pipenv run flask db-setup && pipenv run flask common-check-new-columns && pipenv run flask cmdb-init-cache
    
    # 启动后端服务
    pipenv run flask run -h 0.0.0.0 &
    
    # 启动前端服务
    cd ../cmdb-ui
    yarn run serve &
    
    # 启动 worker
    cd ../cmdb-api
    pipenv run celery -A celery_worker.celery worker -E -Q one_cmdb_async -c 1 --logfile=./logs/one_cmdb_async.log -D &
    pipenv run celery -A celery_worker.celery worker -E -Q acl_async -c 1 --logfile=./logs/one_acl_async.log -D &
    
    echo "所有服务启动完成"
}

# 停止所有服务的函数
stop_all() {
    echo "开始停止所有服务..."
    
    # 停止 Docker 容器
    docker stop mysql-cmdb
    docker stop redis-cmdb
    
    # 停止 Flask 服务
    pkill -f "flask run"
    
    # 停止 Vue.js 服务
    pkill -f "vue-cli-service serve"
    
    # 停止 Celery worker
    pkill -f "celery -A celery_worker.celery worker"
    
    echo "所有服务已停止"
}

# 重启后端服务的函数
restart_backend() {
    echo "开始重启后端服务..."
    
    # 停止后端服务和 worker
    pkill -f "flask run"
    pkill -f "celery -A celery_worker.celery worker"
    
    # 进入 cmdb-api 目录
    cd cmdb-api
    
    # 重新启动后端服务
    pipenv run flask run -h 0.0.0.0 &
    
    # 重新启动 worker
    pipenv run celery -A celery_worker.celery worker -E -Q one_cmdb_async -c 1 --logfile=one_cmdb_async.log -D &
    pipenv run celery -A celery_worker.celery worker -E -Q acl_async -c 1 --logfile=one_acl_async.log -D &
    
    echo "后端服务重启完成"
}

# 启动前端服务的函数
start_frontend() {
    echo "开始启动前端服务..."
    
    # 进入前端目录并启动服务
    cd cmdb-ui
    yarn run serve &
    
    echo "前端服务启动完成"
}

# 显示使用方法
usage() {
    echo "使用方法: $0 {start|stop|restart-backend|start-frontend}"
    echo "  start          - 启动所有服务"
    echo "  stop           - 停止所有服务"
    echo "  restart-backend - 仅重启后端服务和worker"
    echo "  start-frontend  - 仅启动前端服务"
    exit 1
}

# 主程序
case "$1" in
    start)
        start_all
        ;;
    stop)
        stop_all
        ;;
    restart-backend)
        restart_backend
        ;;
    start-frontend)
        start_frontend
        ;;
    *)
        usage
        ;;
esac

exit 0 