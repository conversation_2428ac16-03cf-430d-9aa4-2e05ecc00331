import cmdb_en from '@/modules/cmdb/lang/en.js'
import cs_en from '../views/setting/lang/en.js'
import acl_en from '@/modules/acl/lang/en.js'

export default {
    commonMenu: {
        permission: 'Permission',
        role: 'Roles',
        resource: 'Resources',
        resourceType: 'Resource Types',
        trigger: 'Triggers',
    },
    screen: 'Big Screen',
    dashboard: 'Dashboard',
    admin: 'Admin',
    user: 'User',
    role: 'Role',
    operation: 'Operation',
    login: 'Login',
    refresh: 'Refresh',
    cancel: 'Cancel',
    confirm: 'Confirm',
    create: 'Create',
    edit: 'Edit',
    deleting: 'Deleting',
    deletingTip: 'Deleting, total of {total}, {successNum} succeeded, {errorNum} failed',
    grant: 'Grant',
    revoke: 'Revoke',
    login_at: 'Login At',
    logout_at: 'Logout At',
    createSuccess: 'Create Success',
    editSuccess: 'edit Success',
    warning: 'Warning',
    export: 'Export',
    placeholderSearch: 'Please Search',
    success: 'Success',
    fail: 'Fail',
    browser: 'Browser',
    status: 'Status',
    type: 'Type',
    description: 'Description',
    new: 'New',
    add: 'Add',
    define: 'Define',
    update: 'Update',
    clear: 'Clear',
    delete: 'Delete',
    copy: 'Copy',
    created_at: 'Created At',
    updated_at: 'Updated At',
    placeholder1: 'Please Input',
    placeholder2: 'Please Select',
    confirmDelete: 'Confirm delete?',
    confirmDelete2: 'Confirm delete [{name}]?',
    query: 'Query',
    search: 'Search',
    hide: 'Hide',
    expand: 'Expand',
    save: 'Save',
    submit: 'Submit',
    upload: 'Import',
    download: 'Export',
    name: 'Name',
    alias: 'Alias',
    desc: 'Description',
    other: 'Other',
    icon: 'Icon',
    addSuccess: 'Added successfully',
    uploadSuccess: 'Import successfully',
    saveSuccess: 'Save successfully',
    copySuccess: 'Copy successfully',
    updateSuccess: 'Updated successfully',
    deleteSuccess: 'Deleted successfully',
    operateSuccess: 'The operation was successful',
    noPermission: 'No Permission',
    noData: 'No Data',
    seconds: 'Seconds',
    createdAt: 'Created At',
    updatedAt: 'Updated At',
    deletedAt: 'Deleted At',
    required: 'required',
    email: 'Email',
    wechat: 'Wechat',
    dingding: 'DingTalk',
    feishu: 'Feishu',
    bot: 'Robot',
    checkAll: 'Select All',
    loading: 'Loading...',
    view: 'View',
    reset: 'Reset',
    yes: 'Yes',
    no: 'No',
    all: 'All',
    selectRows: 'Selected: {rows} items',
    itemsPerPage: '/page',
    '星期一': 'Monday',
    '星期二': 'Tuesday',
    '星期三': 'Wednesday',
    '星期四': 'Thursday',
    '星期五': 'Friday',
    '星期六': 'Saturday',
    '星期日': 'Sunday',
    hour: 'hour',
    'items/page': '{items} items/page',
    max: 'Max',
    min: 'Min',
    visual: 'Visual',
    default: 'default',
    tip: 'Tip',
    pagination: {
        total: '{range0}-{range1} of {total} items'
    },
    topMenu: {
        personalCenter: 'My Profile',
        logout: 'Logout',
        confirmLogout: 'Are you sure to log out?'
    },
    cmdbFilterComp: {
        conditionFilter: 'Conditional filtering',
        and: 'and',
        or: 'or',
        is: 'equal',
        '~is': 'not equal',
        contain: 'contain',
        '~contain': 'not contain',
        start_with: 'start_with',
        '~start_with': 'not start_with',
        end_with: 'end_with',
        '~end_with': 'not end_with',
        '~value': 'null',
        value: 'not null',
        in: 'in',
        '~in': 'not in',
        range: 'range',
        '~range': 'out of range',
        compare: 'compare',
        addHere: 'Add Here',
        split: 'split by {separator}'
    },
    customIconSelect: {
        outlined: 'Outlined',
        filled: 'Filled',
        multicolor: 'Multicolor',
        custom: 'Custom',
        preview: 'Preview',
        sizeLimit: 'The image size cannot exceed 2MB!',
        nodata: 'There are currently no custom icons available. Click here to upload'
    },
    regexSelect: {
        limitedFormat: 'Limited Format',
        regExp: 'RegExp',
        errMsg: 'Error Message',
        test: 'Test',
        placeholder: 'Please Select RegExp',
        error: 'Error',
        letter: 'letter',
        number: 'number',
        letterAndNumber: 'letter&number',
        phone: 'phone',
        landline: 'landline',
        zipCode: 'zip code',
        IDCard: 'ID card',
        ip: 'IPv4',
        email: 'email',
        link: 'link',
        monetaryAmount: 'monetary amount',
        custom: 'custom',
    },
    cmdb: cmdb_en,
    cs: cs_en,
    acl: acl_en,
}
