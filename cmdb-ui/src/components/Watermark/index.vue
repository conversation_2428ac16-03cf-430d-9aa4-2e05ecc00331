<template>
  <div class="watermark-container" v-if="show">
    <div class="watermark" :style="style" ref="watermark"></div>
  </div>
</template>

<script>
export default {
  name: 'Watermark',
  props: {
    show: {
      type: Boolean,
      default: true
    },
    content: {
      type: String,
      default: ''
    },
    width: {
      type: Number,
      default: 300
    },
    height: {
      type: Number,
      default: 200
    },
    rotate: {
      type: Number,
      default: -20
    },
    fontSize: {
      type: Number,
      default: 14
    },
    opacity: {
      type: Number,
      default: 0.15
    }
  },
  computed: {
    style() {
      return {
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        zIndex: 9999,
        pointerEvents: 'none',
        backgroundImage: this.watermarkImage,
        backgroundRepeat: 'repeat'
      }
    }
  },
  data() {
    return {
      watermarkImage: ''
    }
  },
  mounted() {
    this.createWatermark()
  },
  methods: {
    createWatermark() {
      const canvas = document.createElement('canvas')
      canvas.width = this.width
      canvas.height = this.height
      const ctx = canvas.getContext('2d')

      // 清空画布
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // 保存画布状态
      ctx.save()

      // 设置文字样式
      ctx.fillStyle = `rgba(0, 0, 0, ${this.opacity})`
      ctx.font = `${this.fontSize}px Arial`

      // 移动到画布中心进行旋转
      ctx.translate(canvas.width / 2, canvas.height / 2)
      ctx.rotate((Math.PI / 180) * this.rotate)
      ctx.translate(-canvas.width / 2, -canvas.height / 2)

      // 获取文本宽度以实现居中效果
      const text = this.content || `${this.$store.state.user?.username || ''}`
      const textWidth = ctx.measureText(text).width

      // 绘制文本（居中）
      ctx.fillText(
        text,
        (canvas.width - textWidth) / 2,
        canvas.height / 2
      )

      // 恢复画布状态
      ctx.restore()

      this.watermarkImage = `url(${canvas.toDataURL()})`
    }
  },
  watch: {
    content: {
      handler() {
        this.createWatermark()
      }
    }
  }
}
</script>

<style scoped>
.watermark-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 9999;
}

.watermark {
  background-attachment: fixed;  /* 确保水印固定不动 */
}
</style>
