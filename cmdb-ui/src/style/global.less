::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  width: 8px;
  background-color: rgba(217, 217, 217, 0.2);
  box-shadow: inset 0px 4px 4px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  // border-radius: 2em;
}

::-webkit-scrollbar-thumb {
  background-color: @scrollbar-color;
  background-clip: padding-box;
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: @scrollbar-color;
}

@import '~ant-design-vue/dist/antd.less';
@import './static.less';

body {
  &.colorWeak {
    filter: invert(80%);
  }

  &.userLayout {
    overflow: auto;
  }
  .text-color-1 {
    color: @text-color_1;
  }
  .text-color-2 {
    color: @text-color_2;
  }
  .text-color-3 {
    color: @text-color_3;
  }
  .text-color-4 {
    color: @text-color_4;
  }
  .border-radius-box {
    border-radius: @border-radius-box;
  }
}

.ant-layout {
  background-color: @layout-content-background;
}

.layout.ant-layout {
  height: auto;
  overflow-x: hidden;

  &.mobile,
  &.tablet {
    .ant-layout-content {
      .content {
        margin: 24px 0 0;
      }
    }

    /**
     * ant-table-wrapper
     * 覆盖的表格手机模式样式，如果想修改在手机上表格最低宽度，可以在这里改动
     */
    .ant-table-wrapper {
      .ant-table-content {
        overflow-y: auto;
      }

      .ant-table-body {
        min-width: 800px;
      }
    }

    .topmenu {
      /* 必须为 topmenu  才能启用流式布局 */
      &.content-width-Fluid {
        .header-index-wide {
          margin-left: 0;
        }
      }
    }
  }

  &.mobile {
    .sidemenu {
      .ant-header-fixedHeader {
        &.ant-header-side-opened,
        &.ant-header-side-closed {
          width: 100%;
        }
      }
    }
  }

  &.ant-layout-has-sider {
    flex-direction: row;
  }

  .trigger {
    font-size: 20px;
    // line-height: @layout-header-height;
    padding: 4px 10px;
    cursor: pointer;
    color: @layout-header-font-color;
    border-radius: 4px;
    height: @layout-header-icon-height;
    line-height: @layout-header-icon-height;
    display: inline-flex;
    align-items: center;
    margin-right: 10px;

    &:hover {
      color: @primary-color;
    }
  }

  .topmenu {
    .ant-header-fixedHeader {
      position: fixed;
      top: 0;
      right: 0;
      z-index: 9;
      width: 100%;
      transition: width 0.2s;

      &.ant-header-side-opened {
        width: 100%;
        height: @layout-header-height;
      }

      &.ant-header-side-closed {
        width: 100%;
        height: @layout-header-height;
      }
    }

    /* 必须为 topmenu  才能启用流式布局 */
    &.content-width-Fluid {
      .header-index-wide {
        max-width: unset;
        margin-left: 24px;
      }

      .page-header-index-wide {
        max-width: unset;
      }
    }
  }

  .sidemenu {
    .ant-header-fixedHeader {
      position: fixed;
      top: 0;
      right: 0;
      z-index: 9;
      width: 100%;
      transition: width 0.2s;

      &.ant-header-side-opened {
        width: calc(100% - 220px);
      }

      &.ant-header-side-closed {
        width: calc(100% - 80px);
      }
    }
  }

  .header {
    width: 100%;
    height: @layout-header-height;
    padding: 0 12px 0 0;
    background: @layout-header-background;
    // box-shadow: 0px 1px 4px rgba(0, 21, 41, 0.12);
    position: relative;
    display: inline-flex;
    align-items: center;
  }

  .header,
  .top-nav-header-index {
    .user-wrapper {
      height: 100%;
      margin-left: auto;

      .content-box {
        display: inline-flex;
        flex-direction: row;
        align-items: center;
        height: @layout-header-height;
      }

      .action {
        cursor: pointer;
        padding: 0 8px;
        display: inline-flex;
        transition: all 0.3s;
        height: @layout-header-icon-height;
        color: @layout-header-font-color;
        align-items: center;
        border-radius: 4px;
        .avatar {
          margin-right: 5px;
          color: @layout-header-font-color;
          vertical-align: middle;
          background-color: #fff;
        }

        .icon {
          font-size: 16px;
          padding: 4px;
        }
      }
    }

    &.dark {
      .user-wrapper {
        .action {
          color: rgba(255, 255, 255, 0.85);

          a {
            color: rgba(255, 255, 255, 0.85);
          }

          &:hover {
            background: rgba(255, 255, 255, 0.16);
          }
        }
      }
    }
  }

  &.mobile,
  &.tablet {
    .top-nav-header-index {
      .header-index-wide {
        .header-index-left {
          .trigger {
            color: rgba(255, 255, 255, 0.85);
            padding: 0 12px;
          }

          .logo.top-nav-header {
            flex: 0 0 56px;
            text-align: center;
            line-height: 58px;

            h1 {
              display: none;
            }
          }
        }
      }

      &.light {
        .header-index-wide {
          .header-index-left {
            .trigger {
              color: rgba(0, 0, 0, 0.65);
            }
          }
        }

        //
      }
    }
  }

  &.tablet {
    // overflow: hidden; text-overflow:ellipsis; white-space: nowrap;
    .top-nav-header-index {
      .header-index-wide {
        .header-index-left {
          .logo > a {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .ant-menu.ant-menu-horizontal {
          flex: 0 1 auto;
          white-space: normal;
        }
      }
    }
  }

  .top-nav-header-index {
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    position: relative;
    transition: background 0.3s, width 0.2s;

    .header-index-wide {
      max-width: 1200px;
      margin: auto;
      padding-left: 0;
      display: flex;
      height: 64px;

      .ant-menu.ant-menu-horizontal {
        max-width: 835px;
        flex: 0 1 835px;
        border: none;
        height: 64px;
        line-height: 64px;
      }

      .header-index-left {
        flex: 0 1 1000px;
        display: flex;

        .logo.top-nav-header {
          flex: 0 0 165px;
          width: 165px;
          height: 64px;
          position: relative;
          line-height: 64px;
          transition: all 0.3s;
          overflow: hidden;

          img,
          svg {
            display: inline-block;
            vertical-align: middle;
            height: 32px;
            width: 32px;
          }

          h1 {
            color: #fff;
            display: inline-block;
            vertical-align: top;
            font-size: 16px;
            margin: 0 0 0 12px;
            font-weight: 400;
          }
        }
      }

      .header-index-right {
        flex: 1 0 auto;
        height: 64px;
        overflow: hidden;

        .content-box {
          float: right;
        }
      }
    }

    &.light {
      .header-index-wide {
        .header-index-left {
          .logo {
            h1 {
              color: #002140;
            }
          }
        }
      }
    }
  }

  // 内容区
  .ant-layout-content {
    padding: 0 24px;
  }

  // footer
  .ant-layout-footer {
    padding: 0;
  }
}

.topmenu {
  .page-header-index-wide {
    max-width: 1200px;
    margin: 0 auto;
  }
}

// drawer-sider 自定义
.ant-drawer.drawer-sider {
  .sider {
    box-shadow: none;
  }

  &.dark {
    .ant-drawer-content {
      background-color: rgb(0, 21, 41);
    }
  }

  &.light {
    box-shadow: none;

    .ant-drawer-content {
      background-color: #fff;
    }
  }

  .ant-drawer-body {
    padding: 0;
  }
}

// 菜单样式
.sider {
  position: relative;
  z-index: 10;
  min-height: 100vh;

  .ant-layout-sider-children {
    overflow-y: hidden;
    > .ant-menu {
      height: calc(100vh - 40px);
      overflow-x: hidden;
      overflow-y: hidden;
      &:hover {
        overflow-y: auto;
      }
    }
  }

  &.ant-fixed-sidemenu {
    position: fixed;
    height: 100%;
    z-index: 99;
  }

  .logo {
    position: relative;
    height: @layout-header-height;
    padding-left: 0;
    overflow: hidden;
    line-height: @layout-header-height;
    background: @layout-header-background;
    // transition: all 0.3s;

    img,
    svg,
    h1 {
      display: inline-block;
      vertical-align: middle;
    }

    img,
    svg {
      height: 32px;
      width: 32px;
    }

    h1 {
      color: #fff;
      font-size: 20px;
      margin: 0 0 0 12px;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
      font-weight: 600;
      vertical-align: middle;
    }
  }

  &.light {
    background-color: #fff;

    .logo {
      background: @layout-header-background;
      box-shadow: 0 1px 3px 0px #9eabbe25;
    }
  }
}

.ant-menu-vertical .ant-menu-item:not(:last-child),
.ant-menu-vertical-left .ant-menu-item:not(:last-child),
.ant-menu-vertical-right .ant-menu-item:not(:last-child),
.ant-menu-inline .ant-menu-item:not(:last-child) {
  margin: 0;
}

// 从此处开始
.ops-side-bar.ant-menu {
  transition: none;
  .ant-menu-item {
    text-overflow: initial !important;
  }
}
.ops-side-bar.ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
  background: @layout-sidebar-selected-color;
  transition: none;
}
.ops-side-bar.ant-menu .ant-menu-submenu .ant-menu-item.ant-menu-item-selected {
  background-position-x: 12px;
}

@keyframes wordsLoop {
  0% {
    margin-left: 0;
  }
  100% {
    margin-left: -300%;
  }
}

.ops-side-bar.ant-menu-light {
  border-right-color: #e8e8e8;
  background: @layout-sidebar-color;
  background-repeat: no-repeat !important;
  background-size: cover;
  .ant-menu-inline.ant-menu-sub {
    background-color: @layout-sidebar-sub-color;
  }
  .ant-menu-submenu-content .ant-menu-item,
  .ant-menu-item {
    > a {
      display: inline-flex;
      align-items: center;
      color: @layout-sidebar-font-color;
      i {
        color: @layout-sidebar-icon-color;
      }
    }
    &:hover {
      .scroll {
        animation: 5s wordsLoop linear infinite normal;
      }
      span {
        text-overflow: initial;
      }
    }
    span {
      max-width: 130px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    a:hover {
      color: @layout-sidebar-font-color;
      font-weight: 600;
    }
    &:hover .custom-menu-extra-ellipsis {
      display: inline-flex;
      justify-content: center;
      align-items: center;
    }
    // span:hover {
    //   animation: 5s wordsLoop linear infinite normal;
    // }

    .custom-menu-extra-ellipsis {
      position: absolute;
      right: 10px;
      display: none;
      width: 20px;
      height: 20px;
      top: 10px;
      border-radius: 5px;
      &:hover {
        color: @primary-color;
      }
      .anticon {
        margin-right: 0;
      }
    }
  }
  .ant-menu-item:active,
  .ant-menu-submenu-title:active {
    background: transparent;
  }
  .ant-menu-submenu-content .ant-menu-item-selected,
  .ant-menu-item-selected {
    a,
    a:hover {
      color: @layout-sidebar-selected-font-color;
      font-weight: 600;
      i {
        color: @layout-sidebar-selected-font-color;
      }
    }
  }
  .ant-menu-item::after,
  .ant-menu-inline .ant-menu-item::after {
    border-right: none;
  }

  .ant-menu-submenu {
    color: @layout-sidebar-font-color;
    i {
      color: @layout-sidebar-icon-color;
    }
  }
  .ant-menu-submenu-title:hover {
    color: @layout-sidebar-font-color;
    background: @layout-sidebar-selected-color;
    font-weight: 600;
    .ant-menu-submenu-arrow::before,
    .ant-menu-submenu-arrow::after {
      background: @layout-sidebar-arrow-color;
    }
  }
  .ant-menu-submenu-selected {
    > .ant-menu-submenu-title {
      color: @layout-sidebar-selected-font-color;
      font-weight: 800;
      i {
        color: @layout-sidebar-selected-font-color;
      }
    }
  }
  .ant-menu-submenu-content .ant-menu-submenu-active .ant-menu-submenu-title {
    background-color: #0000000a;
  }
  .ant-menu-item-disabled {
    background: none;
    cursor: default;
    padding-left: 0 !important;
    > a {
      padding-left: 10px;
      color: @layout-sidebar-disabled-font-color !important;
      font-size: 12px;
    }
    &:hover {
      background: none;
    }
  }
  .ant-menu-submenu-arrow::after,
  .ant-menu-submenu-arrow::before {
    background: @layout-sidebar-arrow-color;
  }

  .ant-menu-item.ant-menu-item-active:hover {
    background: @layout-sidebar-selected-color;
  }
}
// 侧边栏折叠时
.ops-side-bar {
  .ant-menu-submenu.ant-menu-submenu-placement-rightTop {
    > .ant-menu {
      // background: url('../assets/sidebar_background.png');
      background: @layout-sidebar-color;
      background-position-x: center;
      background-position-y: center;
      background-repeat: no-repeat !important;
    }
  }
}

.custom-menu-extra-submenu {
  z-index: 9999;
  position: fixed;
  .ant-popover-inner-content {
    padding: 5px;
    .custom-menu-extra-submenu-item {
      padding: 5px 6px;
      cursor: pointer;
      i {
        margin-right: 5px !important;
      }
      &:hover {
        background-color: @primary-color_5;
      }
    }
  }
}

.ops-side-bar {
  .ant-menu-inline-collapsed {
    .ant-menu-submenu-selected {
      background: #0000000a;
    }
  }
}

// 外置的样式控制
.user-dropdown-menu {
  span {
    user-select: none;
  }
}

.user-dropdown-menu-wrapper.ant-dropdown-menu {
  padding: 4px 0;

  .ant-dropdown-menu-item {
    // width: 100px;
    padding: 0 20px;
  }

  .ant-dropdown-menu-item > .anticon:first-child,
  .ant-dropdown-menu-item > a > .anticon:first-child,
  .ant-dropdown-menu-submenu-title > .anticon:first-child .ant-dropdown-menu-submenu-title > a > .anticon:first-child {
    min-width: 12px;
    margin-right: 8px;
  }
}

// 数据列表 样式
.table-alert {
  margin-bottom: 16px;
}

.table-page-search-wrapper {
  .ant-form-inline {
    .ant-form-item {
      display: flex;
      margin-bottom: 24px;
      margin-right: 0;

      .ant-form-item-control-wrapper {
        flex: 1 1;
        display: inline-block;
        vertical-align: middle;
      }

      > .ant-form-item-label {
        line-height: 32px;
        padding-right: 8px;
        width: auto;
      }

      .ant-form-item-control {
        height: 32px;
        line-height: 32px;
      }
    }
  }

  .table-page-search-submitButtons {
    display: block;
    margin-bottom: 24px;
    white-space: nowrap;
  }
}

.content {
  .table-operator {
    margin-bottom: 18px;

    button {
      margin-right: 8px;
    }
  }
}

.custom-drawer-bottom-action {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  border-top: 1px solid #e9e9e9;
  padding: 10px;
  background: #fff;
  text-align: right;
  .ant-btn {
    margin-right: 10px;
  }
}

.dag-status-dot {
  width: 8px;
  height: 8px;
  display: inline-block;
  border-radius: 50%;
  margin-right: 2px;
}
.dag-status-dot-running {
  animation: twinkle 1s infinite alternate;
}
@keyframes twinkle {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0.2;
  }
}

.dag-history-filter {
  .ant-popover-inner-content {
    padding: 0;
    .dag-history-filter-inner {
      width: 100px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      cursor: pointer;
      font-size: 12px;
      &:hover {
        background: #f0f0f0;
      }
      .dag-history-filter-icon {
        cursor: pointer;
      }
    }
  }
}

//element-ui select 样式
.el-input__inner {
  border-radius: 2px !important;
}

.el-input__inner:hover,
.el-select .el-input.is-focus .el-input__inner,
.el-select .el-input__inner:focus,
.el-button.is-plain:focus,
.el-button.is-plain:hover,
.el-input.is-active .el-input__inner,
.el-input__inner:focus {
  border-color: @primary-color !important;
}
.el-button--text,
.el-select-dropdown__item.selected,
.el-button.is-plain:focus,
.el-button.is-plain:hover {
  color: @primary-color !important;
}

.el-select-dropdown__item.hover {
  background-color: @primary-color_5;
}

.ant-tabs-nav .ant-tabs-tab {
  margin-right: 16px;
}

// el-descriptions 的样式
.el-descriptions-item__label.is-bordered-label {
  cursor: default;
  white-space: nowrap;
}

.jsoneditor-poweredBy {
  display: none;
}

// 全屏模式下  topmenu和sidemenu隐藏
.ops-fullscreen {
  .ant-layout-sider,
  .ant-layout .ant-layout-header {
    display: none;
  }
}
// 仅topmenu下 sidemenu隐藏
.ops-only-topmenu {
  .ant-layout-sider {
    box-shadow: none;
    .ant-layout-sider-children {
      background: @primary-color_7;
      .ant-menu {
        display: none;
      }
    }
  }
}

// vue-treeselect 单选样式
.ops-setting-treeselect.vue-treeselect--single {
  line-height: 32px;
  .vue-treeselect__control {
    border-radius: 2px !important;
    height: 32px;
    border-color: #e4e7ed;
    .vue-treeselect__value-container{
      height: 30px;
    }
    .vue-treeselect__input-container{
      display: flex;
      align-items: center;
      height: 30px;
    }

    &:hover {
      border-color: #597ef7 !important;
    }
  }
  .vue-treeselect__placeholder,
  .vue-treeselect__single-value {
    line-height: 28px !important;
  }
  .vue-treeselect__input {
    height: 28px !important;
    line-height: 28px !important;
  }
}
// vue-treeselect 多选样式
.ops-setting-treeselect.vue-treeselect--multi {
  .vue-treeselect__control {
    border-radius: 2px !important;
    border-color: #e4e7ed;
  }

  &:hover {
    border-color: #597ef7 !important;
  }
}

.custom-vue-treeselect__control(
  @bgColor: @primary-color_7,
  @border: none,
  @hoverBgColor: none,
  @hoverBorderColor: none,
) {
  .vue-treeselect__control {
    background-color: @bgColor;
    border: @border;
  }

  .mixin(@borderColor) when (iscolor(@borderColor)) {
    border-color: @borderColor !important;
  }

  &:not(.vue-treeselect--disabled):not(.vue-treeselect--focused) {
    .vue-treeselect__control {
      &:hover {
        background-color: if(iscolor(@hoverBgColor), @hoverBgColor, @bgColor);
        .mixin(@hoverBorderColor)
      }
    }
  }
}

.custom-treeselect {
  .custom-vue-treeselect__control(
    @primary-color_7,
    none,
    @primary-color_5,
    none
  );

  .vue-treeselect__control {
    height: var(--custom-height) !important;
    border-radius: 2px !important;
  }
  .vue-treeselect__placeholder,
  .vue-treeselect__single-value {
    line-height: var(--custom-height) !important;
  }

  .vue-treeselect__menu {
    border: none;
    box-shadow: 0px 4px 6px rgba(78, 94, 160, 0.25) !important;
  }
  .vue-treeselect__limit-tip-text {
    margin: 0;
  }

  &.vue-treeselect--focused {
    .vue-treeselect__control {
      border: 1px solid @primary-color;
    }
  }
}

// 自定义背景颜色和border
.custom-treeselect-bgcAndBorder {
  .custom-vue-treeselect__control(
    var(--custom-bg-color),
    var(--custom-border),
    var(--custom-hover-bg-color),
    var(--custom-hover-border-color)
  );
}

// 白色背景
.custom-treeselect-white {
  .custom-vue-treeselect__control(
    ~'#fff !important',
    1px solid #d9d9d9,
    none,
    @primary-color
  );
}

// 自定义背景颜色和border
.custom-treeselect.vue-treeselect--multi {
  .vue-treeselect__multi-value,
  .vue-treeselect__multi-value-item {
    line-height: var(--custom-multiple-lineHeight);
    line-height: 18px;
  }
}
.custom-treeselect.vue-treeselect--open-below .vue-treeselect__menu {
  margin-top: 0;
}
.custom-treeselect.vue-treeselect--has-value .vue-treeselect__input {
  vertical-align: middle;
}
.vue-treeselect__option--highlight,
.vue-treeselect__option--selected {
  color: @primary-color;
  background-color: @primary-color_5 !important;
}
.vue-treeselect__checkbox--checked,
.vue-treeselect__checkbox--indeterminate {
  border-color: @primary-color !important;
  background: @primary-color !important;
}
.vue-treeselect__label-container:hover {
  .vue-treeselect__checkbox--checked,
  .vue-treeselect__checkbox--indeterminate {
    border-color: @primary-color !important;
    background: @primary-color !important;
  }
}
.vue-treeselect__multi-value-item {
  background: @primary-color_5 !important;
  color: @primary-color !important;
}
.vue-treeselect__value-remove {
  color: @primary-color !important;
}
.vue-treeselect__label-container:hover .vue-treeselect__checkbox--unchecked {
  border-color: @primary-color !important;
}

//表格样式
//非斑马纹
.ops-unstripe-table {
  .vxe-table--border-line {
    // border: none !important;
  }
  .vxe-table--header-wrapper {
    background-color: @primary-color_6 !important;
  }
  .vxe-header--row .vxe-header--column:hover {
    background: @primary-color_3;
  }
}
.ops-unstripe-table.vxe-table--render-default.border--full {
  .vxe-table--main-wrapper,
  .vxe-table--fixed-wrapper .vxe-table--fixed-right-wrapper {
    .vxe-body--column:last-child,
    .vxe-footer--column:last-child,
    .vxe-header--column:last-child {
      background-size: 0px 100%, 100% 1px !important;
    }
  }
}

//表格样式
//斑马纹
.ops-stripe-table {
  .vxe-table--border-line {
    border: none !important;
  }
  .vxe-table--header-wrapper {
    background-color: @primary-color_6 !important;
  }
  // .vxe-table--header-wrapper.body--wrapper {
  //   border-radius: 8px !important;
  // }
  // .vxe-table--fixed-wrapper {
  //   .vxe-table--fixed-right-wrapper {
  //     .vxe-table--header-wrapper.fixed-right--wrapper {
  //       border-top-right-radius: 8px !important;
  //       border-bottom-right-radius: 8px !important;
  //     }
  //   }
  // }
  .vxe-body--row {
    &.row--stripe {
      background-color: @primary-color_6 !important;
      border-top: 1px solid rgba(0, 0, 0, 0.1) !important;
      border-radius: 5px !important;
      > td {
        background-image: none !important;
      }
    }
    &.row-error {
      background-color: rgba(255, 0, 0, 0.26) !important;
    }
  }
  .vxe-header--row .vxe-header--column:hover {
    background: @primary-color_3;
  }
}

.ops-input {
  .ant-input,
  .ant-time-picker-input {
    background-color: @primary-color_7;
    border: none;
  }

  &,
  .ant-input,
  .ant-time-picker-input {
    &:not([disabled]) {
      &:hover {
        background-color: @primary-color_5;
      }
  
      &:focus {
        background-color: @primary-color_7;
        border: solid 1px @primary-color;
      }
    }
  }
}
.ops-input.ant-input {
  background-color: @primary-color_7;
  border: none;
}
.ops-input.ant-input[disabled] {
  background-color: #f9f9f9;
  color: #333;
  cursor: default;
}
.ant-input:focus,
.ant-input-number:focus,
.ant-input-number-focused {
  box-shadow: none;
}

.ant-pagination-options-quick-jumper input:focus {
  box-shadow: none;
}

// vxe-table checkbox 选中 highlight
.vxe-table--render-default .vxe-body--row.row--checked,
.vxe-table--render-default .vxe-body--row.row--radio {
  background: @primary-color_5 !important;
}
.vxe-table--render-default .vxe-body--row.row--hover.row--checked,
.vxe-table--render-default .vxe-body--row.row--hover.row--radio {
  background: @primary-color_4 !important;
}

// vxe-table 颜色
.is--checked.vxe-checkbox,
.is--checked.vxe-checkbox .vxe-checkbox--icon,
.is--checked.vxe-custom--option,
.is--checked.vxe-custom--option .vxe-checkbox--icon,
.is--checked.vxe-export--panel-column-option,
.is--checked.vxe-export--panel-column-option .vxe-checkbox--icon,
.is--checked.vxe-table--filter-option,
.is--checked.vxe-table--filter-option .vxe-checkbox--icon,
.is--indeterminate.vxe-checkbox,
.is--indeterminate.vxe-checkbox .vxe-checkbox--icon,
.is--indeterminate.vxe-custom--option,
.is--indeterminate.vxe-custom--option .vxe-checkbox--icon,
.is--indeterminate.vxe-export--panel-column-option,
.is--indeterminate.vxe-export--panel-column-option .vxe-checkbox--icon,
.is--indeterminate.vxe-table--filter-option,
.is--indeterminate.vxe-table--filter-option .vxe-checkbox--icon,
.vxe-table--render-default .is--checked.vxe-cell--checkbox,
.vxe-table--render-default .is--checked.vxe-cell--checkbox .vxe-checkbox--icon,
.vxe-table--render-default .is--indeterminate.vxe-cell--checkbox,
.vxe-table--render-default .is--indeterminate.vxe-cell--checkbox .vxe-checkbox--icon,
.vxe-checkbox:not(.is--disabled):hover .vxe-checkbox--icon,
.vxe-custom--option:not(.is--disabled):hover .vxe-checkbox--icon,
.vxe-export--panel-column-option:not(.is--disabled):hover .vxe-checkbox--icon,
.vxe-table--filter-option:not(.is--disabled):hover .vxe-checkbox--icon,
.vxe-table--render-default .vxe-cell--checkbox:not(.is--disabled):hover .vxe-checkbox--icon,
.is--filter-active .vxe-cell--filter .vxe-filter--btn,
.vxe-table .vxe-sort--asc-btn.sort--active,
.vxe-table .vxe-sort--desc-btn.sort--active,
.vxe-select-option.is--selected,
.vxe-loading > .vxe-loading--chunk,
.vxe-loading > .vxe-loading--warpper,
.vxe-pager .vxe-pager--jump-next:not(.is--disabled).is--active,
.vxe-pager .vxe-pager--jump-next:not(.is--disabled):focus,
.vxe-pager .vxe-pager--jump-prev:not(.is--disabled).is--active,
.vxe-pager .vxe-pager--jump-prev:not(.is--disabled):focus,
.vxe-pager .vxe-pager--next-btn:not(.is--disabled).is--active,
.vxe-pager .vxe-pager--next-btn:not(.is--disabled):focus,
.vxe-pager .vxe-pager--num-btn:not(.is--disabled).is--active,
.vxe-pager .vxe-pager--num-btn:not(.is--disabled):focus,
.vxe-pager .vxe-pager--prev-btn:not(.is--disabled).is--active,
.vxe-pager .vxe-pager--prev-btn:not(.is--disabled):focus,
.vxe-button.type--text:not(.is--disabled):hover,
.vxe-table--filter-footer > button:hover {
  color: @primary-color !important;
}

.vxe-cell .vxe-default-input:focus,
.vxe-cell .vxe-default-select:focus,
.vxe-cell .vxe-default-textarea:focus,
.vxe-table--filter-wrapper .vxe-default-input:focus,
.vxe-table--filter-wrapper .vxe-default-select:focus,
.vxe-table--filter-wrapper .vxe-default-textarea:focus,
.vxe-select.is--active:not(.is--filter) > .vxe-input .vxe-input--inner,
.vxe-input:not(.is--disabled).is--active .vxe-input--inner {
  border-color: @primary-color !important;
}

.vxe-table--render-default .vxe-body--row.row--hover,
.vxe-table--render-default .vxe-body--row.row--hover.row--stripe {
  background-color: @primary-color_7;
}

.vxe-select-option:not(.is--disabled).is--hover {
  background-color: @primary-color_5;
}

//批量操作
.ops-list-batch-action {
  display: inline-block;
  background-color: @primary-color_6;
  font-size: 12px;
  color: @text-color_3;
  > span {
    display: inline-block;
    padding: 7px 8px;
    cursor: pointer;
    &:hover {
      color: @primary-color;
    }
  }
  > span:last-child {
    color: @primary-color;
    cursor: default;
  }
}

// card tab
.ops-tab.ant-tabs.ant-tabs-card {
  .ant-tabs-card-bar {
    margin: 0;
    border-bottom: none;
    .ant-tabs-nav-container {
      .ant-tabs-tab {
        border: none;
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
        background: @primary-color_6;
        margin-right: 5px;
      }
      .ant-tabs-tab-active {
        background: #fff;
      }
    }
  }
}

// line tab
.ops-tab.ant-tabs {
  .ant-tabs-bar {
    margin-bottom: 20px;
    border-bottom: none;
    .ant-tabs-nav.ant-tabs-nav-animated,
    .ant-tabs-nav.ant-tabs-nav-animated > div:first-child {
      border-bottom: 1px solid @border-color-base;
    }
    .ant-tabs-tab {
      color: @text-color_2;
    }
    .ant-tabs-tab-active {
      color: @primary-color;
    }
  }
}

// button
.ops-button-ghost {
  position: relative;
  overflow: hidden;

  &.ant-btn-background-ghost.ant-btn-primary {
    border-color: @primary-color_8;
    background-color: @primary-color_5 !important;
    box-shadow: none;

    .btn-wave-hover(@primary-color_4);

    &[disabled] {
      border-color: #d9d9d9;
      background-color: @primary-color_7!important;
    }
  
    &:not([disabled]):hover {
      color: #3F75FF;
      border-color: transparent;
    }
  }
}

.ant-btn-primary:not(.ant-btn-background-ghost) {
  .btn-wave-hover(@primary-color_9);
}

// button
.ant-btn.ant-btn-icon-only {
  &:hover > i {
    color: @primary-color;
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  }
  > i {
    color: #d9d9d9;
  }
}

// select
.ops-select {
  &.white {
    .ant-select-selection {
      background-color: white;
      border-color: white;
      height: 30px;
      border-radius: 4px;
    }
  }
  &.bold {
    .ant-select-selection {
      .ant-select-selection-selected-value {
        font-weight: bold;
        color: #000000;
      }
    }
  }
}

.ops-select-bg {
  .ant-select-selection {
    background-color: @primary-color_7;
  }

  &:hover {
    .ant-select-selection {
      background-color: @primary-color_5;
    }
  }

  .ant-select-focused {
    .ant-select-selection {
      background-color: @primary-color_7;
      border: solid 1px @primary-color;
    }
  }
}

.ant-select-selection {
  box-shadow: none !important;
}

// date picker
.ant-calendar-today .ant-calendar-date {
  color: @primary-color;
  border-color: @primary-color;
}

//dropdown
.ops-dropdown {
  .ant-dropdown-menu-item:hover,
  .ant-dropdown-menu-submenu-title:hover {
    background-color: @primary-color_5;
    color: @primary-color;
  }
}

// radio
.ant-radio-input:focus + .ant-radio-inner {
  box-shadow: none;
}

//modal
.ant-modal-content {
  .ant-modal-close-x {
    width: 56px;
    height: 56px;
    line-height: 56px;
  }

  .ant-modal-header {
    border-bottom: none;
    padding: 22px 22px;

    .ant-modal-title {
      padding-left: 10px;
      border-left: 4px solid @primary-color;
    }
  }

  .ant-modal-footer {
    border-top: none;
    padding: 16px 22px 22px;
  }
}

.ops-tooltip {
  .ant-tooltip-content {
    width: 276px;
  }
  .ant-tooltip-inner {
    background-color: @primary-color_3;
    border-radius: '4px';
    display: flex;
    align-items: flex-start;
    .title {
      font-style: normal;
      font-weight: 400;
      font-size: 12px;
      line-height: 15px;
      color: @layout-header-font-color;
    }
  }
  .ant-tooltip-arrow::before {
    width: 7px;
    height: 7px;
    background-color: @primary-color_3;
  }
}

.ops-crontab.el-tabs--card {
  box-shadow: 0px 8px 16px rgba(160, 181, 235, 0.25);
  border-radius: 8px;

  .el-tabs__header {
    border-bottom: none;
    background-color: @primary-color_5;
    border-radius: 8px 8px 0px 0px;
  }

  .el-tabs__header .el-tabs__item {
    border: none;
    color: #9094a6;
  }

  .el-tabs__header .el-tabs__item.is-active {
    background-color: white;
    color: @primary-color;
  }
  .el-tabs__header .el-tabs__item:first-child.is-active {
    border-top-left-radius: 8px;
  }

  .el-tabs__header .el-tabs__nav {
    border: none;
  }

  .el-radio__input.is-checked .el-radio__inner {
    background-color: @primary-color;
    border-color: @primary-color;
  }

  .el-radio__input.is-checked + .el-radio__label {
    color: @primary-color;
  }

  .el-tab-pane {
    padding: 0px 12px;
  }

  .el-input-number--small .el-input-number__decrease,
  .el-input-number--small .el-input-number__increase {
    width: 28px;

    &:hover {
      color: @primary-color; 

      &:not(.is-disabled)~.el-input .el-input__inner:not(.is-disabled) {
        border-color: @primary-color;
      }
    }
  }

  .el-input-number--small {
    width: 94px;
  }

  .el-input-number--small .el-input__inner {
    padding: 0px 30px;
  }

  .el-radio__label {
    padding-left: 8px;
    font-size: 13px;
  }
}

.ops-form {
  .ant-row.ant-form-item {
    margin: 5px 0px;
  }
  .ant-select-selection--multiple > ul > li,
  .ant-select-selection--multiple .ant-select-selection__rendered > ul > li {
    max-width: 100px;
  }
}

// a-drop-down
.ant-dropdown-menu-item-active {
  color: @primary-color;
}

.ant-tag {
  &.ops-perm-tag {
    border: none;
    background-color: @primary-color_5;
    color: @primary-color;
  }
}

.vue-grid-item {
  &.ops-grid-item {
    .vue-resizable-handle {
      width: 10px;
      height: 10px;
    }
  }
}

// json editor
.jsoneditor-vue {
  div.jsoneditor {
    border: none;
  }
  div.jsoneditor-menu {
    border-bottom-color: @primary-color;
  }
}

.ant-tree li .ant-tree-node-content-wrapper.ant-tree-node-selected,
.ant-tree li .ant-tree-node-content-wrapper:hover {
  background-color: @primary-color_3;
}

.ant-pagination-options-size-changer.ant-select {
  margin-right: 0;
}

.ant-form-explain{
  font-size: 12px;
}

.ant-message-info .anticon,
.ant-message-loading .anticon {
  color: @primary-color_9;
}

.primary-color {
  color: @primary-color !important;
}

.primary-bg-color {
  background-color: @primary-color;
}
