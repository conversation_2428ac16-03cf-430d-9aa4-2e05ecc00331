@border-radius-base: 2px; // 组件/浮层圆角
@border-radius-box: 4px; // big box radius

// primary color
@primary-color: #2f54eb;
@primary-color_2: #7f97fa;
@primary-color_3: #ebeff8;
@primary-color_4: #e1efff;
@primary-color_5: #f0f5ff;
@primary-color_6: #f9fbff;
@primary-color_7: #f7f8fa;
@primary-color_8: #b1c9ff;
@primary-color_9: #3F75FF;

@link-color: @primary-color;

// Neutral color
@text-color_1: #1d2129;
@text-color_2: #4e5969;
@text-color_3: #86909c;
@text-color_4: #a5a9bc;
@text-color_5: #cacdd9;
@text-color_6: #e4e7ed;
@text-color_7: #f0f1f5;

@func-color_1: #fd4c6a;
@func-color_2: #ff7d00;
@func-color_3: #00b42a;
@func-color_4: #fad337;

@border-color-base: @text-color_6; // 边框色

@scrollbar-color: rgba(47, 122, 235, 0.2);

@layout-content-background: @primary-color_7;
@layout-header-background: #fff;
@layout-header-height: 40px;
@layout-header-line-height: 32px;
@layout-header-icon-height: 34px;
@layout-header-font-color: #020000;
@layout-header-font-selected-color: @primary-color;

@layout-sidebar-color: #ffffff; //bg
@layout-sidebar-sub-color: @primary-color_7; //bg
@layout-sidebar-selected-color: @primary-color_5; //selected bg
@layout-sidebar-arrow-color: @text-color_4;
@layout-sidebar-font-color: @text-color_2;
@layout-sidebar-icon-color: @text-color_4;
@layout-sidebar-selected-font-color: @primary-color;
@layout-sidebar-disabled-font-color: @text-color_4;


.ops_display_wrapper(@backgroundColor:@primary-color_5) {
  cursor: pointer;
  padding: 5px 8px;
  background-color: @backgroundColor;
  border-radius: @border-radius-box;
  height: 30px;
  color: rgba(0, 0, 0, 0.6);
  white-space: nowrap;
}

.ops_popover_item() {
  cursor: pointer;
  padding: 5px 10px;
  &:hover {
    background-color: @primary-color_3;
  }
}
.ops_popover_item_selected() {
  background-color: @primary-color_3;
  color: @primary-color;
}

.btn-wave-hover(
  @hoverBgColor,
  @bgZIndex: 0,
  @duration: 0.3s
) {
  position: relative;
  overflow: hidden;

  & > * {
    position: relative;
    z-index: 1;
  }

  &:not([disabled])::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: @bgZIndex;
    width: 100%;
    aspect-ratio: 1/1;
    border-radius: 50%;

    background-color: @hoverBgColor;
    -webkit-transform: scale(0) translate(-50%, -50%);
    -ms-transform: scale(0) translate(-50%, -50%);
    transform: scale(0) translate(-50%, -50%);
    transform-origin: left top;

    -webkit-transition: -webkit-transform @duration ease-out;
    transition: -webkit-transform @duration ease-out;
    transition: transform @duration ease-out;
    transition: transform @duration ease-out, -webkit-transform @duration ease-out;
  }

  &:not([disabled]):hover {
    &::after {
      -webkit-transform: scale(2) translate(-50%, -50%);
      -ms-transform: scale(2) translate(-50%, -50%);
      transform: scale(2) translate(-50%, -50%);
    }
  }
}
