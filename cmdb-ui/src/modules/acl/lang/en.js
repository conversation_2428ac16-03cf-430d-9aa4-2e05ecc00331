const acl_en = {
    date: 'Date',
    operator: 'Operator',
    resource: 'Resource',
    resourceType: 'Resource Type',
    addResourceType: 'Add Resource Type',
    app: 'App',
    operateTime: 'Operate Time',
    permission: 'Permission',
    permission_placeholder: 'please select permission',
    permissionList: 'Permission List',
    summaryPermissions: 'Summary of permissions',
    source: 'Source',
    username: '<PERSON><PERSON><PERSON>',
    username_placeholder: 'please input username',
    userList: 'User List',
    groupUser: 'Group User',
    addUser: 'Add User',
    subordinateUsers: 'Subordinate Users',
    nickname: 'Nickname',
    nickname_placeholder: 'please input nickname',
    password: 'Password',
    password_placeholder: 'please input password',
    department: 'Department',
    group: 'Group',
    email: 'Email',
    email_placeholder: 'please input email',
    mobile: 'Mobile',
    isBlock: 'Is Block',
    block: 'Block',
    joined_at: 'Joined At',
    role: 'Role',
    role_placeholder1: 'please input role',
    role_placeholder2: 'please select role',
    role_placeholder3: 'please select a role name, multiple choices are allowed',
    allRole: 'All Roles',
    visualRole: 'Virtual Role',
    addVisualRole: 'Add Virtual Role',
    inheritedFrom: 'Inherited from',
    heir: 'Inherit Roles',
    permissionChange: 'Permissions',
    roleChange: 'Roles',
    resourceChange: 'Resources',
    resourceTypeChange: 'Resource Type',
    trigger: 'Triggers',
    triggerNameInput: 'Please enter trigger name',
    triggerChange: 'Triggers',
    roleManage: 'Roles',
    userManage: 'Users',
    appManage: 'Applications',
    resourceManage: 'Resources',
    history: 'Audits',
    userSecret: 'Secrets',
    none: 'none',
    danger: 'Dangerous',
    confirmDeleteApp: 'Are you sure you want to delete this app?',
    revoke: 'Revoke',
    convenient: 'Quick Grant',
    group2: 'Group',
    groupName: 'Group Name',
    resourceName: 'Resource Name',
    creator: 'Creator',
    member: 'Members',
    viewAuth: 'view Auth',
    addTypeTips: 'There is no type information yet, please add the resource type first!',
    addResource: 'Add Resource',
    resourceList: 'Resource List',
    confirmResetSecret: 'Are you sure you want to reset the user secrets?',
    addTrigger: 'Add Trigger',
    deleteTrigger: 'Delete Trigger',
    applyTrigger: 'Apply Trigger',
    cancelTrigger: 'Cancel Trigger',
    enable: 'Enable',
    disable: 'Disable',
    viewMatchResult: 'View regular matching results',
    confirmDeleteTrigger: 'Are you sure you want to delete this trigger?',
    ruleApply: 'Apply',
    triggerTip1: 'Are you sure you want to apply this trigger?',
    triggerTip2: 'Cancel applying this trigger?',
    appNameInput: 'Please enter an application name',
    descInput: 'Please enter a description',
    addApp: 'Add',
    updateApp: 'Update',
    cancel: 'Cancel',
    typeName: 'Name',
    typeNameInput: 'Please enter a type name',
    resourceNameInput: 'Please enter resource name',
    pressEnter: 'Press Enter to confirm filtering',
    groupMember: 'Group Members:',
    isGroup: 'Group?',
    errorTips: 'Error message',
    roleList: 'Role List',
    virtual: 'Virtual',
    resourceBatchTips: 'Please enter the resource name, separated by newlines',
    memberManage: 'Members: ',
    newResource: 'New Resource: ',
    deleteResource: 'Delete Resource: ',
    deleteResourceType: 'Delete Resource Type: ',
    noChange: 'No change',
    batchOperate: 'Batch Operations',
    batchGrant: 'Batch Grant',
    batchRevoke: 'Batch Revoke',
    editPerm: 'Add authorization: ',
    permInput: 'Please enter permission name',
    resourceTypeName: 'Resource Type Name',
    selectedParents: 'Optionally inherit roles',
    isAppAdmin: 'is app admin',
    addRole: 'Add Role',
    roleRelation: 'Role Relation',
    roleRelationAdd: 'Add Role Relation',
    roleRelationDelete: 'Delete Role Relation',
    role2: 'Role',
    admin: 'Admin',
    involvingRP: 'Involving resources and permissions',
    startAt: 'Start Time',
    endAt: 'End Time',
    triggerTips1: 'Priority regular pattern (secondary wildcard)',
    pleaseSelectType: 'Please select resource type',
    apply: 'Apply',
    mobileTips: 'Please enter the correct phone number',
    remove: 'Remove',
    deleteUserConfirm: 'Are you sure you want to remove this user?',
    copyResource: 'Copy resource name'
}
export default acl_en
