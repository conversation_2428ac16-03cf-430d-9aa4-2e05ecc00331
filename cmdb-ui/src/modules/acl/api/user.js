/* eslint-disable */
import { axios } from '@/utils/request'

const urlPrefix = '/v1/acl'

export function currentUser() {
  return axios({
    url: urlPrefix + `/users/info`,
    method: 'GET'
  })
}

export function currentUserOTP() {
  return axios({
    url: urlPrefix + `/users/secret_otp`,
    method: 'GET'
  })
}

export function getOnDutyUser() {
  return axios({
    url: urlPrefix + '/users/employee',
    method: 'GET',
  })
}

export function searchUser(params) {
  return axios({
    url: urlPrefix + `/users`,
    method: 'GET',
    params: params
  })
}

export function addUser(params) {
  return axios({
    url: urlPrefix + '/users',
    method: 'POST',
    data: params
  })
}

export function updateUserById(id, params) {
  return axios({
    url: urlPrefix + `/users/${id}`,
    method: 'PUT',
    data: params
  })
}

export function deleteUserById(uid, params) {
  return axios({
    url: urlPrefix + `/users/${uid}`,
    method: 'DELETE',
    data: {eid : params}
  })
}
