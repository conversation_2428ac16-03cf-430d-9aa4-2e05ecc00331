<template>
  <a-card :bordered="false">
    <div class="action-btn">
      <a-button @click="handleCreate" type="primary" style="margin-bottom: 15px;">{{ $t('cmdb.measurement_unit.addMeasurementUnit') }}</a-button>
    </div>
    <vxe-table
      ref="measurementUnitTable"
      :data="tableData"
      keep-source
      highlight-hover-row
      :edit-config="{ trigger: 'manual', mode: 'row' }"
      @edit-closed="handleEditClose"
      stripe
      class="ops-stripe-table"
      bordered
    >
      <vxe-table-column
        field="name"
        :title="$t('cmdb.measurement_unit.measurementUnit')"
        :edit-render="{ name: 'input', attrs: { type: 'text' }, events: { keyup: customCloseEdit } }"
      ></vxe-table-column>
      <vxe-table-column
        field="unit_num"
        :title="$t('cmdb.measurement_unit.unit_num')"
        :edit-render="{ name: 'input', attrs: { type: 'text' }, events: { keyup: customCloseEdit } }"
      ></vxe-table-column>
      <vxe-table-column field="updateTime" :title="$t('updated_at')">
        <template #default="{row}">
          {{ row.updated_at || row.created_at }}
        </template>
      </vxe-table-column>
      <vxe-table-column field="operation" :title="$t('operation')" align="center">
        <template #default="{row}">
          <template>
            <a><a-icon type="edit" @click="handleEdit(row)"/></a>
            <a-divider type="vertical" />
            <a-popconfirm :title="$t('confirmDelete')" @confirm="handleDelete(row)" :okText="$t('yes')" :cancelText="$t('no')">
              <a :style="{ color: 'red' }"><a-icon type="delete"/></a>
            </a-popconfirm>
          </template>
        </template>
      </vxe-table-column>
    </vxe-table>
  </a-card>
</template>

<script>
import moment from 'moment'
import {
  getMeasurementUnit,
  deleteMeasurementUnit,
  addMeasurementUnit,
  updateMeasurementUnit,
} from '@/modules/cmdb/api/measurementUnit'

export default {
  name: 'MeasurementUnit',
  components: {},
  data() {
    return {
      tableData: [],
    }
  },

  computed: {},
  mounted() {
    this.loadData()
  },

  methods: {
    loadData() {
      getMeasurementUnit().then((res) => {
        this.tableData = res
      })
    },
    handleEdit(row) {
      const $table = this.$refs.measurementUnitTable
      $table.setActiveRow(row)
    },
    handleCreate() {
      const $table = this.$refs.measurementUnitTable
      const newRow = {
        name: '',
        unit_num: '',
        created_at: moment().format('YYYY-MM-DD hh:mm:ss'),
      }
      $table.insert(newRow).then(({ row }) => $table.setActiveRow(row))
    },
    handleEditClose({ row, rowIndex, column }) {
      const $table = this.$refs.measurementUnitTable
      if (row.id) {
        if (row.name && row.unit_num && $table.isUpdateByRow(row)) {
          this.updateMeasurementUnit(row.id, { name: row.name, unit_num: row.unit_num })
        } else {
          $table.revertData(row)
        }
      } else {
        if (row.name) {
          this.createRelationType({ name: row.name, unit_num: row.unit_num })
        } else {
          this.loadData()
        }
      }
    },
    updateMeasurementUnit(id, data) {
      updateMeasurementUnit(id, data).then((res) => {
        this.$message.success(this.$t('updateSuccess'))
        this.loadData()
      })
    },

    createRelationType(data) {
      addMeasurementUnit(data).then((res) => {
        this.$message.success(this.$t('addSuccess'))
        this.loadData()
      })
    },
    handleDelete(record) {
      this.deleteMeasurementUnit(record.id)
    },
    deleteMeasurementUnit(id) {
      deleteMeasurementUnit(id).then((res) => {
        this.$message.success(this.$t('deleteSuccess'))
        this.loadData()
      })
    },
    customCloseEdit(value, $event) {
      // enter, close edit
      if ($event.keyCode === 13) {
        const $table = this.$refs.measurementUnitTable
        $table.clearActived()
      }
    },
  },
  watch: {},
}
</script>

<style lang="less" scoped></style>
