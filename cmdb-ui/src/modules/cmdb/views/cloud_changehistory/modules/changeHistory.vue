<template>
  <div class="acl-resource-types">
    <div class="acl-resource-types-header">
      <a-input-search
        class="ops-input"
        :style="{ display: 'inline', marginLeft: '10px', width: '200px' }"
        :placeholder="`${$t('search')} | ${$t('cmdb.history.cloudHistoryID')}`"
        v-model="searchName"
        allowClear
        @search="
          () => {
            this.tablePage.currentPage = 1
            this.searchData()
          }
        "
      ></a-input-search>
    </div>
    <a-spin :spinning="loading">
      <vxe-table
        :striped="true"
        :size="'small'"
        :data="groups"
        :height="windowHeight - 200"
        :highlight-on-hover="true"
        border
        show-overflow="tooltip"
        show-header-overflow="tooltip"
        class="ops-unstripe-table"
        resizable
        :scroll-y="{ enabled: false }"
      >
        <!-- 1 -->
        <vxe-table-column
          field="id"
          :title="$t('cmdb.history.cloudHistoryID')"
          :min-width="30"
          fixed="left"
          show-overflow
        ></vxe-table-column>
        <vxe-table-column field="created_at" :title="$t('opreateTime')" :min-width="30"></vxe-table-column>
        <vxe-table-column field="type_id" :title="$t('cmdb.ciType.ciType')" :min-width="30"></vxe-table-column>
        <vxe-table-column field="user" :title="$t('cmdb.history.user')" :min-width="30"></vxe-table-column>
        <!-- 2 -->
        <vxe-table-column field="extractedData" :title="$t('cmdb.history.old')" :min-width="400"></vxe-table-column>
        <!-- 4 -->
        <vxe-table-column field="business_is_active" :title="$t('operation')" :width="100" fixed="right">
          <template #default="{ row }">
            <!-- 切换按钮 -->
            <a @click="handleactive(row)" style="cursor: pointer;">
              <!-- 根据business_is_active的值显示不同的图标和颜色 -->
              <a-icon :type="row.business_is_active ? 'check' : 'stop'" :style="{ color: row.business_is_active ? 'green' : 'grey' }"/>
            </a>
          </template>
        </vxe-table-column>
      </vxe-table>
      <a-pagination
        size="small"
        show-size-changer
        show-quick-jumper
        :current="tablePage.currentPage"
        :total="tablePage.total"
        :show-total="(total, range) => `当前展示 ${range[0]}-${range[1]} 条数据, 共 ${total} 条`"
        :page-size="tablePage.pageSize"
        :default-current="1"
        :page-size-options="pageSizeOptions"
        @change="pageOrSizeChange"
        @showSizeChange="pageOrSizeChange"
        :style="{ marginTop: '10px', textAlign: 'right' }"
      />
    </a-spin>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { getCIHistoryTable, updateCloudHistoryById } from '@/modules/cmdb/api/cloudHistory'
import { getCITypes } from '@/modules/cmdb/api/CIType'

export default {
  name: 'ChangeHistory',
  components: {
  },
  data() {
    return {
      loading: false,
      groups: [],
      id2perms: {},
      pageSizeOptions: ['20', '50', '100', '200'],
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 50,
      },
      searchName: '',
      queryParams: {
        page: 1,
        page_size: 50,
        business_type: 1,
        record_id: '',
      },
    }
  },

  beforeCreate() {},
  mounted() {
    this.getTable(this.queryParams)
  },
  computed: {
    ...mapState({
      windowHeight: (state) => state.windowHeight,
    }),
    operateTypeMap() {
      return new Map([
        ['0', this.$t('new')],
        ['1', this.$t('delete')],
        ['2', this.$t('update')],
      ])
    },
    BusinessTypeMap() {
      return new Map([
        ['0', this.$t('normal')],
        ['1', this.$t('billing')],
        ['2', this.$t('nonbilling')],
      ])
    },
  },
  watch: {
    '$route.name': function(newName, oldName) {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 50,
      }
      this.searchData()
    },
    searchName: {
      immediate: true,
      handler(newVal, oldVal) {
        if (!newVal) {
          this.tablePage.currentPage = 1
          this.searchData()
        }
      },
    },
  },
  async created() {
    await Promise.all([this.getTypes()])
  },
  inject: ['reload'],

  methods: {
    async getTypes() {
      const res = await getCITypes()
      const typesArr = []
      const typesMap = new Map()
      res.ci_types.forEach((item) => {
        const tempObj = {}
        tempObj[item.alias] = item.id
        if (item.alias) {
          typesArr.push(tempObj)
          typesMap.set(item.id, item.alias)
        }
      })
      this.typeList = typesMap
    },
    async getTable(queryParams) {
      try {
        const res = await getCIHistoryTable(queryParams)
        const tempArr = []
        res.records.forEach((item) => {
          item[0].type_id = this.handleTypeId(item[0].type_id)
          const business_is_active = item[1][0].business_is_active
          const extractedData = item[1].map(({ attr_alias, new: newValue, old }) => ({
            attr_alias,
            new: newValue,
            old
          }))
          const extractedDataString = JSON.stringify(extractedData)
          const tempObj = Object.assign({}, item[0], { extractedData: extractedDataString, business_is_active: business_is_active })
          tempArr.push(tempObj)
        })
        this.groups = tempArr
        this.tablePage.total = res.total
      } finally {
        this.loading = false
      }
    },
    searchData() {
      const { currentPage, pageSize } = this.tablePage
      this.loading = true
      const param = {
        page_size: pageSize,
        page: currentPage,
        business_type: 1,
        record_id: this.searchName,
      }
      this.getTable(param)
    },
    handleTypeId(type_id) {
      return this.typeList.get(type_id) ? this.typeList.get(type_id) : type_id
    },
    handleOperateType(operate_type) {
      return this.operateTypeMap.get(operate_type)
    },
    handleBusinessType(operate_type) {
      return this.BusinessTypeMap.get(operate_type)
    },
    handleEdit(record) {
      var perms = []
      var permList = this.id2perms[record.id]
      if (permList) {
        for (var i = 0; i < permList.length; i++) {
          perms.push(permList[i].name)
        }
      }
      record.perms = perms
      this.$refs.resourceTypeForm.handleEdit(record)
    },
    handleactive(record) {
      this.updateCloudHistory(record.id)
      this.$set(record, 'business_is_active', !record.business_is_active)
    },
    handleOk() {
      this.searchData()
    },
    handleCreate() {
      this.$refs.resourceTypeForm.handleCreate()
    },
    updateCloudHistory(id) {
      updateCloudHistoryById(id).then((res) => {
        this.$message.success(this.$t('updateSuccess'))
      })
    },
    pageOrSizeChange(currentPage, pageSize) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.searchData()
    },
  },
}
</script>

<style lang="less" scoped>

.acl-resource-types {
  border-radius: @border-radius-box;
  background-color: #fff;
  height: calc(100vh - 64px);
  margin-bottom: -24px;
  padding: 24px;
  .acl-resource-types-header {
    width: 100%;
    display: inline-flex;
    margin-bottom: 15px;
    align-items: center;
  }
}
</style>
