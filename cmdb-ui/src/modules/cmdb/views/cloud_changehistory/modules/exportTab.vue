<template>
  <div
    class="resource-search"
    id="resource_search"
    :style="{ height: fromCronJob ? `${windowHeight - 48}px` : `${windowHeight - 64}px` }"
  >
    <div class="button-container">
      <a-button
        v-if="!fromCronJob"
        icon="download"
        type="primary"
        class="ops-button-ghost"
        ghost
        @click="handleExport"
      >{{ $t('download') }}</a-button
      >
    </div>
    <div v-if="fromCronJob" class="resource-search-tip">
      <div class="resource-search-tip-item">{{ $t('cmdb.ciType.resourceSearchTip1') }}</div>
      <div class="resource-search-tip-item">{{ $t('cmdb.ciType.resourceSearchTip2') }}</div>
      <div class="resource-search-tip-item">{{ $t('cmdb.ciType.resourceSearchTip3') }}</div>
    </div>
    <CloudSearchForm
      ref="search"
      :type="type"
      :typeId="typeId"
      @refresh="handleSearch"
      :preferenceAttrList="allAttributesList"
      @updateAllAttributesList="updateAllAttributesList"
      @copyExpression="copyExpression"
    >
      <PreferenceSearch
        v-if="!fromCronJob"
        ref="preferenceSearch"
        @getQAndSort="getQAndSort"
        @setParamsFromPreferenceSearch="setParamsFromPreferenceSearch"
      />
    </CloudSearchForm>
    <vxe-table
      :id="`cmdb-resource`"
      border
      keep-source
      show-overflow
      resizable
      ref="xTable"
      size="small"
      :loading="loading"
      :height="fromCronJob ? windowHeight - 280 : windowHeight - 240"
      show-header-overflow
      highlight-hover-row
      :data="instanceList"
      :sort-config="{ remote: true, trigger: 'cell' }"
      @sort-change="handleSortCol"
      :row-key="true"
      :column-key="true"
      :cell-style="getCellStyle"
      :scroll-y="{ enabled: true, gt: 20 }"
      :scroll-x="{ enabled: true, gt: 0 }"
      :export-config="{
        isColgroup: true,
        type: 'xlsx',
        types: ['xlsx', 'csv', 'html', 'xml', 'txt'],
        mode: 'current',
        modes: ['current'],
        isFooter: false,
        isHeader: true,
        isColgroup: true,
      }"
      class="ops-unstripe-table"
      :custom-config="{ storage: true }"
    >
      <vxe-column
        v-if="instanceList.length"
        :title="$t('cmdb.ciType.ciType')"
        field="ci_type_alias"
        :width="100"
        fixed="left"
      ></vxe-column>
      <vxe-colgroup v-for="colGroup in columnsGroup" :key="colGroup.value" :title="colGroup.label">
        <template #header>
          <span :style="{ display: 'inline-flex', alignItems: 'center' }">
            {{ colGroup.label }}
            <EditAttrsPopover
              :style="{ borderLeft: 'none', width: '30px', height: '38px', cursor: 'pointer' }"
              v-if="colGroup.isCiType"
              :typeId="Number(colGroup.id.split('-')[1])"
              @refresh="loadInstance"
            />
          </span>
        </template>
        <vxe-column
          v-for="(col, index) in colGroup.children"
          :key="`${col.field}_${index}`"
          :title="col.title"
          :field="col.field"
          :width="col.width"
          :minWidth="100"
          :cell-type="col.value_type === '2' ? 'string' : 'auto'"
        >
          <template v-if="col.value_type === '6' || col.is_link || col.is_password || col.is_choice" #default="{row}">
            <span v-if="col.value_type === '6' && row[col.field]">{{ JSON.stringify(row[col.field]) }}</span>
            <template v-else-if="col.is_link && row[col.field]">
              <a
                v-for="(item, linkIndex) in (col.is_list ? row[col.field] : [row[col.field]])"
                :key="linkIndex"
                :href="
                  item.startsWith('http') || item.startsWith('https')
                    ? `${item}`
                    : `http://${item}`
                "
                target="_blank"
              >
                {{ item }}
              </a>
            </template>
            <PasswordField
              v-else-if="col.is_password && row[col.field]"
              :ci_id="row._id"
              :attr_id="col.attr_id"
            ></PasswordField>
            <template v-else-if="col.is_choice">
              <template v-if="col.is_list">
                <span
                  v-for="value in row[col.field]"
                  :key="value"
                  :style="{
                    borderRadius: '4px',
                    padding: '1px 5px',
                    margin: '2px',
                    ...getChoiceValueStyle(col, value),
                  }"
                ><ops-icon
                  :style="{ color: getChoiceValueIcon(col, value).color }"
                  :type="getChoiceValueIcon(col, value).name"
                />{{ value }}</span
                >
              </template>
              <span
                v-else
                :style="{
                  borderRadius: '4px',
                  padding: '1px 5px',
                  margin: '2px 0',
                  ...getChoiceValueStyle(col, row[col.field]),
                }"
              >
                <ops-icon
                  :style="{ color: getChoiceValueIcon(col, row[col.field]).color }"
                  :type="getChoiceValueIcon(col, row[col.field]).name"
                />
                {{ row[col.field] }}</span
              >
            </template>
          </template>
        </vxe-column>
      </vxe-colgroup>

      <template #empty>
        <div>
          <img :style="{ width: '140px' }" :src="require('@/assets/data_empty.png')" />
          <div>{{ $t('noData') }}</div>
        </div>
      </template>
      <template #loading>
        <div style="height: 200px; line-height: 200px">{{ $t('loading') }}</div>
      </template>
    </vxe-table>
    <div :style="{ textAlign: 'right', marginTop: '4px' }">
      <a-pagination
        :showSizeChanger="true"
        :current="currentPage"
        size="small"
        :total="totalNumber"
        show-quick-jumper
        :page-size="pageSize"
        :page-size-options="pageSizeOptions"
        @showSizeChange="onShowSizeChange"
        :show-total="
          (total, range) =>
            $t('pagination.total', {
              range0: range[0],
              range1: range[1],
              total,
            })
        "
        @change="
          (page) => {
            currentPage = page
            loadInstance(sortByTable)
          }
        "
      >
        <template slot="buildOptionText" slot-scope="props">
          <span v-if="props.value !== '100000'">{{ props.value }}{{ $t('itemsPerPage') }}</span>
          <span v-if="props.value === '100000'">{{ $t('all') }}</span>
        </template>
      </a-pagination>
    </div>

    <BatchDownload
      :replaceFields="{ cildren: 'children', title: 'label', key: 'id' }"
      ref="batchDownload"
      @batchDownload="batchDownload"
    />
  </div>
</template>

<script>
import _ from 'lodash'
import CloudSearchForm from '@/modules/cmdb/components/searchForm/CloudSearchForm.vue'
import { getCloudHistory } from '@/modules/cmdb/api/cloudHistory'
import { searchAttributes, getCITypeAttributesByTypeIds, getCITypeAttributesById } from '@/modules/cmdb/api/CITypeAttr'
import { getCITypes, getCIType } from '@/modules/cmdb/api/CIType'
import { getSubscribeAttributes } from '@/modules/cmdb/api/preference'
import { getCITableColumns } from '@/modules/cmdb/utils/helper'
import EditAttrsPopover from '@/modules/cmdb/views/ci/modules/editAttrsPopover.vue'
import PasswordField from '@/modules/cmdb/components/passwordField/index.vue'
import BatchDownload from '@/modules/cmdb/components/batchDownload/batchDownload.vue'
import PreferenceSearch from '@/modules/cmdb/components/preferenceSearch/preferenceSearch.vue'

export default {
  name: 'ResourceSearch',
  components: { CloudSearchForm, EditAttrsPopover, PasswordField, BatchDownload, PreferenceSearch },
  props: {
    fromCronJob: {
      type: Boolean,
      default: false,
    },
    typeId: {
      type: Number,
      default: null
    },
    type: {
      type: String,
      default: 'resourceSearch'
    }
  },
  data() {
    return {
      ciTypes: [],
      originAllAttributesList: [],
      allAttributesList: [], // 当前选择的模型的全部attributes  默认全部
      currentPage: 1,
      pageSizeOptions: ['50', '100', '200', '100000'],
      pageSize: 50,
      totalNumber: 0,
      instanceList: [],
      // columns: {},
      sortByTable: undefined,
      loading: false,
      columnsGroup: [],
    }
  },
  computed: {
    windowHeight() {
      return this.$store.state.windowHeight
    },
  },
  provide() {
    return {
      setPreferenceSearchCurrent: this.setPreferenceSearchCurrent,
      filterCompPreferenceSearch: () => {},
    }
  },
  mounted() {
    if (this.typeId) {
      this.getCIType(this.typeId)
      this.getAttrsByType(this.typeId)
      this.loadInstance()
    } else {
      this.getAllAttr()
      this.getAllCiTypes()
    }
  },
  methods: {
    getAllCiTypes() {
      getCITypes().then((res) => {
        this.ciTypes = res.ci_types
      })
    },
    async getCIType(typeId) {
      await getCIType(typeId).then((res) => {
        this.ciTypes = res.ci_types
      })
    },
    async getAttrsByType(typeId) {
      await getCITypeAttributesById(typeId).then((res) => {
        this.allAttributesList = res.attributes
        this.originAllAttributesList = res.attributes
      })
    },
    async getAllAttr() {
      await searchAttributes({ page_size: 9999 }).then((res) => {
        this.allAttributesList = res.attributes
        this.originAllAttributesList = res.attributes
      })
    },
    async updateAllAttributesList(value) {
      if (value && value.length) {
        await getCITypeAttributesByTypeIds({ type_ids: value.join(',') }).then((res) => {
          this.allAttributesList = res.attributes
        })
      } else {
        this.allAttributesList = this.originAllAttributesList
      }
    },
    async loadInstance(sortByTable = undefined) {
      this.loading = true
      // 若模糊搜索可以 queryParam相关后期可删除
      // const queryParams = this.$refs['search'].queryParam || {}
      const fuzzySearch = this.$refs['search'].fuzzySearch
      const expression = this.$refs['search'].expression || ''
      const regQ = /(?<=q=).+(?=&)|(?<=q=).+$/g
      const regSort = /(?<=sort=).+/g

      const exp = expression.match(regQ) ? expression.match(regQ)[0] : null
      // if (exp) {
      //   exp = exp.replace(/(\:)/g, '$1*')
      //   exp = exp.replace(/(\,)/g, '*$1')
      // }
      // 如果是表格点击的排序 以表格为准
      let sort
      if (sortByTable) {
        sort = sortByTable
      } else {
        sort = expression.match(regSort) ? expression.match(regSort)[0] : undefined
      }
      if (!sort) {
        sort = '_type'
      }
      console.log(this.$refs['search'].currenCiType)
      const currenCiType = this.$refs['search'].currenCiType
      console.log(currenCiType.length)
      // 当选项改为单选时，此处的判断逻辑有问题。
      // if (!currenCiType.length) {
      //   console.log('haha')
      //   const _currenCiType = []
      //   this.$refs['search'].ciTypeGroup.forEach((item) => {
      //     _currenCiType.push(...item.ci_types.map((type) => type.id))
      //   })
      //   currenCiType = _currenCiType
      // }
      getCloudHistory({
        q: `${currenCiType ? `_type:(${currenCiType})` : ''}${exp ? `,${exp}` : ''}${
          fuzzySearch ? `,*${fuzzySearch}*` : ''
        }`,
        count: this.pageSize,
        page: this.currentPage,
        sort,
      })
        .then(async (res) => {
          this.columnsGroup = []
          this.instanceList = []
          this.totalNumber = res['numfound']
          if (!res['numfound']) {
            return
          }
          const { attributes: resAllAttributes } = await getCITypeAttributesByTypeIds({
            type_ids: Object.keys(res.counter).join(','),
          })
          const _columnsGroup = Object.keys(res.counter).map((key) => {
            const _find = this.ciTypes.find((item) => item.name === key)
            return {
              id: `parent-${_find.id}`,
              value: key,
              label: _find?.alias || _find?.name,
              isCiType: true,
            }
          })
          const ciTypeAttribute = {}
          const promises = _columnsGroup.map((item) => {
            return getCITypeAttributesById(item.id.split('-')[1]).then((res) => {
              ciTypeAttribute[item.label] = res.attributes
            })
          })
          await Promise.all(promises)

          const outputKeys = {}
          resAllAttributes.forEach((attr) => {
            outputKeys[attr.name] = ''
          })

          const common = {}
          Object.keys(outputKeys).forEach((key) => {
            Object.entries(ciTypeAttribute).forEach(([type, attrs]) => {
              if (attrs.find((a) => a.name === key)) {
                if (key in common) {
                  common[key][type] = ''
                } else {
                  common[key] = { [type]: '' }
                }
              }
            })
          })

          const commonObject = {}
          const commonKeys = []
          // 整理common
          Object.keys(common).forEach((key) => {
            if (Object.keys(common[key]).length > 1) {
              commonKeys.push(key)
              const reverseKey = Object.keys(common[key]).join('&')
              if (!commonObject[reverseKey]) {
                commonObject[reverseKey] = [key]
              } else {
                commonObject[reverseKey].push(key)
              }
            }
          })
          const _commonColumnsGroup = Object.keys(commonObject).map((key) => {
            return {
              id: `parent-${key}`,
              value: key,
              label: key,
              children: this.getColumns(
                res.result,
                commonObject[key].map((item) => {
                  const _find = this.allAttributesList.find((attr) => attr.name === item)
                  return _find
                })
              ),
            }
          })

          const promises1 = _columnsGroup.map((item) => {
            return getSubscribeAttributes(item.id.split('-')[1]).then((res1) => {
              item.children = this.getColumns(res.result, res1.attributes).filter(
                (col) => !commonKeys.includes(col.field)
              )
            })
          })
          await Promise.all(promises1).then(() => {
            console.log(_commonColumnsGroup, _columnsGroup)
            this.columnsGroup = [..._commonColumnsGroup, ..._columnsGroup]
            console.log('columnsGroup', this.columnsGroup[0]['children'])
            console.log(typeof this.columnsGroup[0]['children']) // 打印对象的类型
            this.instanceList = res['result']
          })
          // this.instanceList = res['result']
          // const subscribed = await getSubscribeAttributes(currenCiType)
          // console.log('subscribed', subscribed.attributes)
          // console.log(typeof subscribed.attributes) // 打印对象的类型
          // this.columnsGroup_2 = subscribed.attributes // All columns that have been subscribed
        })
        .finally(() => {
          this.loading = false
        })
    },
    getColumns(data, attrList) {
      const width = document.getElementById('resource_search').clientWidth - 50
      return getCITableColumns(data, attrList, width).map((item) => {
        return { ...item, id: item.field, label: item.title }
      })
    },
    async handleSearch() {
      this.currentPage = 1
      // await this.updateAllAttributesList()
      this.loadInstance()
    },
    onShowSizeChange(current, pageSize) {
      this.pageSize = pageSize
      this.currentPage = 1
      this.loadInstance()
    },
    handleSortCol() {},
    getCellStyle({ row, rowIndex, $rowIndex, column, columnIndex, $columnIndex }) {
      const { property } = column
      const _find = this.allAttributesList.find((attr) => attr.name === property)
      if (
        _find &&
        _find.option &&
        _find.option.fontOptions &&
        row[`${property}`] !== undefined &&
        row[`${property}`] !== null
      ) {
        return { ..._find.option.fontOptions }
      }
    },
    getChoiceValueStyle(col, colValue) {
      const _find = col.filters.find((item) => String(item[0]) === String(colValue))
      if (_find) {
        return _find[1]?.style || {}
      }
      return {}
    },
    getChoiceValueIcon(col, colValue) {
      const _find = col.filters.find((item) => String(item[0]) === String(colValue))
      if (_find) {
        return _find[1]?.icon || {}
      }
      return {}
    },
    handleExport() {
      this.$refs.batchDownload.open({
        preferenceAttrList: this.columnsGroup[0]['children'],
        label: this.$t('cmdb.ciType.ciType')
      })
    },
    batchDownload({ filename, type, checkedKeys }) {
      const jsonAttrList = []
      checkedKeys.forEach((key) => {
        const _find = this.allAttributesList.find((attr) => attr.name === key)
        if (_find && _find.value_type === '6') {
          jsonAttrList.push(key)
        }
      })
      const data = _.cloneDeep(this.instanceList)
      console.log('data', data)
      this.$refs.xTable.exportData({
        filename,
        type,
        columnFilterMethod({ column }) {
          return checkedKeys.includes(column.property)
        },
        data,
        download: false,
      })
      this.selectedRowKeys = []
      this.$refs.xTable.clearCheckboxRow()
      this.$refs.xTable.clearCheckboxReserve()
    },
    getQAndSort() {
      const fuzzySearch = this.$refs['search'].fuzzySearch || ''
      const expression = this.$refs['search'].expression || ''
      const currenCiType = this.$refs['search'].currenCiType || undefined
      this.$refs.preferenceSearch.savePreference({ fuzzySearch, expression, currenCiType })
    },
    setParamsFromPreferenceSearch(item) {
      const { fuzzySearch, expression, currenCiType } = item.option
      this.$refs.search.fuzzySearch = fuzzySearch
      this.$refs.search.expression = expression
      this.$refs.search.currenCiType = currenCiType
      this.currentPage = 1
      this.$nextTick(() => {
        this.loadInstance()
      })
    },
    setPreferenceSearchCurrent(id = null) {
      if (this.$refs.preferenceSearch) {
        this.$refs.preferenceSearch.currentPreferenceSearch = id
      }
    },
    copyExpression() {
      const expression = this.$refs['search'].expression || ''
      const fuzzySearch = this.$refs['search'].fuzzySearch

      const regQ = /(?<=q=).+(?=&)|(?<=q=).+$/g

      const exp = expression.match(regQ) ? expression.match(regQ)[0] : null
      let currenCiType = this.$refs['search'].currenCiType
      if (!currenCiType.length) {
        const _currenCiType = []
        this.$refs['search'].ciTypeGroup.forEach((item) => {
          _currenCiType.push(...item.ci_types.map((type) => type.id))
        })
        currenCiType = _currenCiType
      }
      const text = `q=${currenCiType && currenCiType.length ? `_type:(${currenCiType.join(';')})` : ''}${
        exp ? `,${exp}` : ''
      }${fuzzySearch ? `,*${fuzzySearch}*` : ''}`
      this.$copyText(text)
        .then(() => {
          this.$message.success(this.$t('copySuccess'))
          this.$emit('copySuccess', text)
        })
        .catch(() => {
          this.$message.error(this.$t('cmdb.ci.copyFailed'))
        })
    },
  },
}
</script>

<style lang="less" scoped>

.resource-search {
  margin-bottom: -24px;
  background-color: #fff;
  padding: 20px;
  border-radius: @border-radius-box;

  &-tip {
    margin-bottom: 16px;

    &-item {
      font-size: 12px;
      color: @text-color_4
    }
  }
}

.button-container {
  display: flex;
  justify-content: flex-end;
}

</style>
