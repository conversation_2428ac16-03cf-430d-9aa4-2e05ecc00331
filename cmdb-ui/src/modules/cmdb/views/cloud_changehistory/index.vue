<template>
  <div>
    <a-card :bordered="false">
      <a-tabs default-active-key="1">
        <a-tab-pane key="1" :tab="$t('cmdb.history.cloudHistoryChange')">
          <change-history></change-history>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script>
import exportTab from './modules/exportTab.vue'
import changeHistory from './modules/changeHistory.vue'
export default {
  name: 'OperationHistory',
  components: {
    exportTab,
    changeHistory
  },
  data() {
    return {
      userList: [],
    }
  },
}
</script>

<style></style>
