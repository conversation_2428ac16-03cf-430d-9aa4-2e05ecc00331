<template>
  <div class="cost-table" :style="{ padding: '0 20px 20px' }">
    <div class="cost-table-add">
      <a-button
        type="primary"
        @click="handleCreate"
        ghost
        class="ops-button-ghost"
      >
        <ops-icon type="veops-increase" />
        {{ $t('create') }}
      </a-button>
    </div>
    <vxe-table
      ref="xTable"
      stripe
      :data="tableData"
      size="small"
      show-overflow
      show-header-overflow
      highlight-hover-row
      keep-source
      class="ops-stripe-table"
      min-height="100"
      :row-class-name="rowClass"
      :edit-config="{ trigger: 'dblclick', mode: 'cell', showIcon: false }"
      resizable
      @edit-closed="handleEditClose"
      @edit-actived="handleEditActived"
    >
      <vxe-column field="cost_type_name" :title="$t('cmdb.ciType.costType')"></vxe-column>
      <vxe-column field="attr_num_name" :title="$t('cmdb.ciType.attributeAssociatedTip3')"></vxe-column>
      <vxe-column field="attr_text_name" :title="$t('cmdb.ciType.attributeAssociatedTip4')"></vxe-column>
      <vxe-column field="attr_float_name" :title="$t('cmdb.ciType.attributeAssociatedTip2')"></vxe-column>
      <vxe-column field="cost_unit_name" :title="$t('cmdb.ciType.costUnit')"></vxe-column>
      <vxe-column :width="400" field="attributeAssociation" :edit-render="{}">
        <template #header>
          <span>
            <a-tooltip :title="$t('cmdb.ciType.attributeAssociationTip1')">
              <a><a-icon type="question-circle" /></a>
            </a-tooltip>
            {{ $t('cmdb.ciType.attributeAssociation') }}
            <span :style="{ fontSize: '10px', fontWeight: 'normal' }" class="text-color-4">{{
              $t('cmdb.ciType.attributeAssociationTip2')
            }}</span>
          </span>
        </template>
        <template #default="{ row }">
          <template v-for="(price, key_text) in row.cost_unit_price">
            <div :key="key_text">{{ key_text }}={{ price }}</div>
          </template>
        </template>
        <template #edit="{ row }">
          <div v-for="item in tableAttrList" :key="item.text_value" class="table-attribute-row">
            <a-select
              allowClear
              size="small"
              v-model="item.text_value"
              :getPopupContainer="(trigger) => trigger.parentNode"
              :style="{ width: '100px' }"
              show-search
              optionFilterProp="title"
              :disabled="item.text_value === '*'"
            >
              <a-select-option
                v-for="choice in getAssociatedTEXT(row.attr_text_id)"
                :key="choice.id"
                :value="choice.name"
                :title="choice.name"
              >
                {{ choice.name }}
              </a-select-option>
            </a-select>
            <span class="table-attribute-row-link">=</span>
            <a-input
              :getPopupContainer="(trigger) => trigger.parentNode"
              name="item.cost_unit_price"
              :placeholder="$t('cmdb.ciType.costUnitPriceTip1')"
              v-model="item.cost_unit_price"
            />
            <a v-if="item.text_value !== '*'" class="table-attribute-row-action" @click="removeTableAttr(item.id)">
              <a-icon type="minus-circle" />
            </a>
            <a v-if="item.text_value !== '*'" class="table-attribute-row-action" @click="addTableAttr">
              <a-icon type="plus-circle" />
            </a>
          </div>
        </template>
      </vxe-column>
      <vxe-column field="operation" :title="$t('operation')" width="100">
        <template #default="{ row }">
          <a-space>
            <a-popconfirm :title="$t('cmdb.ciType.confirmDelete2')" @confirm="handleDelete(row)">
              <a style="color: red"><a-icon type="delete" /></a>
            </a-popconfirm>
          </a-space>
        </template>
      </vxe-column>
      <template #empty>
        <div>
          <img :style="{ width: '100px' }" :src="require('@/assets/data_empty.png')" />
          <div>{{ $t('noData') }}</div>
        </div>
      </template>
    </vxe-table>
    <a-modal
      :closable="false"
      :title="drawerTitle"
      :visible="visible"
      @cancel="onClose"
      @ok="handleSubmit"
      width="800px"
    >
      <a-form :form="form" @submit="handleSubmit" :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }">
        <a-form-item :label="$t('cmdb.ciType.costType')" :wrapper-col="{ span: 7 }">
          <a-select
            name="cost_type_id"
            :placeholder="$t('cmdb.ciType.costType')"
            v-decorator="[
              'cost_type_id',
              {
                rules: [{ required: true, message: $t('cmdb.ciType.cost_type_id') }],
              },
            ]"
            @change="changecostType"
          >
            <a-select-option :value="costTypeOption.id" :key="costTypeOption.id" v-for="costTypeOption in costType">{{
              costTypeOption.name
            }}</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item :label="$t('cmdb.ciType.attributeAssociated')" required>
          <a-col :span="7">
            <a-form-item>
              <a-select
                name="attr_num_id"
                :placeholder="$t('cmdb.ciType.attributeAssociatedTip3')"
                v-decorator="[
                  'attr_num_id',
                  {
                    rules: [{ required: false, message: $t('cmdb.ciType.attributeAssociatedTip3') }],
                  },
                ]"
                allowClear
                :disabled="form.getFieldValue('cost_type_id') === '1'"
              >
                <a-select-option v-for="attr in filterAttributes(attributes, 'num')" :key="attr.id">
                  {{ attr.alias || attr.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="2" :style="{ textAlign: 'center' }"> * </a-col>
          <a-col :span="7">
            <a-form-item>
              <a-select
                name="attr_text_id"
                v-decorator="[
                  'attr_text_id',
                  {
                    rules: [{ required: false, message: $t('cmdb.ciType.attributeAssociatedTip4') }],
                  },
                ]"
                :placeholder="$t('cmdb.ciType.attributeAssociatedTip4')"
                allowClear
                :disabled="form.getFieldValue('cost_type_id') === '0'"
                @change="changeAssociatedTEXT"
              >
                <a-select-option v-for="attr in filterAttributes(attributes, 'text')" :key="attr.id">
                  {{ attr.alias || attr.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="2" :style="{ textAlign: 'center' }"> => </a-col>
          <a-col :span="6">
            <a-form-item>
              <a-select
                name="attr_float_id"
                v-decorator="[
                  'attr_float_id',
                  {
                    rules: [{ required: true, message: $t('cmdb.ciType.attributeAssociatedTip2') }],
                  },
                ]"
                :placeholder="$t('cmdb.ciType.attributeAssociatedTip2')"
                allowClear
              >
                <a-select-option v-for="attr in filterAttributes(attributes, 'float')" :key="attr.id">
                  {{ attr.alias || attr.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-form-item>
        <a-form-item :label="$t('cmdb.ciType.measurementUnit')" :wrapper-col="{ span: 7 }">
          <a-select
            showSearch
            name="cost_unit_id"
            :placeholder="$t('cmdb.ciType.measurementUnit')"
            v-decorator="[
              'cost_unit_id',
              {
                rules: [{ required: true, message: $t('cmdb.ciType.measurementUnit') }],
              },
            ]"
            :disabled="form.getFieldValue('cost_type_id') === '1' || form.getFieldValue('cost_type_id') === '2'"
          >
            <a-select-option
              :value="costUnitOption.id"
              :key="costUnitOption.id"
              v-for="costUnitOption in displayMeasurementUnit"
            >
              {{ costUnitOption.name }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item
          v-if="form.getFieldValue('cost_type_id') === '0'"
          :label="$t('cmdb.ciType.costUnitPrice')"
          :wrapper-col="{ span: 7 }"
        >
          <a-input
            name="cost_unit_price"
            :placeholder="$t('cmdb.ciType.costUnitPriceTip1')"
            v-decorator="[
              'cost_unit_price',
              {
                rules: [
                  { required: true, message: $t('cmdb.ciType.costUnitPriceTip1') },
                  {
                    message: $t('cmdb.ciType.costUnitPriceTip2'),
                    pattern: RegExp('^[0-9]*\\.?[0-9]+$'),
                  },
                ],
              },
            ]"
          />
        </a-form-item>
        <a-form-item
          v-if="form.getFieldValue('cost_type_id') === '1' || form.getFieldValue('cost_type_id') === '2'"
          :label="$t('cmdb.ciType.costUnitPrice')"
          required
        >
          <a-row v-for="item in modalAttrList" :key="item.id">
            <a-col :span="10">
              <a-form-item>
                <a-select
                  name="item.text_value"
                  :placeholder="$t('cmdb.ciType.costPredefined')"
                  allowClear
                  v-model="item.text_value"
                >
                  <a-select-option v-for="attr in textChoiceValue" :key="attr.id">
                    {{ attr.name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="2" :style="{ textAlign: 'center' }"> = </a-col>
            <a-col :span="9">
              <a-form-item>
                <a-input
                  name="item.cost_unit_price"
                  :placeholder="$t('cmdb.ciType.costUnitPriceTip1')"
                  v-model="item.cost_unit_price"
                />
              </a-form-item>
            </a-col>
            <a-col :span="3">
              <a class="modal-attribute-action" @click="removeModalAttr(item.id)">
                <a-icon type="minus-circle" />
              </a>
              <a class="modal-attribute-action" @click="addModalAttr">
                <a-icon type="plus-circle" />
              </a>
            </a-col>
          </a-row>
        </a-form-item>
      </a-form>
    </a-modal>
    <CMDBGrant ref="cmdbGrant" resourceType="CITypeRelation" app_id="cmdb" />
  </div>
</template>

<script>
import { createCost, deleteCost, getCost } from '@/modules/cmdb/api/CITypeCost'
import { getMeasurementUnit } from '@/modules/cmdb/api/measurementUnit'
import { getCITypes, getCITypeGroupById } from '@/modules/cmdb/api/CIType'
import { getCITypeAttributesById } from '@/modules/cmdb/api/CITypeAttr'
import { v4 as uuidv4 } from 'uuid'

import CMDBGrant from '../../components/cmdbGrant'

export default {
  name: 'CostTable',
  components: {
    CMDBGrant,
  },
  props: {
    CITypeId: {
      type: Number,
      default: null,
    },
    CITypeName: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      source_ci_type_id: 2,
      form: this.$form.createForm(this),
      visible: false,
      drawerTitle: '',
      CITypes: [],
      CITypeGroup: [],
      measurementUnit: [],
      costType: [
        { id: '0', name: '单一属性' },
        { id: '1', name: '字符属性' },
        { id: '2', name: '双属性' },
      ],
      costUnit: [
        { id: 0, name: '1UNIT' },
        { id: 1, name: '100G' },
        { id: 2, name: '2vCPU' },
        { id: 3, name: '4GB' },
      ],
      costUnitPrice: [
        { id: 1, price: 10.9 },
        { id: 2, price: 11.2 },
        { id: 3, price: 32.1 },
      ],
      textChoiceValue: [],
      relationTypes: [],
      tableData: [],
      parentTableData: [],
      attributes: [],
      tableAttrList: [],
      modalAttrList: [],
      modalChildAttributes: [],
      currentEditData: null,
      isContinueCloseEdit: false,
      costUnitOptionId: 0,
    }
  },
  computed: {
    // sourceCiTypeId() {
    //   console.log('work')
    //   return this.form.getFieldValue('source_ci_type_id')
    // },
    displayCITypes() {
      return this.CITypes.filter((c) => c.id === this.CITypeId)
    },
    displayMeasurementUnit() {
      return this.measurementUnit
    },
    windowHeight() {
      return this.$store.state.windowHeight
    },
    constraintMap() {
      return {
        0: this.$t('cmdb.ciType.one2Many'),
        1: this.$t('cmdb.ciType.one2One'),
        2: this.$t('cmdb.ciType.many2Many'),
      }
    },
  },
  async mounted() {
    try {
      const [attributesRes, groupRes] = await Promise.all([
        getCITypeAttributesById(this.CITypeId),
        getCITypeGroupById(this.CITypeId)
      ])
      this.attributes = attributesRes?.attributes ?? []
      this.CITypeGroup = groupRes ?? []
      await Promise.all([
        this.getCITypes(),
        this.getData(),
        this.getMeasurementUnit()
      ])
    } catch (error) {
      console.error('Error during mounted:', error)
    }
  },
  methods: {
    getAssociatedTEXT(attr_id) {
      const attribute_choice_value = this.attributes.find(attr => attr.id === attr_id)?.choice_value ?? []
      const textChoiceValue = attribute_choice_value.map((item, index) => ({
        id: index,
        name: item[0],
      }))
      return textChoiceValue
    },
    changeAssociatedTEXT(value) {
      const attribute_choice_value = this.attributes.find(attr => attr.id === value)?.choice_value ?? []
      this.textChoiceValue = attribute_choice_value.map((item, index) => ({
        id: index,
        name: item[0],
      }))
      this.modalAttrList.forEach(item => {
        item.text_value = undefined
        item.cost_unit_price = undefined
      })
    },
    changecostType(value) {
      this.form.resetFields(['attr_num_id', 'attr_text_id', 'attr_float_id'])
      // 当cost_type_id为'1'或'2'时，设置cost_unit_id为1
      if (value === '1' || value === '2') {
        this.$nextTick(() => {
          this.form.setFieldsValue({
            cost_unit_id: 1
          })
        })
      }
    },
    async getCost() {
      await getCost(this.CITypeId).then((res) => {
        res.forEach((item) => {
          const type = this.costType.find((type) => type.id === item.cost_type_id)
          item.cost_type_name = type ? type.name : null
        })
        this.tableData = res
      })
    },
    async getData() {
      await this.getCost()
    },
    getCITypes() {
      getCITypes().then((res) => {
        this.CITypes = res.ci_types
      })
    },
    getMeasurementUnit() {
      getMeasurementUnit().then((res) => {
        this.measurementUnit = res
      })
    },
    handleDelete(record) {
      deleteCost(record.id).then((res) => {
        this.$message.success(this.$t('deleteSuccess'))
        this.getData()
      })
    },

    handleCreate() {
      this.drawerTitle = this.$t('cmdb.ciType.addCost')
      this.visible = true
      this.$set(this, 'modalAttrList', [
        {
          id: uuidv4(),
          text_value: undefined,
          cost_unit_price: undefined,
        },
      ])
      this.$nextTick(() => {
        this.form.resetFields()
        this.form.setFieldsValue({
          source_ci_type_id: this.source_ci_type_id,
        })
      })
    },
    onClose() {
      this.form.resetFields()
      this.visible = false
    },
    handleSubmit(e) {
      e.preventDefault()
      this.form.validateFields((err, values) => {
        if (!err) {
          let cost_unit_price = {}
          const ci_type_id = this.CITypeId
          const { cost_type_id, attr_num_id, attr_text_id, attr_float_id, cost_unit_id } = values
          if (cost_type_id === '0') {
            cost_unit_price = { '*': values.cost_unit_price }
          } else {
            cost_unit_price = this.handleValidateAttrList(this.modalAttrList, attr_text_id)
          }
          createCost(ci_type_id, {
            cost_type_id,
            attr_num_id,
            attr_text_id,
            attr_float_id,
            cost_unit_id,
            cost_unit_price,
          }).then((res) => {
            this.$message.success(this.$t('addSuccess'))
            this.onClose()
            this.getData()
          })
        }
      })
    },
    handleValidateAttrList(attrList, attr_text_id = null) {
      const key_work = []
      const price = []
      const cost_unit_price = {}
      const isValidNumber = (value) => /^[0-9]*\.?[0-9]+$/.test(value)
      attrList.forEach(attr => {
        let text_value = attr.text_value
        if (attr_text_id !== null) {
          const textChoiceValue = this.getAssociatedTEXT(attr_text_id)
          const textChoice = textChoiceValue.find((text) => text.id === attr.text_value)
          if (textChoice) {
            text_value = textChoice.name
          }
        }
        if (text_value !== null) {
          key_work.push(text_value)
        }
        if (attr.cost_unit_price) {
          if (!isValidNumber(attr.cost_unit_price)) {
            this.$message.warning(this.$t('cmdb.ciType.costUnitPriceTip2'))
            return { validate: false }
          }
          price.push(attr.cost_unit_price)
        }
        cost_unit_price[text_value] = attr.cost_unit_price
      })
      if (key_work.length !== price.length) {
        this.$message.warning(this.$t('cmdb.ciType.attributeAssociationTip3'))
        return { validate: false }
      }
      return cost_unit_price
    },
    rowClass({ row }) {
      if (row.isDivider) return 'cost-table-divider'
      if (row.isParent) return 'cost-table-parent'
    },
    handleEditActived({ row }) {
      this.$nextTick(async () => {
        if (this.isContinueCloseEdit) {
          const editRecord = this.$refs.xTable.getEditRecord()
          const { row: editRow, column } = editRecord
          this.currentEditData = {
            row: editRow,
            column,
          }
          return
        }
        const tableAttrList = []
        const keys = Object.keys(row.cost_unit_price)
        keys.forEach((key) => {
          tableAttrList.push({
            id: uuidv4(),
            text_value: key,
            cost_unit_price: row.cost_unit_price[key],
          })
        })
        this.$set(this, 'tableAttrList', tableAttrList)
      })
    },
    async handleEditClose({ row }) {
      if (this.currentEditData) {
        this.currentEditData = null
        return
      }
      this.isContinueCloseEdit = true
      const { cost_type_id, attr_num_id, attr_text_id, attr_float_id, cost_unit_id } = row
      const cost_unit_price = this.handleValidateAttrList(this.tableAttrList, attr_text_id)
      const ci_type_id = this.CITypeId
      await createCost(ci_type_id, {
        cost_type_id,
        attr_num_id,
        attr_text_id,
        attr_float_id,
        cost_unit_id,
        cost_unit_price,
      }).finally(async () => {
        await this.getData()
        this.isContinueCloseEdit = false
        if (this.currentEditData) {
          setTimeout(async () => {
            const { fullData } = this.$refs.xTable.getTableData()
            const findEdit = fullData.find((item) => item.id === this?.currentEditData?.row?.id)
            await this.$refs.xTable.setEditRow(findEdit, 'attributeAssociation')
          })
        }
      })
    },
    filterAttributes(attributes, filterType) {
      switch (filterType) {
        case 'all':
          return attributes
            .filter((attr) => !attr.is_password && !attr.is_list && attr.value_type !== '6')
        case 'num':
          return attributes
            .filter((attr) => attr.value_type === '1' || attr.value_type === '0')
        case 'float':
          return attributes
            .filter((attr) => attr.value_type === '1')
        case 'text':
          return attributes
            .filter((attr) => attr.value_type === '2')
        default:
          return attributes
      }
    },
    addTableAttr() {
      this.tableAttrList.push({
        id: uuidv4(),
        text_value: undefined,
        cost_unit_price: undefined,
      })
    },
    removeTableAttr(id) {
      if (this.tableAttrList.length <= 1) {
        this.$message.error(this.$t('cmdb.ciType.attributeAssociationTip6'))
        return
      }
      const index = this.tableAttrList.findIndex((item) => item.id === id)
      if (index !== -1) {
        this.tableAttrList.splice(index, 1)
      }
    },

    addModalAttr() {
      this.modalAttrList.push({
        id: uuidv4(),
        text_value: undefined,
        cost_unit_price: undefined,
      })
    },
    removeModalAttr(id) {
      if (this.modalAttrList.length <= 1) {
        this.$message.error(this.$t('cmdb.ciType.attributeAssociationTip6'))
        return
      }
      const index = this.modalAttrList.findIndex((item) => item.id === id)
      if (index !== -1) {
        this.modalAttrList.splice(index, 1)
      }
    },
  },
}
</script>

<style lang="less" scoped>
.cost-table {
  /deep/ .vxe-cell {
    max-height: max-content !important;
  }

  &-add {
    margin-bottom: 10px;
    display: flex;
    justify-content: flex-end;
  }
}
.table-attribute-row {
  display: inline-flex;
  align-items: center;
  margin-top: 5px;

  &:last-child {
    margin-bottom: 5px;
  }

  &-link {
    margin: 0 5px;
  }

  &-action {
    margin-left: 5px;
  }
}

.modal-attribute-action {
  margin-left: 5px;
}
</style>

<style lang="less">
.ops-stripe-table .vxe-body--row.row--stripe.cost-table-divider {
  background-color: #b1b8d3 !important;
}
.ops-stripe-table .vxe-body--row.cost-table-parent {
  background-color: #f5f8ff !important;
}
.cost-table-divider {
  td {
    height: 1px !important;
    line-height: 1px !important;
  }
}
</style>
