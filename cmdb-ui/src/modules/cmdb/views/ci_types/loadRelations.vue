<template>
  <div class="relation-table" :style="{ padding: '0 20px 20px' }">
    <div v-if="!isInGrantComp" class="relation-table-add">
      <a-button
        type="primary"
        @click="handleCreate"
        ghost
        class="ops-button-ghost"
      >
        <ops-icon type="veops-increase" />
        {{ $t('create') }}
      </a-button>
    </div>
    <vxe-table
      ref="xTable"
      stripe
      :data="tableData"
      size="small"
      show-overflow
      show-header-overflow
      highlight-hover-row
      keep-source
      class="ops-stripe-table"
      min-height="500"
      :row-class-name="rowClass"
      :edit-config="{ trigger: 'dblclick', mode: 'cell', showIcon: false }"
      resizable
      @edit-closed="handleEditClose"
      @edit-actived="handleEditActived"
    >
      <vxe-column field="name" :title="$t('cmdb.ciType.attrName')"></vxe-column>
      <vxe-column field="alias" :title="$t('cmdb.ciType.attrAlias')"></vxe-column>
      <vxe-column field="value_type" :title="$t('cmdb.ciType.valueType')">
        <template #default="{row}">
          {{ getValueTypeName(row.value_type) }}
        </template>
      </vxe-column>
      <vxe-column field="is_monthly" :title="$t('cmdb.load_attr.period_type')">
        <template #default="{row}">
          {{ row.is_monthly ? $t('cmdb.load_attr.monthly') : $t('cmdb.load_attr.daily') }}
        </template>
      </vxe-column>
      <vxe-column field="operation" :title="$t('operation')" width="100">
        <template #default="{row}">
          <a-popconfirm
            v-if="!isInGrantComp"
            :title="$t('cmdb.ciType.confirmDeleteAttr')"
            @confirm="handleDelete(row)"
          >
            <a style="color: red;"><a-icon type="delete"/></a>
          </a-popconfirm>
        </template>
      </vxe-column>
    </vxe-table>

    <a-modal
      :closable="false"
      :title="$t('cmdb.ciType.addLoadAttr')"
      :visible="visible"
      @cancel="onClose"
      @ok="handleSubmit"
      width="700px"
    >
      <a-form :form="form" @submit="handleSubmit" :label-col="{ span: 6 }" :wrapper-col="{ span: 14 }">
        <a-form-item :label="$t('cmdb.ciType.selectAttr')">
          <a-select
            mode="multiple"
            name="attr_ids"
            :placeholder="$t('cmdb.ciType.selectAttrTip')"
            v-decorator="[
              'attr_ids',
              { rules: [{ required: true, message: $t('cmdb.ciType.selectAttrTip') }] }
            ]"
            :filterOption="filterOption"
          >
            <a-select-option
              v-for="attr in availableAttrs"
              :key="attr.id"
              :value="attr.id"
            >
              {{ attr.alias || attr.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { getLoadAttributes } from '@/modules/cmdb/api/loadAttribute'
import {
  getTypeLoadAttrs,
  addTypeLoadAttrs,
  deleteTypeLoadAttr
} from '@/modules/cmdb/api/loadRelation'
import { LoadValueTypeOptions } from '@/modules/cmdb/constants'

export default {
  name: 'LoadRelations',
  props: {
    CITypeId: {
      type: Number,
      required: true
    },
    CITypeName: {
      type: String,
      required: true
    },
    isInGrantComp: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: this.$form.createForm(this),
      visible: false,
      tableData: [],
      allAttrs: [],
      currentEditData: null,
      isContinueCloseEdit: false,
      valueTypes: LoadValueTypeOptions
    }
  },
  computed: {
    availableAttrs() {
      // 过滤掉已经关联的属性
      const existingIds = this.tableData.map(item => item.id)
      return this.allAttrs.filter(attr => !existingIds.includes(attr.id))
    }
  },
  async mounted() {
    await this.loadData()
  },
  methods: {
    async loadData() {
      // 加载已关联的属性
      const { attrs } = await getTypeLoadAttrs(this.CITypeId)
      this.tableData = attrs

      // 加载所有可用属性
      const { attrs: allAttrs } = await getLoadAttributes()
      this.allAttrs = allAttrs
    },

    handleCreate() {
      this.visible = true
    },

    onClose() {
      this.form.resetFields()
      this.visible = false
    },

    async handleSubmit(e) {
      e.preventDefault()
      this.form.validateFields(async (err, values) => {
        if (!err) {
          const { attr_ids, is_required } = values
          const attr_configs = attr_ids.map(attr_id => ({
            attr_id,
            is_required,
            order: this.tableData.length // 默认添加到末尾
          }))

          await addTypeLoadAttrs(this.CITypeId, attr_configs)
          this.$message.success(this.$t('addSuccess'))
          this.onClose()
          await this.loadData()
        }
      })
    },

    async handleDelete(row) {
      try {
        await deleteTypeLoadAttr(this.CITypeId, row.id)
        this.$message.success(this.$t('deleteSuccess'))
        await this.loadData()
      } catch (error) {
        this.$message.error(error.message || this.$t('deleteError'))
      }
    },

    async handleRequiredChange(row, checked) {
      try {
        await addTypeLoadAttrs(this.CITypeId, [{
          attr_id: row.id,
          is_required: checked,
          order: row.order
        }])
      } catch (error) {
        row.is_required = !checked // 恢复原值
        this.$message.error(error.message || this.$t('updateError'))
      }
    },

    async handleEditClose({ row }) {
      if (this.currentEditData) {
        this.currentEditData = null
        return
      }

      this.isContinueCloseEdit = true
      try {
        await addTypeLoadAttrs(this.CITypeId, [{
          attr_id: row.id,
          is_required: row.is_required,
          order: row.order
        }])
        await this.loadData()
      } catch (error) {
        this.$message.error(error.message || this.$t('updateError'))
      } finally {
        this.isContinueCloseEdit = false
      }
    },

    handleEditActived({ row }) {
      if (this.isContinueCloseEdit) {
        const editRecord = this.$refs.xTable.getEditRecord()
        this.currentEditData = editRecord
      }
    },

    getValueTypeName(type) {
      const valueType = this.valueTypes.find(t => t.value === type)
      return valueType ? valueType.label : type
    },

    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    },

    rowClass({ row }) {
      if (row.isDivider) return 'relation-table-divider'
    }
  }
}
</script>

<style lang="less" scoped>
.relation-table {
  /deep/ .vxe-cell {
    max-height: max-content !important;
  }

  &-add {
    margin-bottom: 10px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>

<style lang="less">
.ops-stripe-table .vxe-body--row.relation-table-divider {
  background-color: #b1b8d3 !important;
}
</style>
