<template>
  <CustomDrawer
    width="90%"
    placement="left"
    @close="
      () => {
        visible = false
      }
    "
    style="transform: translateX(0px)!important"
    :visible="visible"
    :hasTitle="false"
    :hasFooter="false"
    :bodyStyle="{ padding: 0, height: '100vh' }"
    destroyOnClose
  >
    <CiDetailTab ref="ciDetailTab" :typeId="currentTypeId" :treeViewsLevels="treeViewsLevels" />
  </CustomDrawer>
</template>

<script>
import CiDetailTab from './ciDetailTab.vue'
export default {
  name: 'CiDetailDrawer',
  components: { CiDetailTab },
  props: {
    typeId: {
      type: Number,
      required: false,
      default: null,
    },
    treeViewsLevels: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      visible: false,
      currentTypeId: this.typeId,
    }
  },
  methods: {
    create(ciId, activeTabKey = 'tab_1', ciDetailRelationKey = '1', customTypeId = null) {
      this.visible = true
      // 如果提供了自定义typeId，临时更新组件的typeId
      if (customTypeId) {
        this.currentTypeId = customTypeId
      } else {
        this.currentTypeId = this.typeId
      }
      this.$nextTick(() => {
        this.$refs.ciDetailTab.create(ciId, activeTabKey, ciDetailRelationKey)
      })
    },
  },
}
</script>
