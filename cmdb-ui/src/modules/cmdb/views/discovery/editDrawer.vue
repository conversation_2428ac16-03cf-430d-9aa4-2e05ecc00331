<template>
  <CustomDrawer
    width="800px"
    :title="title"
    :visible="visible"
    :bodyStyle="{ height: 'calc(-108px + 100vh)' }"
    @close="handleClose"
  >
    <AgentTable
      v-if="adType === DISCOVERY_CATEGORY_TYPE.AGENT"
      :tableData="tableData"
    />
    <template v-else-if="adType === DISCOVERY_CATEGORY_TYPE.PLUGIN">
      <a-form-model
        ref="autoDiscoveryForm"
        :model="form"
        :rules="rules"
        :label-col="{ span: 2 }"
        :wrapper-col="{ span: 20 }"
      >
        <a-divider :style="{ margin: '5px 0' }">{{ $t('cmdb.ciType.basicConfig') }}</a-divider>
        <a-form-model-item :label="$t('name')" prop="name">
          <a-input v-model="form.name" />
        </a-form-model-item>
        <a-form-model-item :label="$t('icon')" v-if="is_inner">
          <CustomIconSelect v-model="customIcon" :style="{ marginTop: '6px' }" />
        </a-form-model-item>
        <a-form-model-item :label="$t('cmdb.ad.mode')" prop="mode">
          <a-radio-group v-model="form.mode" @change="changeMode" :disabled="!is_inner">
            <a-radio value="default">{{ $t('cmdb.custom_dashboard.default') }}</a-radio>
            <a-radio value="plugin">agent</a-radio>
            <a-radio value="prefect">prefect</a-radio>
          </a-radio-group>
        </a-form-model-item>
      </a-form-model>
      <a-divider :style="{ margin: '5px 0' }">{{ $t('cmdb.ad.collectSettings') }}</a-divider>
      <CustomCodeMirror
        codeMirrorId="cmdb-adt"
        v-if="form.mode === 'plugin' || form.mode === 'prefect'"
        ref="codemirror"
        @changeCodeContent="changeCodeContent"
      ></CustomCodeMirror>
      <div style="margin:10px 0;text-align:right;">
        <a-button
          v-show="form.mode === 'plugin' || form.mode === 'prefect'"
          size="small"
          type="primary"
          ghost
          :loading="submitLoading"
          @click="handleSubmit(true)"
        >{{ $t('cmdb.ad.updateFields') }}</a-button
        >
      </div>
      <a-button
        v-show="form.mode === 'default'"
        size="small"
        type="primary"
        ghost
        icon="plus"
        :style="{ marginBottom: '10px' }"
        @click="insertEvent(-1)"
      >
        {{ $t('new') }}
      </a-button>
      <vxe-table
        size="mini"
        stripe
        class="ops-stripe-table"
        show-overflow
        keep-source
        ref="xTable"
        max-height="400"
        :data="tableData"
        :edit-config="{ trigger: 'manual', mode: 'row' }"
      >
        <vxe-column field="name" :title="$t('name')" :edit-render="{ autofocus: '.vxe-input--inner' }">
          <template #edit="{ row }">
            <vxe-input v-model="row.name" type="text"></vxe-input>
          </template>
        </vxe-column>
        <vxe-column field="type" :title="$t('type')" :edit-render="{}">
          <template #edit="{ row }">
            <vxe-select v-model="row.type" transfer>
              <vxe-option v-for="item in typeList" :key="item" :value="item" :label="item"></vxe-option>
            </vxe-select>
          </template>
        </vxe-column>
        <vxe-column field="desc" :title="$t('desc')" :edit-render="{ autofocus: '.vxe-input--inner' }">
          <template #edit="{ row }">
            <vxe-input v-model="row.desc" type="text"></vxe-input>
          </template>
        </vxe-column>
        <vxe-column
          :title="$t('operation')"
          width="60"
          v-if="form.mode === 'default'"
        >
          <template #default="{ row }">
            <a-space v-if="$refs.xTable.isActiveByRow(row)">
              <a @click="saveRowEvent(row)"><a-icon type="save"/></a>
              <a @click="cancelRowEvent(row)"><a-icon type="close"/></a>
            </a-space>
            <a-space v-else>
              <a @click="editRowEvent(row)"><a-icon type="edit"/></a>
              <a :style="{ color: 'red' }" @click="deleteRowEvent(row)"><a-icon type="delete"/></a>
            </a-space>
          </template>
        </vxe-column>
      </vxe-table>

      <div class="custom-drawer-bottom-action">
        <a-button @click="handleClose">{{ $t('cancel') }}</a-button>
        <a-button @click="handleSubmit(false)" type="primary" :loading="submitLoading">{{ $t('save') }}</a-button>
      </div>
    </template>
    <template v-else>
      <HttpSnmpAD ref="httpSnmpAd" :ruleType="adType" :ruleName="ruleName" />
    </template>
  </CustomDrawer>
</template>

<script>
import { postDiscovery, putDiscovery } from '../../api/discovery'
import { DISCOVERY_CATEGORY_TYPE } from './constants.js'

import AgentTable from './agentTable.vue'
import CustomIconSelect from '@/components/CustomIconSelect'
import HttpSnmpAD from '../../components/httpSnmpAD'
import CustomCodeMirror from '@/components/CustomCodeMirror'
import 'codemirror/lib/codemirror.css'
import 'codemirror/theme/monokai.css'

export default {
  name: 'EditDrawer',
  components: {
    CustomIconSelect,
    CustomCodeMirror,
    HttpSnmpAD,
    AgentTable
  },
  props: {
    is_inner: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    const default_plugin_script = this.$t('cmdb.ad.pluginScript')
    const default_plugin_prefect = this.$t('cmdb.ad.pluginPrefect')
    const typeList = ['String', 'Integer', 'Float', 'Date', 'DateTime', 'Time', 'JSON']
    return {
      default_plugin_script,
      default_plugin_prefect,
      typeList,
      visible: false,
      ruleData: {},
      type: 'add',
      adType: '',
      form: { name: '', mode: 'default' },
      rules: {},
      customIcon: { name: '', color: '' },
      tableData: [],
      editDefaultTableData: [],
      plugin_script: '',
      hasUpdatedFields: false,
      submitLoading: false,
      DISCOVERY_CATEGORY_TYPE,
    }
  },
  computed: {
    title() {
      if ([DISCOVERY_CATEGORY_TYPE.HTTP, DISCOVERY_CATEGORY_TYPE.SNMP, DISCOVERY_CATEGORY_TYPE.AGENT, DISCOVERY_CATEGORY_TYPE.PRIVATE_CLOUD, DISCOVERY_CATEGORY_TYPE.COMPONENT].includes(this.adType)) {
        return this.ruleData.name
      }
      if (this.type === 'edit') {
        return this.$t('edit') + `：${this.ruleData.name}`
      }
      return this.$t('new')
    },
    ruleName() {
      return this?.ruleData?.option?.en || this?.ruleData?.name || ''
    }
  },
  inject: {
    getDiscovery: {
      from: 'getDiscovery',
      default: () => {},
    },
  },
  methods: {
    open(data, type, adType) {
      this.visible = true
      this.type = type
      this.ruleData = data || {}
      this.adType = adType
      this.hasUpdatedFields = false // 重置状态

      if (type === 'add') {
        this.form = {
          name: '',
          mode: data?.mode || 'default'
        }
        this.customIcon = { name: 'caise-chajian', color: '' }
        this.tableData = []
        this.editDefaultTableData = []
      } else {
        this.form = {
          name: data.name,
          mode: data.mode || (data.type === DISCOVERY_CATEGORY_TYPE.PREFECT ? 'prefect' : (data.is_plugin ? 'plugin' : 'default')),
        }
        this.customIcon = data?.option?.icon ?? { name: 'caise-chajian', color: '' }
        this.tableData = data?.attributes ?? []
        this.editDefaultTableData = data?.attributes ?? []
      }

      if (adType === DISCOVERY_CATEGORY_TYPE.HTTP || adType === DISCOVERY_CATEGORY_TYPE.SNMP || adType === DISCOVERY_CATEGORY_TYPE.PRIVATE_CLOUD) {
         if (type === 'edit') {
            this.tableData = data?.attributes ?? []
         }
      } else if (adType === DISCOVERY_CATEGORY_TYPE.AGENT) {
          if (type === 'edit') {
             this.tableData = data?.attributes ?? []
          }
      }

      if (this.type === 'add') {
        if (this.form.mode === 'prefect') {
             this.plugin_script = this.default_plugin_prefect
        } else if (this.form.mode === 'plugin') {
             this.plugin_script = this.default_plugin_script
        } else {
             this.plugin_script = ''
        }
      } else {
          if (this.form.mode === 'prefect') {
             this.plugin_script = data?.plugin_script ?? this.default_plugin_prefect
          } else if (this.form.mode === 'plugin') {
             this.plugin_script = data?.plugin_script ?? this.default_plugin_script
          } else {
             this.plugin_script = ''
          }
      }

      this.$nextTick(() => {
         if (this.form.mode === 'plugin' || this.form.mode === 'prefect') {
            if (this.$refs.codemirror) {
                this.$refs.codemirror.initCodeMirror(this.plugin_script)
            } else {
               console.warn('CodeMirror ref not found immediately after nextTick.')
            }
         }

         if (this.$refs.autoDiscoveryForm) {
             this.$refs.autoDiscoveryForm.clearValidate()
         }
      })
    },
    handleClose() {
      this.tableData = []
      this.customIcon = { name: '', color: '' }
      this.form = { name: '', mode: 'default' }
      if (this.adType === DISCOVERY_CATEGORY_TYPE.PLUGIN) {
        this.$refs.autoDiscoveryForm.clearValidate()
      } else {
        // this.$refs.httpSnmpAd.currentCate = ''
      }
      this.visible = false
    },
    async insertEvent(row) {
      const $table = this.$refs.xTable
      const record = {}
      const { row: newRow } = await $table.insertAt(record, row)
      await $table.setEditRow(newRow)
    },
    editRowEvent(row) {
      const $table = this.$refs.xTable
      $table.setActiveRow(row)
    },
    saveRowEvent() {
      const $table = this.$refs.xTable
      $table.clearActived().then(() => {
        this.loading = true
        setTimeout(() => {
          this.loading = false
        }, 300)
      })
    },
    cancelRowEvent(row) {
      const $table = this.$refs.xTable
      $table.clearActived().then(() => {
        // Restore row data
        $table.revertData(row)
      })
    },
    deleteRowEvent(row) {
      const $table = this.$refs.xTable
      $table.remove(row)
    },
    async handleSubmit(isUpdateAttr = false) {
      // 如果不是更新字段操作，且已经更新过字段，则直接关闭抽屉
      if (!isUpdateAttr && this.hasUpdatedFields) {
        this.handleClose()
        return
      }

      this.submitLoading = true // 开始加载动画

      try {
        // Ensure table data is correctly retrieved only if table exists and mode is default
        let _tableData = []
        if (this.form.mode === 'default' && this.$refs.xTable) {
           const { fullData } = this.$refs.xTable.getTableData()
           _tableData = fullData
        }

        console.log('Table Data for submit:', _tableData) // Log for debugging
        let type
        if (this.form.mode === 'prefect') {
          type = DISCOVERY_CATEGORY_TYPE.PREFECT // Use PREFECT type if mode is prefect
        } else {
          // Original logic for other modes
          type = this.adType === DISCOVERY_CATEGORY_TYPE.PLUGIN ? DISCOVERY_CATEGORY_TYPE.AGENT : this.adType
        }

        const params = {
          name: this.form.name,
          is_plugin: this.form.mode === 'plugin' || this.form.mode === 'prefect',
          type, // Use the calculated type
          is_inner: this.is_inner,
          option: { icon: this.customIcon },
          // Conditionally add attributes or plugin_script based on mode
          ...(this.form.mode === 'default' && {
               attributes: _tableData.map(({ name, alias, desc, type }) => ({ name, alias, desc, type }))
          }),
          ...((this.form.mode === 'plugin' || this.form.mode === 'prefect') && {
               plugin_script: this.plugin_script
          })
        }

        console.log('Params for submit:', params) // Log for debugging

        let res
        if (this.type === 'add') {
          res = await postDiscovery(params)
        } else {
          res = await putDiscovery(this.ruleData.id, params)
        }

        if (isUpdateAttr && (this.form.mode === 'plugin' || this.form.mode === 'prefect')) {
          // Update tableData only if the response contains attributes AND it's relevant
          if (res && res.attributes) {
             this.tableData = res.attributes
          }
          this.type = 'edit' // Stay in edit mode
          this.ruleData = res // Update ruleData with the full response
          this.$message.success(this.$t('updateSuccess'))
          this.hasUpdatedFields = true // 标记已经更新过字段
          if (this.is_inner) {
            this.getDiscovery() // Refresh list if inner
          }
          return // Don't close drawer
        }

        // Normal save or update fields in default mode
        this.handleClose()
        if (this.is_inner) {
          this.$message.success(this.$t('saveSuccess'))
          this.getDiscovery() // Refresh list if inner
        } else {
          this.$emit('updateNotInner', res) // Notify parent if not inner
        }
      } catch (error) {
         console.error('Error submitting discovery rule:', error)
         // Optionally show an error message to the user
         this.$message.error(this.$t('saveFailed') || 'Save failed') // Assuming a translation key exists
      } finally {
         this.submitLoading = false // 结束加载动画
      }
    },
    changeMode(e) {
      const newMode = e.target.value
      // Update script content based on the new mode *before* initializing editor
      // When editing, prefer existing script if mode matches, otherwise load default
      if (newMode === 'prefect') {
        this.plugin_script = (this.type === 'edit' && this.ruleData?.mode === 'prefect' && this.ruleData?.plugin_script)
                             ? this.ruleData.plugin_script
                             : this.default_plugin_prefect
      } else if (newMode === 'plugin') {
        this.plugin_script = (this.type === 'edit' && this.ruleData?.mode === 'plugin' && this.ruleData?.plugin_script)
                             ? this.ruleData.plugin_script
                             : this.default_plugin_script
      } else {
        this.plugin_script = '' // No script for default mode
      }

      // Initialize CodeMirror only if the new mode requires it
      if (newMode === 'plugin' || newMode === 'prefect') {
        this.$nextTick(() => {
          // Check if the ref exists (it might not if v-if was false just before)
          if (this.$refs.codemirror) {
            this.$refs.codemirror.initCodeMirror(this.plugin_script)
          } else {
             setTimeout(() => {
                if (this.$refs.codemirror) {
                  this.$refs.codemirror.initCodeMirror(this.plugin_script)
                } else {
                   console.warn('Codemirror ref still not found after delay in changeMode.')
                }
             }, 50) // Small delay
          }
        })
      }
    },
    changeCodeContent(value) {
      this.plugin_script = value && value.replace('\t', '    ')
    },
  },
}
</script>

<style></style>
