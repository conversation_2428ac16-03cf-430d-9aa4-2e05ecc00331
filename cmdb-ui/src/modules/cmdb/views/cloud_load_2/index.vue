<template>
  <div class="cloud-load-container">
    <a-card :bordered="false">
      <a-tabs default-active-key="1">
        <a-tab-pane key="1" :tab="$t('cmdb.cloud_load.loadData')">
          <load-data-tab></load-data-tab>
        </a-tab-pane>
        <a-tab-pane key="2" :tab="$t('cmdb.cloud_load.importHistory')">
          <history-tab></history-tab>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script>
import LoadDataTab from './modules/loadDataTab.vue'
import HistoryTab from './modules/historyTab.vue'

export default {
  name: 'CloudLoad2',
  components: {
    LoadDataTab,
    HistoryTab
  },
}
</script>

<style lang="less" scoped>
.cloud-load-container {
  .table-page-search-wrapper {
    margin-bottom: 24px;
  }
}
</style>
