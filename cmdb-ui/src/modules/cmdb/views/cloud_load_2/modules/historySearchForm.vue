<template>
  <div class="table-page-search-wrapper">
    <a-form layout="inline">
      <!-- CI类型选择 -->
      <a-row :gutter="48">
        <a-col :md="6" :sm="24">
          <a-form-item :label="$t('cmdb.cloud_load.ciType')">
            <a-select
              showSearch
              v-model="queryParams.type_id"
              :placeholder="$t('cmdb.cloud_load.selectCiType')"
              style="width: 100%"
              :filter-option="filterOption"
            >
              <a-select-opt-group v-for="group in ciTypeGroups" :key="group.name" :label="group.name">
                <a-select-option v-for="ciType in group.ci_types" :key="ciType.name" :value="ciType.id">
                  {{ ciType.alias }}
                </a-select-option>
              </a-select-opt-group>
            </a-select>
          </a-form-item>
        </a-col>

        <!-- 状态选择 -->
        <a-col :md="6" :sm="24">
          <a-form-item :label="$t('cmdb.cloud_load.status')">
            <a-select
              v-model="queryParams.status"
              :placeholder="$t('cmdb.cloud_load.selectStatus')"
              style="width: 100%"
              allowClear
            >
              <a-select-option
                v-for="option in statusOptions"
                :key="option.value"
                :value="option.value"
              >
                {{ $t(`cmdb.cloud_load.${option.label}`) }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <!-- 用户选择 -->
        <a-col :md="6" :sm="24">
          <a-form-item :label="$t('cmdb.history.user')">
            <a-select
              v-model="queryParams.username"
              :placeholder="$t('cmdb.history.pleaseSelect')"
              style="width: 100%"
              showSearch
              :filter-option="filterOption"
              allowClear
            >
              <a-select-option
                v-for="user in userList"
                :key="user.value"
                :value="user.value"
              >
                {{ user.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <!-- 文件名搜索 -->
        <a-col :md="6" :sm="24">
          <a-form-item :label="$t('cmdb.cloud_load.fileName')">
            <a-input
              v-model="queryParams.file_name"
              :placeholder="$t('cmdb.cloud_load.enterFileName')"
              allowClear
            />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 操作按钮 -->
      <div class="table-page-search-submitButtons">
        <a-space>
          <a-button type="primary" @click="handleSearch" :loading="loading">
            {{ $t('cmdb.cloud_load.search') }}
          </a-button>
          <a-button @click="handleReset">
            {{ $t('cmdb.cloud_load.reset') }}
          </a-button>
        </a-space>
      </div>
    </a-form>
  </div>
</template>

<script>
import { getCITypeGroupsWithLoadAttrs } from '@/modules/cmdb/api/ciTypeGroup'
import { HistoryStatusOptions } from '@/modules/cmdb/constants'
import { getUsers } from '@/modules/cmdb/api/cloudHistory'

export default {
  name: 'HistorySearchForm',
  data() {
    return {
      loading: false,
      queryParams: {
        type_id: undefined,
        status: undefined,
        file_name: '',
        username: '',
        page: 1,
        page_size: 10
      },
      ciTypeGroups: [],
      statusOptions: HistoryStatusOptions,
      userList: []
    }
  },

  async created() {
    await Promise.all([
      this.loadCITypeGroups(),
      this.loadUserList()
    ])
  },

  methods: {
    async loadCITypeGroups() {
      try {
        const response = await getCITypeGroupsWithLoadAttrs()
        this.ciTypeGroups = response
      } catch (error) {
        console.error('Failed to load CI type groups:', error)
        this.$message.error(this.$t('cmdb.cloud_load.loadCiTypesFailed'))
      }
    },

    async loadUserList() {
      try {
        const res = await getUsers()
        this.userList = res.map((x) => {
          return {
            label: x.nickname,
            value: x.nickname
          }
        })
      } catch (error) {
        console.error('Failed to load user list:', error)
        this.$message.error(this.$t('cmdb.cloud_load.loadUsersFailed'))
      }
    },

    handleSearch() {
      this.loading = true
      this.$emit('search', { ...this.queryParams })
      this.loading = false
    },

    handleReset() {
      this.queryParams = {
        type_id: undefined,
        status: undefined,
        file_name: '',
        user: '',
        page: 1,
        page_size: 10
      }
      this.$emit('reset')
    },

    filterOption(input, option) {
      if (option.componentOptions.propsData.label) {
        return false
      }
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      )
    }
  }
}
</script>
