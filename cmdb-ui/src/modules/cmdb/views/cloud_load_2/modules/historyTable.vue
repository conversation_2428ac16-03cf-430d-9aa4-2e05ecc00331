<template>
  <div>
    <vxe-table
      ref="xTable"
      :loading="loading"
      border
      size="small"
      show-overflow="tooltip"
      :data="tableData"
      :max-height="`${windowHeight - windowHeightMinus}px`"
      :scroll-y="{ enabled: false }"
      :auto-resize="true"
      class="ops-unstripe-table"
    >
      <vxe-column field="id" :title="$t('ID')" width="80"></vxe-column>
      <vxe-column field="file_name" :title="$t('cmdb.cloud_load.fileName')" min-width="120"></vxe-column>
      <vxe-column field="status" :title="$t('cmdb.cloud_load.status')" width="110">
        <template v-slot="{ row }">
          <a-tag :color="getStatusColor(row.status)">
            {{ getStatusText(row.status) }}
          </a-tag>
        </template>
      </vxe-column>
      <vxe-column field="total_count" :title="$t('cmdb.cloud_load.totalCount')" width="100"></vxe-column>
      <vxe-column field="success_count" :title="$t('cmdb.cloud_load.successCount')" width="100"></vxe-column>
      <vxe-column field="error_count" :title="$t('cmdb.cloud_load.errorCount')" width="100"></vxe-column>
      <vxe-column field="start_time" :title="$t('cmdb.cloud_load.startTime')" width="160">
        <template v-slot="{ row }">
          {{ formatDateTime(row.start_time) }}
        </template>
      </vxe-column>
      <vxe-column field="end_time" :title="$t('cmdb.cloud_load.endTime')" width="160">
        <template v-slot="{ row }">
          {{ formatDateTime(row.end_time) }}
        </template>
      </vxe-column>
      <vxe-column field="user" :title="$t('cmdb.cloud_load.user')" width="100"></vxe-column>
      <vxe-column field="error_message" :title="$t('cmdb.cloud_load.errorMessage')" min-width="200">
        <template v-slot="{ row }">
          <a-tooltip v-if="row.error_message" :title="row.error_message">
            <span class="error-message">{{ row.error_message }}</span>
          </a-tooltip>
        </template>
      </vxe-column>
    </vxe-table>

    <div :style="{ textAlign: 'right', marginTop: '4px' }">
      <a-pagination
        :showSizeChanger="true"
        :current="pagination.current"
        size="small"
        :total="pagination.total"
        show-quick-jumper
        :page-size="pagination.pageSize"
        :page-size-options="pageSizeOptions"
        @showSizeChange="handleSizeChange"
        :show-total="
          (total, range) =>
            $t('pagination.total', {
              range0: range[0],
              range1: range[1],
              total,
            })
        "
        @change="handlePageChange"
      >
        <template slot="buildOptionText" slot-scope="props">
          <span v-if="props.value !== '100000'">{{ props.value }}{{ $t('itemsPerPage') }}</span>
          <span v-if="props.value === '100000'">{{ $t('cmdb.ci.all') }}</span>
        </template>
      </a-pagination>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import moment from 'moment'
import { queryLoadHistory } from '@/modules/cmdb/api/loadHistory'
import { HistoryStatusEnum, HistoryStatusOptions } from '@/modules/cmdb/constants'

export default {
  name: 'HistoryTable',
  data() {
    return {
      loading: false,
      tableData: [],
      pagination: {
        current: 1,
        pageSize: 50,
        total: 0
      },
      pageSizeOptions: ['50', '100', '200', '100000'],
      currentParams: null
    }
  },

  computed: {
    ...mapState({
      windowHeight: state => state.windowHeight
    }),
    windowHeightMinus() {
      return 280
    }
  },

  methods: {
    async search(params) {
      this.loading = true
      this.currentParams = params
      try {
        const res = await queryLoadHistory({
          ...params,
          page: this.pagination.current,
          page_size: this.pagination.pageSize
        })
        this.processQueryResult(res)
      } catch (error) {
        console.error('Query failed:', error)
        this.$message.error(this.$t('cmdb.cloud_load.queryFailed'))
      } finally {
        this.loading = false
      }
    },

    processQueryResult(result) {
      this.pagination.total = result.total || 0
      this.pagination.current = result.page || 1
      this.pagination.pageSize = result.page_size || 50
      this.tableData = result.items || []
    },

    handlePageChange(page) {
      this.pagination.current = page
      if (this.currentParams) {
        this.search({
          ...this.currentParams,
          page
        })
      }
    },

    handleSizeChange(current, size) {
      this.pagination.pageSize = size
      this.pagination.current = 1
      if (this.currentParams) {
        this.search({
          ...this.currentParams,
          page: 1,
          page_size: size
        })
      }
    },

    reset() {
      this.tableData = []
      this.pagination = {
        current: 1,
        pageSize: 50,
        total: 0
      }
      this.currentParams = null
    },

    formatDateTime(datetime) {
      return datetime ? moment(datetime).format('YYYY-MM-DD HH:mm:ss') : ''
    },

    getStatusColor(status) {
      const colors = {
        [HistoryStatusEnum.BEGINSYNC]: 'blue',
        [HistoryStatusEnum.PENDING]: 'blue',
        [HistoryStatusEnum.PROCESSING]: 'orange',
        [HistoryStatusEnum.COMPLETED]: 'green',
        [HistoryStatusEnum.FAILED]: 'red'
      }
      return colors[status] || 'default'
    },

    getStatusText(status) {
      return HistoryStatusOptions.find(item => item.value === status)?.label || status
    }
  }
}
</script>

<style lang="less" scoped>
.error-message {
  display: inline-block;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
