<template>
  <div class="table-page-search-wrapper">
    <a-form layout="inline">
      <!-- CI类型选择 -->
      <a-row :gutter="48">
        <a-col :md="8" :sm="24">
          <a-form-item :label="$t('cmdb.cloud_load.ciType')">
            <a-select
              showSearch
              v-model="queryParams.type_id"
              :placeholder="$t('cmdb.cloud_load.selectCiType')"
              style="width: 100%"
              @change="handleCiTypeChange"
              :filter-option="filterOption"
            >
              <a-select-opt-group v-for="group in ciTypeGroups" :key="group.name" :label="group.name">
                <a-select-option v-for="ciType in group.ci_types" :key="ciType.name" :value="ciType.id">
                  {{ ciType.alias }}
                </a-select-option>
              </a-select-opt-group>
            </a-select>
          </a-form-item>
        </a-col>

        <!-- 主键查询 -->
        <a-col :md="8" :sm="24">
          <a-form-item :label="$t('cmdb.cloud_load.uniqueValues')">
            <a-input
              v-model="queryParams.unique_values"
              :placeholder="$t('cmdb.cloud_load.enterUniqueValues')"
              style="width: 100%"
              allowClear
            />
          </a-form-item>
        </a-col>
        <!-- 时间段选择 (仅每日数据显示) -->
        <a-col v-if="queryParams.period_type === PeriodTypes.DAILY" :md="8" :sm="24">
          <a-form-item :label="$t('cmdb.batch.selectTimeSlot')">
            <a-select
              v-model="queryParams.time_slot"
              style="width: 100%"
              allowClear
              :placeholder="$t('cmdb.cloud_load.selectTimeSlot')"
            >
              <a-select-option v-for="option in timeSlotOptions" :key="option.value" :value="option.value">
                {{ option.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="48">
        <!-- 负载属性选择 -->
        <a-col :md="8" :sm="24">
          <a-form-item :label="$t('cmdb.cloud_load.loadAttributes')">
            <a-select
              v-model="queryParams.attribute_ids"
              mode="multiple"
              :placeholder="$t('cmdb.cloud_load.selectLoadAttributes')"
              style="width: 100%"
              @change="handleAttributesSelect"
              :maxTagCount="3"
              :maxTagPlaceholder="tagPlaceholder"
            >
              <a-select-option v-for="attr in loadAttributes" :key="attr.id" :value="attr.id">
                {{ attr.alias || attr.name }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <!-- 周期类型选择 -->
        <a-col :md="8" :sm="24">
          <a-form-item :label="$t('cmdb.cloud_load.periodType')">
            <a-select
              v-model="queryParams.period_type"
              style="width: 100%"
              @change="handlePeriodTypeChange"
            >
              <a-select-option :value="PeriodTypes.DAILY">{{ $t('cmdb.cloud_load.daily') }}</a-select-option>
              <a-select-option :value="PeriodTypes.MONTHLY">{{ $t('cmdb.cloud_load.monthly') }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <!-- 时间范围选择 -->
        <a-col :md="8" :sm="24">
          <a-form-item>
            <div class="period-selector">
              <div class="picker-wrapper">
                <a-range-picker
                  v-if="queryParams.period_type === PeriodTypes.DAILY"
                  v-model="dateRange"
                  :placeholder="[$t('cmdb.cloud_load.startDate'), $t('cmdb.cloud_load.endDate')]"
                  @change="handleDateRangeChange"
                  style="width: 100%"
                />
                <a-range-picker
                  v-else
                  v-model="dateRange"
                  :placeholder="[$t('cmdb.cloud_load.startMonth'), $t('cmdb.cloud_load.endMonth')]"
                  :mode="['month', 'month']"
                  format="YYYY-MM"
                  @panelChange="handleMonthRangeChange"
                  style="width: 100%"
                />
              </div>
              <div class="period-shortcuts">
                <a-button type="link" @click="selectCurrentMonth">{{ $t('cmdb.cloud_load.currentMonth') }}</a-button>
                <a-button type="link" @click="selectLastMonth">{{ $t('cmdb.cloud_load.lastMonth') }}</a-button>
              </div>
            </div>
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 操作按钮 -->
      <div class="table-page-search-submitButtons">
        <a-space>
          <a-button type="primary" @click="handleSearch" :loading="loading">
            {{ $t('cmdb.cloud_load.search') }}
          </a-button>
          <a-button @click="handleReset">
            {{ $t('cmdb.cloud_load.reset') }}
          </a-button>
          <a-button v-if="showExportButton" @click="handleExport">
            {{ $t('cmdb.cloud_load.export') }}
          </a-button>
        </a-space>
      </div>
    </a-form>
  </div>
</template>

<script>
import { PeriodTypes, TimeSlotOptions } from '@/modules/cmdb/constants'
import { getCITypeGroupsWithLoadAttrs } from '@/modules/cmdb/api/ciTypeGroup'
import { getLoadAttrByCITypeId } from '@/modules/cmdb/api/loadRelation'
import { searchPermResourceByRoleId } from '@/modules/acl/api/permission'
import { mapState } from 'vuex'
import moment from 'moment'

export default {
  name: 'SearchForm',
  data() {
    return {
      PeriodTypes,
      loading: false,
      dateRange: [],
      queryParams: {
        type_id: undefined,
        unique_values: '',
        attribute_ids: [],
        period_type: PeriodTypes.DAILY,
        time_slot: undefined, // 时间段参数
        start_period: '',
        end_period: '',
        page: 1,
        page_size: 10
      },
      ciTypeGroups: [],
      ciTypeList: [],
      loadAttributes: [],
      showExportButton: false,
      timeSlotOptions: TimeSlotOptions // 时间段选项
    }
  },

  computed: {
    ...mapState({
      rid: (state) => state.user.rid,
    }),
    tagPlaceholder() {
      return (h) => h('span', {}, `+${this.queryParams.attribute_ids.length - 3}`)
    }
  },

  async created() {
    await this.loadCITypeGroups()
  },

  methods: {
    async loadCITypeGroups() {
      try {
        const { resources } = await searchPermResourceByRoleId(this.rid, {
          resource_type_id: 'CIType',
          app_id: 'cmdb',
        })

        const res = await getCITypeGroupsWithLoadAttrs({ need_other: true })
        this.ciTypeGroups = res || []

        this.ciTypeList = this.ciTypeGroups.reduce((acc, group) => {
          if (!Array.isArray(group.ci_types)) {
            console.warn(`组 ${group.name} 的ci_types无效`)
            return acc
          }

          const filteredTypes = group.ci_types.filter((type) => {
            const _findRe = resources.find((re) => re.name === type.name)
            return _findRe?.permissions.includes('read') ?? false
          })
          return [...acc, ...filteredTypes]
        }, [])
      } catch (error) {
        console.error('Failed to load CI type groups:', error)
        this.$message.error(this.$t('cmdb.cloud_load.loadCiTypesFailed'))
      }
    },

    async handleCiTypeChange(value) {
      if (!value) {
        this.queryParams.attribute_ids = []
        this.loadAttributes = []
        this.$emit('attributesChange', [], true, true)
        return
      }

      try {
        const res = await getLoadAttrByCITypeId(value)
        const attrList = Array.isArray(res?.attrs) ? res.attrs : []
        this.loadAttributes = attrList.filter(attr =>
          this.queryParams.period_type === PeriodTypes.DAILY ? !attr.is_monthly : attr.is_monthly
        )
        this.queryParams.attribute_ids = this.loadAttributes.map(attr => attr.id)
        this.$emit('attributesChange', this.loadAttributes, true, true)
      } catch (error) {
        console.error('Failed to load attributes:', error)
        this.$message.error(this.$t('cmdb.cloud_load.loadAttributesFailed'))
      }
    },

    handleAttributesSelect(selectedIds) {
      this.queryParams.attribute_ids = selectedIds
      const selectedAttrs = this.loadAttributes.filter(attr => selectedIds.includes(attr.id))
      this.$emit('attributesChange', selectedAttrs, false, false)
    },

    handlePeriodTypeChange() {
      if (this.queryParams.type_id) {
        this.handleCiTypeChange(this.queryParams.type_id)
      }
      this.dateRange = []
      this.queryParams.start_period = ''
      this.queryParams.end_period = ''
      // 切换到月度时重置时间段
      if (this.queryParams.period_type === PeriodTypes.MONTHLY) {
        this.queryParams.time_slot = undefined
      }
    },

    handleDateRangeChange(dates, dateStrings) {
      this.queryParams.start_period = dateStrings[0]
      this.queryParams.end_period = dateStrings[1]
    },

    handleMonthRangeChange(dates, dateStrings) {
      this.queryParams.start_period = dateStrings[0]
      this.queryParams.end_period = dateStrings[1]
      this.dateRange = dates
    },

    handleSearch() {
      if (!this.validateQuery()) return

      const params = {
        ...this.queryParams,
        attribute_ids: this.queryParams.attribute_ids.join(',')
      }

      if (params.unique_values) {
        params.unique_values = params.unique_values.split(',').map(val => val.trim()).join(',')
      }

      // 只在有时间段值时才传递时间段参数
      if (!params.time_slot) {
        delete params.time_slot
      }

      this.$emit('search', params)
      this.showExportButton = true
    },

    handleReset() {
      this.queryParams = {
        type_id: undefined,
        unique_values: '',
        attribute_ids: [],
        period_type: PeriodTypes.DAILY,
        time_slot: undefined, // 重置时间段
        start_period: '',
        end_period: '',
        page: 1,
        page_size: 10
      }
      this.dateRange = []
      this.loadAttributes = []
      this.$emit('reset', true)
      this.showExportButton = false
    },

    handleExport() {
      this.$emit('export')
    },

    validateQuery() {
      if (!this.queryParams.type_id) {
        this.$message.warning(this.$t('cmdb.cloud_load.selectCiTypeFirst'))
        return false
      }

      if (!this.queryParams.attribute_ids.length) {
        this.$message.warning(this.$t('cmdb.cloud_load.selectAttributes'))
        return false
      }

      if (!this.queryParams.start_period || !this.queryParams.end_period) {
        this.$message.warning(this.$t('cmdb.cloud_load.selectPeriod'))
        return false
      }

      return true
    },

    filterOption(input, option) {
      if (option.componentOptions.propsData.label) {
        return false
      }
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      )
    },

    // 选择本月
    selectCurrentMonth() {
      const currentMonth = moment()
      if (this.queryParams.period_type === PeriodTypes.DAILY) {
        // 本月第一天到今天
        this.dateRange = [moment(currentMonth).startOf('month'), moment()]
        this.queryParams.start_period = this.dateRange[0].format('YYYY-MM-DD')
        this.queryParams.end_period = this.dateRange[1].format('YYYY-MM-DD')
      } else {
        // 本月
        this.dateRange = [currentMonth, currentMonth]
        this.queryParams.start_period = currentMonth.format('YYYY-MM')
        this.queryParams.end_period = currentMonth.format('YYYY-MM')
      }
      // 自动触发查询
      this.$nextTick(() => {
        this.handleSearch()
      })
    },

    // 选择上月
    selectLastMonth() {
      const lastMonth = moment().subtract(1, 'months')
      if (this.queryParams.period_type === PeriodTypes.DAILY) {
        // 上月第一天到最后一天
        this.dateRange = [
          moment(lastMonth).startOf('month'),
          moment(lastMonth).endOf('month')
        ]
        this.queryParams.start_period = this.dateRange[0].format('YYYY-MM-DD')
        this.queryParams.end_period = this.dateRange[1].format('YYYY-MM-DD')
      } else {
        // 上月
        this.dateRange = [lastMonth, lastMonth]
        this.queryParams.start_period = lastMonth.format('YYYY-MM')
        this.queryParams.end_period = lastMonth.format('YYYY-MM')
      }
      // 自动触发查询
      this.$nextTick(() => {
        this.handleSearch()
      })
    }
  }
}
</script>

<style lang="less" scoped>
.table-page-search-wrapper {
  .ant-form-inline {
    .ant-form-item {
      display: flex;
      margin-bottom: 24px;
      margin-right: 0;

      .ant-form-item-control-wrapper {
        flex: 1 1;
        display: inline-block;
        vertical-align: middle;
      }

      > .ant-form-item-label {
        line-height: 32px;
        padding-right: 8px;
        width: auto;
      }
    }
  }

  .table-page-search-submitButtons {
    margin-bottom: 24px;
    white-space: nowrap;
  }
}

.period-selector {
  display: flex;
  align-items: center;
  gap: 8px;

  .picker-wrapper {
    flex: 1;
  }

  .period-shortcuts {
    display: flex;
    gap: 4px;
    white-space: nowrap;
  }
}

// 优化多选下拉框样式
.ant-select-selection--multiple {
  min-height: 32px;
  max-height: 32px;
  overflow: hidden;

  &:hover {
    overflow: auto;
  }
}
</style>
