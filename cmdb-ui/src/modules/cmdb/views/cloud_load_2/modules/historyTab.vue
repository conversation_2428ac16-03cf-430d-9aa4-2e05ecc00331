<template>
  <div>
    <!-- 搜索表单 -->
    <history-search-form
      ref="searchForm"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 数据表格 -->
    <history-table
      ref="historyTable"
    />
  </div>
</template>

<script>
import HistorySearchForm from './historySearchForm.vue'
import HistoryTable from './historyTable.vue'

export default {
  name: 'HistoryTab',
  components: {
    HistorySearchForm,
    HistoryTable
  },

  methods: {
    handleSearch(params) {
      this.$refs.historyTable.search(params)
    },

    handleReset() {
      this.$refs.historyTable.reset()
    }
  }
}
</script>
