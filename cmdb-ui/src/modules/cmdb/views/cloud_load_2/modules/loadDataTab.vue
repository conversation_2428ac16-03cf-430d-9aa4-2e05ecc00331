<template>
  <div>
    <!-- 搜索表单 -->
    <search-form
      ref="searchForm"
      @search="handleSearch"
      @reset="handleReset"
      @attributesChange="handleAttributesChange"
      @export="handleExport"
    />

    <!-- 数据表格 -->
    <ci-table
      ref="ciTable"
      :loadAttributes="loadAttributes"
    />
  </div>
</template>

<script>
import SearchForm from './searchForm.vue'
import CiTable from './ciTable.vue'

export default {
  name: 'LoadDataTab',
  components: {
    SearchForm,
    CiTable
  },

  data() {
    return {
      loadAttributes: []
    }
  },

  methods: {
    handleSearch(params) {
      this.$refs.ciTable.search(params)
    },

    handleReset() {
      this.$refs.ciTable.reset()
    },

    handleAttributesChange(attributes) {
      this.loadAttributes = attributes
    },

    handleExport() {
      this.$refs.ciTable.exportData()
    }
  }
}
</script>

<style lang="less" scoped>
.table-page-search-wrapper {
  margin-bottom: 24px;
}
</style>
