<template>
  <div>
    <vxe-table
      ref="xTable"
      :loading="loading"
      border
      size="small"
      show-overflow="tooltip"
      show-header-overflow="tooltip"
      resizable
      :data="tableData"
      :max-height="`${windowHeight - windowHeightMinus}px`"
      :scroll-y="{ enabled: false }"
      :auto-resize="true"
      class="ops-unstripe-table"
    >
      <vxe-column field="ci_id" :title="$t('cmdb.cloud_load.keyword')" :width="80" fixed="left"></vxe-column>
      <vxe-column field="period" :title="$t('cmdb.cloud_load.period')" :width="100" fixed="left"></vxe-column>
      <vxe-column
        v-for="attr in loadAttributes"
        :key="attr.id"
        :field="attr.name"
        :title="attr.alias || attr.name"
        :min-width="120"
        show-overflow="tooltip"
      ></vxe-column>
    </vxe-table>

    <div :style="{ textAlign: 'right', marginTop: '4px' }">
      <a-pagination
        :showSizeChanger="true"
        :current="pagination.current"
        size="small"
        :total="pagination.total"
        show-quick-jumper
        :page-size="pagination.pageSize"
        :page-size-options="pageSizeOptions"
        @showSizeChange="handleSizeChange"
        :show-total="
          (total, range) =>
            $t('pagination.total', {
              range0: range[0],
              range1: range[1],
              total,
            })
        "
        @change="handlePageChange"
      >
        <template slot="buildOptionText" slot-scope="props">
          <span v-if="props.value !== '100000'">{{ props.value }}{{ $t('itemsPerPage') }}</span>
          <span v-if="props.value === '100000'">{{ $t('cmdb.ci.all') }}</span>
        </template>
      </a-pagination>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import moment from 'moment'
import { queryLoadData } from '@/modules/cmdb/api/loadData'
import ExcelJS from 'exceljs'
import FileSaver from 'file-saver'

export default {
  name: 'CiTable',
  props: {
    loadAttributes: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      pagination: {
        current: 1,
        pageSize: 50,
        total: 0
      },
      pageSizeOptions: ['50', '100', '200', '100000'],
      currentParams: null
    }
  },

  computed: {
    ...mapState({
      windowHeight: state => state.windowHeight
    }),
    windowHeightMinus() {
      return 331
    }
  },

  mounted() {
    window.addEventListener('resize', this.handleResize)
  },

  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },

  watch: {
    loadAttributes: {
      handler(newAttrs, oldAttrs) {
        // 当属性列表变化时，重新计算表格
        this.$nextTick(() => {
          if (this.$refs.xTable) {
            this.$refs.xTable.refreshColumn()
            this.$refs.xTable.recalculate(true)
          }
        })
      },
      deep: true
    }
  },

  methods: {
    async search(params, keepCurrentPage = false) {
      this.loading = true
      this.currentParams = params
      // 只有在不保持当前页的情况下才重置为第一页
      if (!keepCurrentPage) {
        this.pagination.current = 1
      }
      try {
        const res = await queryLoadData({
          ...params,
          page: this.pagination.current,
          page_size: this.pagination.pageSize
        })
        this.processQueryResult(res)
      } catch (error) {
        console.error('Query failed:', error)
        this.$message.error(this.$t('cmdb.cloud_load.queryFailed'))
      } finally {
        this.loading = false
      }
    },

    processQueryResult(result) {
      this.pagination.total = result.total || 0
      this.pagination.current = result.page || 1
      this.pagination.pageSize = result.page_size || 50

      // 转换数据格式以适应表格展示
      this.tableData = []
      if (result.data) {
        Object.entries(result.data).forEach(([ci_id, ciData]) => {
          Object.entries(ciData).forEach(([period, periodData]) => {
            const row = {
              key: `${ci_id}_${period}`,
              ci_id,
              period,
              ...periodData
            }
            this.tableData.push(row)
          })
        })
      }
      // 数据加载完成后重新计算列宽
      this.$nextTick(() => {
        if (this.$refs.xTable) {
          this.$refs.xTable.refreshColumn()
          this.$refs.xTable.recalculate(true)
        }
      })
    },

    handlePageChange(page) {
      this.pagination.current = page
      if (this.currentParams) {
        this.search({
          ...this.currentParams,
          page
        }, true) // 传入true表示保持当前页码
      }
    },

    handleSizeChange(current, size) {
      this.pagination.pageSize = size
      this.pagination.current = 1
      if (this.currentParams) {
        this.search({
          ...this.currentParams,
          page: 1,
          page_size: size
        })
      }
    },

    reset(resetTable = false, clearData = false) {
      if (clearData) {
        this.tableData = []
        this.pagination = {
          current: 1,
          pageSize: 50,
          total: 0
        }
        this.currentParams = null
      }
      if (resetTable && this.$refs.xTable) {
        this.$nextTick(() => {
          this.$refs.xTable.refreshColumn()
          this.$refs.xTable.recalculate(true)
        })
      }
    },

    async exportData() {
      if (!this.currentParams || !this.tableData.length) {
        this.$message.warning(this.$t('cmdb.cloud_load.noDataToExport'))
        return
      }

      try {
        const workbook = new ExcelJS.Workbook()
        const worksheet = workbook.addWorksheet('负载数据')

        // 设置列
        const columns = [
          { header: this.$t('cmdb.cloud_load.keyword'), key: 'ci_id', width: 15 },
          { header: this.$t('cmdb.cloud_load.period'), key: 'period', width: 20 }
        ]

        this.loadAttributes.forEach(attr => {
          columns.push({
            header: attr.alias || attr.name,
            key: attr.name,
            width: 15
          })
        })

        worksheet.columns = columns

        // 添加数据
        this.tableData.forEach(row => {
          worksheet.addRow(row)
        })

        // 导出文件
        const buffer = await workbook.xlsx.writeBuffer()
        const blob = new Blob([buffer], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })
        FileSaver.saveAs(blob, `load-data-${moment().format('YYYYMMDDHHmmss')}.xlsx`)

        this.$message.success(this.$t('cmdb.cloud_load.exportSuccess'))
      } catch (error) {
        console.error('Export failed:', error)
        this.$message.error(this.$t('cmdb.cloud_load.exportFailed'))
      }
    },

    handleResize() {
      this.$refs.xTable.recalculate()
    }
  }
}
</script>

<style lang="less" scoped>
.ops-unstripe-table {
  background: #fff;

  /deep/ .vxe-table--header-wrapper {
    background-color: #fafafa;
  }

  /deep/ .vxe-table--body-wrapper {
    background-color: #fff;
  }

  /deep/ .vxe-table--body {
    td {
      padding: 8px;
    }
  }
}
</style>
