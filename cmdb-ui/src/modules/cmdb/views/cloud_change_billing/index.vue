<template>
  <div class="cmdb-batch-upload" :style="{ height: `${windowHeight - 64}px` }">
    <div class="cmdb-views-header">
      <span>
        <span class="cmdb-views-header-title">{{ $t('cmdb.menu.batchUpload') }}</span>
      </span>
    </div>
    <CiTypeChoice ref="ciTypeChoice" @getCiTypeAttr="showCiType" />
    <p class="cmdb-batch-upload-label"><span>*</span>3. {{ $t('cmdb.batch.uploadFile') }}</p>
    <UploadFileForm
      :isUploading="isUploading"
      :ciType="ciType"
      ref="uploadFileForm"
      @uploadDone="uploadDone"
    ></UploadFileForm>
    <p class="cmdb-batch-upload-label">4. {{ $t('cmdb.batch.dataPreview') }}</p>
    <CiUploadTable :ciTypeAttrs="ciTypeAttrs" ref="ciUploadTable" :uploadData="uploadData"></CiUploadTable>
    <div class="cmdb-batch-upload-action">
      <a-space size="large">
        <a-button :disabled="!(ciType && uploadData.length)" @click="handleUpload" type="primary">{{
          $t('upload')
        }}</a-button>
        <a-button @click="handleCancel">{{ $t('cancel') }}</a-button>
        <a-button v-if="hasError && !isUploading" @click="downloadError" type="primary">{{
          $t('cmdb.batch.downloadFailed')
        }}</a-button>
      </a-space>
    </div>
    <UploadResult
      v-if="ciType"
      ref="uploadResult"
      :upLoadData="uploadData"
      :ciType="ciType"
      :unique-field="uniqueField"
      :isUploading="isUploading"
      @uploadResultDone="uploadResultDone"
      @uploadResultError="uploadResultError"
    ></UploadResult>
  </div>
</template>

<script>
import moment from 'moment'
import { mapState } from 'vuex'
import CiTypeChoice from './modules/CiTypeChoice'
import CiUploadTable from './modules/CiUploadTable'
import UploadFileForm from './modules/UploadFileForm'
import UploadResult from './modules/UploadResult'
import { filterNull } from '@/modules/cmdb/api/batch'

export default {
  name: 'CloudChangeBilling',
  components: {
    CiTypeChoice,
    CiUploadTable,
    UploadFileForm,
    UploadResult,
  },
  data() {
    return {
      ciTypeAttrs: {},
      uploadData: [],
      ciType: 0,
      uniqueField: '',
      uniqueId: 0,
      isUploading: false,
      hasError: false,
    }
  },
  computed: {
    ...mapState({
      windowHeight: (state) => state.windowHeight,
    }),
  },
  methods: {
    showCiType(message) {
      this.ciTypeAttrs = message ?? {}
      this.ciType = message?.type_id ?? 0
      this.uniqueField = message?.unique ?? ''
      this.uniqueId = message?.unique_id ?? 0
    },
    uploadDone(dataList) {
      const _uploadData = filterNull(dataList).map((item, i) => {
        if (i > 0) {
          const _ele = {}
          item.forEach((ele, j) => {
            if (ele !== undefined && ele !== null) {
              const _find = this.ciTypeAttrs.attributes.find(
                (attr) => attr.alias === dataList[0][j] || attr.name === dataList[0][j]
              )
              // console.log('find:', _find)
              // if (_find?.name === 'jifeitiaozheng_date' && !!ele) {
              //   // 跳出本次循环
              //   return
              // }
              if (_find?.value_type === '4' && typeof ele === 'number') {
                _ele[dataList[0][j]] = moment(Math.round((ele - 25569) * 86400 * 1000 - 28800000)).format('YYYY-MM-DD')
              } else if (_find?.value_type === '3' && typeof ele === 'number') {
                _ele[dataList[0][j]] = moment(Math.round((ele - 25569) * 86400 * 1000 - 28800000)).format(
                  'YYYY-MM-DD HH:mm:ss'
                )
              } else if (_find?.value_type === '5' && typeof ele === 'number') {
                _ele[dataList[0][j]] = moment(Math.round(ele * 86400 * 1000 - 28800000)).format('HH:mm:ss')
              } else if (_find) {
                _ele[dataList[0][j]] = ele
              } else {
                console.log('data', j)
                this.$message.warning(this.$t('cmdb.batch.undefinedAttribute'))
              }
            }
          })
          return _ele
        }
        return item
      })
      this.uploadData = _uploadData.slice(1)
      this.hasError = false
      this.isUploading = false
    },
    handleUpload() {
      if (!this.ciType) {
        this.$message.error(this.$t('cmdb.batch.unselectCIType'))
        return
      }
      if (this.uploadData && this.uploadData.length > 0) {
        this.isUploading = true
        this.$nextTick(() => {
          this.$refs.uploadResult.upload2Server()
        })
      } else {
        this.$message.error(this.$t('cmdb.batch.pleaseUploadFile'))
      }
    },
    handleCancel() {
      if (!this.isUploading) {
        this.showCiType(null)
        this.$refs.ciTypeChoice.selectNum = undefined
        this.hasError = false
      } else {
        this.$message.warning(this.$t('cmdb.batch.batchUploadCanceled'))
        this.isUploading = false
      }
    },
    uploadResultDone() {
      this.isUploading = false
    },
    uploadResultError(index) {
      this.hasError = true
      this.$refs.ciUploadTable.uploadResultError(index)
    },
    downloadError() {
      this.$refs.ciUploadTable.downloadError()
    },
  },
}
</script>
<style lang="less">
@import '../index.less';
.cmdb-batch-upload-label {
  color: @text-color_1;
  font-weight: bold;
  white-space: pre;
  > span {
    color: red;
  }
}
</style>
<style lang="less" scoped>

.cmdb-batch-upload {
  margin-bottom: -24px;
  padding: 20px;
  background-color: #fff;
  border-radius: @border-radius-box;
  overflow: auto;
  .cmdb-batch-upload-action {
    width: 50%;
    margin: 12px 0;
  }
}
</style>
