<template>
  <a-modal
    width="1200px"
    :title="`${type === 'add' ? $t('cmdb.custom_dashboard.newChart') : $t('cmdb.custom_dashboard.editChart')}`"
    :visible="visible"
    @cancel="handleclose"
    @ok="handleok"
    :bodyStyle="{ paddingTop: 0 }"
  >
    <div class="chart-wrapper">
      <div class="chart-left">
        <a-form-model ref="chartForm" :model="form" :rules="rules" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
          <a-form-model-item :label="$t('cmdb.custom_dashboard.title')" prop="name">
            <a-input v-model="form.name" :placeholder="$t('cmdb.custom_dashboard.titleTips')"></a-input>
          </a-form-model-item>
          <a-form-model-item :label="$t('type')" prop="category" v-if="chartType !== 'metric' && chartType !== 'table'">
            <a-radio-group
              @change="
                (e) => {
                  // 当切换到关系类型(category=2)时，重置聚合方式为count
                  if (e === 2) {
                    this.form.aggregation = 'count'
                  }
                  resetForm()
                }
              "
              :default-value="1"
              v-model="form.category"
            >
              <a-radio-button :value="Number(key)" :key="key" v-for="key in Object.keys(dashboardCategory)">
                {{ dashboardCategory[key].label }}
              </a-radio-button>
            </a-radio-group>
          </a-form-model-item>
          <a-form-model-item :label="$t('type')" prop="tableCategory" v-if="chartType === 'table'">
            <a-radio-group
              @change="
                () => {
                  resetForm()
                }
              "
              :default-value="1"
              v-model="form.tableCategory"
            >
              <a-radio-button :value="1">
                {{ $t('cmdb.custom_dashboard.calcIndicators') }}
              </a-radio-button>
              <a-radio-button :value="2">
                {{ $t('cmdb.menu.ciTable') }}
              </a-radio-button>
            </a-radio-group>
          </a-form-model-item>

          <a-form-model-item
            v-if="(chartType !== 'table' && form.category !== 2) || (chartType === 'table' && form.tableCategory === 1) || chartType === 'metric'"
            :label="$t('cmdb.ciType.ciType')"
            prop="type_ids"
          >
            <a-select
              show-search
              optionFilterProp="children"
              @change="changeCIType"
              v-model="form.type_ids"
              :placeholder="$t('cmdb.ciType.selectCIType')"
              mode="multiple"
            >
              <a-select-option v-for="ci_type in ci_types" :key="ci_type.id" :value="ci_type.id">{{
                ci_type.alias || ci_type.name
              }}</a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item v-if="chartType !== 'metric' && form.category === 2" :label="$t('cmdb.ciType.ciType')" prop="type_id">
            <a-select
              show-search
              optionFilterProp="children"
              @change="changeCIType"
              v-model="form.type_id"
              :placeholder="$t('cmdb.ciType.selectCIType')"
            >
              <a-select-option v-for="ci_type in ci_types" :key="ci_type.id" :value="ci_type.id">{{
                ci_type.alias || ci_type.name
              }}</a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item
            :label="$t('cmdb.custom_dashboard.dimensions')"
            prop="attr_ids"
            v-if="chartType === 'metric' || (['bar', 'line', 'pie'].includes(chartType) && form.category === 1) || chartType === 'table'"
          >
            <a-select
              :filter-option="filterOption"
              @change="changeAttr"
              v-model="form.attr_ids"
              :placeholder="$t('cmdb.custom_dashboard.selectDimensions')"
              mode="multiple"
              show-search
            >
              <a-select-option
                v-for="attr in commonAttributes.filter((attr) => !attr.is_password && attr.value_type !== '6')"
                :key="attr.id"
                :value="attr.id"
              >{{ attr.alias || attr.name }}</a-select-option
              >
            </a-select>
          </a-form-model-item>

          <!-- 聚合方式选择 -->
          <a-form-model-item
            :label="$t('cmdb.custom_dashboard.aggregationType')"
            prop="aggregation"
            v-if="showAggregationSelector"
            class="aggregation-form-item"
          >
            <a-tooltip v-if="aggregationTooltip" :title="aggregationTooltip" placement="topLeft">
              <a-radio-group class="chart-width" style="width:100%;" v-model="form.aggregation" :disabled="!isValueAttrAggregatable">
                <a-radio-button
                  v-for="agg in aggregationTypeOptions"
                  :key="agg.value"
                  :value="agg.value"
                  :disabled="!isValueAttrAggregatable && agg.value !== 'count'"
                >
                  {{ agg.label }}
                </a-radio-button>
              </a-radio-group>
            </a-tooltip>
            <a-radio-group v-else class="chart-width" style="width:100%;" v-model="form.aggregation" :disabled="!isValueAttrAggregatable">
              <a-radio-button
                v-for="agg in aggregationTypeOptions"
                :key="agg.value"
                :value="agg.value"
                :disabled="!isValueAttrAggregatable && agg.value !== 'count'"
              >
                {{ agg.label }}
              </a-radio-button>
            </a-radio-group>
          </a-form-model-item>

          <a-form-model-item
            prop="type_ids"
            :label="$t('cmdb.custom_dashboard.childCIType')"
            v-if="['bar', 'line', 'pie'].includes(chartType) && form.category === 2"
          >
            <a-select
              show-search
              optionFilterProp="children"
              mode="multiple"
              v-model="form.type_ids"
              :placeholder="$t('cmdb.ciType.selectCIType')"
            >
              <a-select-opt-group
                v-for="(key, index) in Object.keys(level2children)"
                :key="key"
                :label="$t('cmdb.custom_dashboard.level') + `${index + 1}`"
              >
                <a-select-option
                  @click="(e) => clickLevel2children(e, citype, index + 1)"
                  v-for="citype in level2children[key]"
                  :key="citype.id"
                  :value="citype.id"
                >
                  {{ citype.alias || citype.name }}
                </a-select-option>
              </a-select-opt-group>
            </a-select>
          </a-form-model-item>
          <div :class="{ 'chart-left-preview': true, 'chart-left-preview-empty': !isShowPreview }">
            <span
              class="chart-left-preview-operation"
              @click="showPreview"
            ><a-icon type="play-circle" /> {{ $t('cmdb.custom_dashboard.preview') }}</span
            >
            <template v-if="isShowPreview">
              <div v-if="chartType !== 'metric'" class="cmdb-dashboard-grid-item-title">
                <template v-if="form.showIcon && ciType">
                  <template v-if="ciType.icon">
                    <img
                      v-if="ciType.icon.split('$$')[2]"
                      :src="`/api/common-setting/v1/file/${ciType.icon.split('$$')[3]}`"
                    />
                    <ops-icon
                      v-else
                      :style="{
                        color: ciType.icon.split('$$')[1],
                      }"
                      :type="ciType.icon.split('$$')[0]"
                    />
                  </template>
                  <span :style="{ color: '#2f54eb' }" v-else>{{ ciType.name[0].toUpperCase() }}</span>
                </template>
                <span :style="{ color: '#000' }"> {{ form.name }}</span>
              </div>
              <div
                class="chart-left-preview-box"
                :style="{
                  height: chartType === 'metric' ? '120px' : '',
                  marginTop: chartType === 'metric' ? '80px' : '',
                  background:
                    chartType === 'metric'
                      ? Array.isArray(bgColor)
                        ? `linear-gradient(to bottom, ${bgColor[0]} 0%, ${bgColor[1]} 100%)`
                        : bgColor
                      : '#fafafa',
                }"
              >
                <div v-if="chartType === 'metric'" :style="{ color: fontColor }">{{ form.name }}</div>
                <Chart
                  :ref="`chart_${item.id}`"
                  :chartId="item.id"
                  :data="previewData"
                  :category="form.category"
                  :options="{
                    ...item.options,
                    name: form.name,
                    fontColor: fontColor,
                    bgColor: bgColor,
                    chartType: chartType,
                    showIcon: form.showIcon,
                    barDirection: barDirection,
                    barStack: barStack,
                    chartColor: chartColor,
                    type_ids: form.type_ids,
                    attr_ids: form.attr_ids,
                    isShadow: isShadow,
                    ret: form.tableCategory === 2 ? 'cis' : '',
                  }"
                  :editable="false"
                  :ci_types="ci_types"
                  :type_id="form.type_id || form.type_ids"
                  isPreview
                />
              </div>
            </template>
          </div>
          <a-form-model-item
            :label="$t('cmdb.custom_dashboard.showIcon')"
            prop="showIcon"
            :label-col="{ span: 5 }"
            :wrapper-col="{ span: 18 }"
          >
            <a-switch v-model="form.showIcon"></a-switch>
          </a-form-model-item>
        </a-form-model>
      </div>

      <div class="chart-right">
        <h4>{{ $t('cmdb.custom_dashboard.chartType') }}</h4>
        <div class="chart-right-type">
          <div
            :class="{ 'chart-right-type-box': true, 'chart-right-type-box-selected': chartType === t.value }"
            v-for="t in chartTypeList"
            :key="t.value"
            @click="changeChartType(t)"
          >
            <ops-icon :type="`cmdb-${t.value === 'metric' ? 'count' : t.value}`" />
            <span>{{ t.label }}</span>
          </div>
        </div>
        <h4>{{ $t('cmdb.custom_dashboard.dataFilter') }}</h4>
        <FilterComp
          ref="filterComp"
          :isDropdown="false"
          :canSearchPreferenceAttrList="attributes"
          @setExpFromFilter="setExpFromFilter"
          :expression="filterExp ? `q=${filterExp}` : ''"
        />
        <h4>{{ $t('cmdb.custom_dashboard.format') }}</h4>
        <a-form-model :colon="false" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
          <a-form-model-item :label="$t('cmdb.custom_dashboard.fontColor')" v-if="chartType === 'metric'">
            <ColorPicker
              v-model="fontColor"
              :colorList="[
                '#1D2129',
                '#4E5969',
                '#103C93',
                '#86909C',
                '#ffffff',
                '#C9F2FF',
                '#FFEAC0',
                '#D6FFE6',
                '#F2DEFF',
              ]"
            />
          </a-form-model-item>
          <a-form-model-item :label="$t('cmdb.custom_dashboard.backgroundColor')" v-if="chartType === 'metric'">
            <ColorPicker
              v-model="bgColor"
              :colorList="[
                ['#6ABFFE', '#5375EB'],
                ['#C69EFF', '#A377F9'],
                ['#85EBC9', '#4AB8D8'],
                ['#FEB58B', '#DF6463'],
                '#ffffff',
                '#FFFBF0',
                '#FFF1EC',
                '#E5FFFE',
                '#E5E7FF',
              ]"
            />
          </a-form-model-item>
          <a-form-model-item :label="$t('cmdb.custom_dashboard.chartColor')" v-else-if="chartType !== 'table'">
            <ColorListPicker v-model="chartColor" />
          </a-form-model-item>
          <a-form-model-item :label="$t('cmdb.custom_dashboard.chartLength') + `(%)`">
            <a-radio-group class="chart-width" style="width:100%;" v-model="width">
              <a-radio-button :value="3">
                25
              </a-radio-button>
              <a-radio-button :value="6">
                50
              </a-radio-button>
              <a-radio-button :value="9">
                75
              </a-radio-button>
              <a-radio-button :value="12">
                100
              </a-radio-button>
            </a-radio-group>
          </a-form-model-item>
          <a-form-model-item :label="$t('cmdb.custom_dashboard.barType')" v-if="chartType === 'bar'">
            <a-radio-group v-model="barStack">
              <a-radio value="total">
                {{ $t('cmdb.custom_dashboard.stackedBar') }}
              </a-radio>
              <a-radio value="">
                {{ $t('cmdb.custom_dashboard.multipleSeriesBar') }}
              </a-radio>
            </a-radio-group>
          </a-form-model-item>
          <a-form-model-item :label="$t('cmdb.custom_dashboard.direction')" v-if="chartType === 'bar'">
            <a-radio-group v-model="barDirection">
              <a-radio value="x"> X {{ $t('cmdb.custom_dashboard.axis') }} </a-radio>
              <a-radio value="y"> Y {{ $t('cmdb.custom_dashboard.axis') }} </a-radio>
            </a-radio-group>
          </a-form-model-item>
          <a-form-model-item :label="$t('cmdb.custom_dashboard.lowerShadow')" v-if="chartType === 'line'">
            <a-switch v-model="isShadow" />
          </a-form-model-item>
        </a-form-model>
      </div>
    </div>
  </a-modal>
</template>

<script>
import Chart from './chart.vue'
import { dashboardCategory, aggregationTypes } from './constant'
import { postCustomDashboard, putCustomDashboard, postCustomDashboardPreview } from '../../api/customDashboard'
import { getCITypeAttributesByTypeIds, getCITypeCommonAttributesByTypeIds } from '../../api/CITypeAttr'
import { getRecursive_level2children } from '../../api/CITypeRelation'
import { getLastLayout } from '../../utils/helper'
import FilterComp from '@/components/CMDBFilterComp'
import ColorPicker from './colorPicker.vue'
import ColorListPicker from './colorListPicker.vue'
import { isAggregatableType } from '../../utils/const'

export default {
  name: 'ChartForm',
  components: { Chart, FilterComp, ColorPicker, ColorListPicker },
  props: {
    ci_types: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      visible: false,
      attributes: [],
      type: 'add',
      form: {
        category: 1,
        tableCategory: 1,
        name: undefined,
        type_id: undefined,
        type_ids: undefined,
        attr_ids: undefined,
        level: undefined,
        showIcon: false,
        // 聚合方式
        aggregation: 'count'
      },
      rules: {
        category: [{ required: true, trigger: 'change' }],
        name: [{ required: true, message: this.$t('cmdb.custom_dashboard.titleTips') }],
        type_id: [{ required: true, message: this.$t('cmdb.ciType.selectCIType'), trigger: 'change' }],
        type_ids: [{ required: true, message: this.$t('cmdb.ciType.selectCIType'), trigger: 'change' }],
        attr_ids: [
          { required: true, message: this.$t('cmdb.ciType.selectCITypeAttributes'), trigger: 'change' },
          {
            validator: (_, value, callback) => {
              // 检查是否是默认类型，且需要至少2个维度
              const isDefaultType = this.form.category === 1
              const needsMinTwoDimensions = isDefaultType && ['bar', 'line', 'pie', 'table'].includes(this.chartType)

              if (needsMinTwoDimensions && (!value || value.length < 2)) {
                callback(new Error(this.$t('cmdb.custom_dashboard.needsMinTwoDimensions')))
              } else {
                callback()
              }
            },
            trigger: 'submit'
          }
        ],
        level: [{ required: true, message: this.$t('cmdb.custom_dashboard.levelTips') }],
        showIcon: [{ required: false }],
      },
      item: {},
      chartType: 'metric', // table,bar,line,pie,metric (原count)
      width: 3,
      fontColor: '#ffffff',
      bgColor: ['#6ABFFE', '#5375EB'],
      chartColor: '#5DADF2,#86DFB7,#5A6F96,#7BD5FF,#FFB980,#4D58D6,#D9B6E9,#8054FF', // chart color
      isShowPreview: false,
      filterExp: undefined,
      previewData: null,
      barStack: 'total',
      barDirection: 'y',
      commonAttributes: [],
      level2children: {},
      isShadow: false,
      // 聚合相关数据
      numericAttributes: [] // 数值型属性列表
    }
  },
  computed: {
    ciType() {
      if (this.form.type_id || this.form.type_ids) {
        const _find = this.ci_types.find((item) => item.id === this.form.type_id || item.id === this.form.type_ids[0])
        return _find || null
      }
      return null
    },
    chartTypeList() {
      return [
        {
          value: 'metric', // 原 'count'
          label: this.$t('cmdb.custom_dashboard.metric'), // 需要添加翻译
        },
        {
          value: 'bar',
          label: this.$t('cmdb.custom_dashboard.bar'),
        },
        {
          value: 'line',
          label: this.$t('cmdb.custom_dashboard.line'),
        },
        {
          value: 'pie',
          label: this.$t('cmdb.custom_dashboard.pie'),
        },
        {
          value: 'table',
          label: this.$t('cmdb.custom_dashboard.table'),
        },
      ]
    },
    dashboardCategory() {
      return dashboardCategory()
    },
    // 聚合类型选项
    aggregationTypeOptions() {
      return aggregationTypes()
    },
    // 是否显示聚合选择器
    showAggregationSelector() {
      // 当类型为关系(category=2)时，不显示聚合选择器
      if (this.form.category === 2) {
        return false
      }
      // 对于其他图表类型，显示聚合选择器
      return true
    },
    // 获取当前值属性（最后一个选择的属性）
    valueAttr() {
      // 对于所有图表类型，如果有属性选择，使用最后一个选择的属性
      if (this.form.attr_ids && this.form.attr_ids.length > 0) {
        const valueAttrId = this.form.attr_ids[this.form.attr_ids.length - 1]
        // 首先从 attributes 中查找
        let attr = this.attributes.find(attr => attr.id === valueAttrId)
        // 如果在 attributes 中找不到，则从 commonAttributes 中查找
        if (!attr) {
          attr = this.commonAttributes.find(attr => attr.id === valueAttrId)
        }
        return attr || null
      }

      return null
    },
    // 当前值属性是否可聚合
    isValueAttrAggregatable() {
      if (!this.valueAttr) return false
      return isAggregatableType(this.valueAttr.value_type)
    },
    // 是否满足选择聚合方式的条件
    canSelectAggregation() {
      // 对于 metric 类型，需要选择模型和维度
      if (this.chartType === 'metric') {
        return this.form.type_ids &&
               this.form.type_ids.length > 0 &&
               this.form.attr_ids &&
               this.form.attr_ids.length > 0
      }

      // 对于 bar/line/pie 类型的字段值统计，需要选择属性
      if (['bar', 'line', 'pie'].includes(this.chartType) && this.form.category === 1) {
        return this.form.attr_ids && this.form.attr_ids.length > 0
      }

      // 对于关系统计，需要选择目标CI类型
      if (['bar', 'line', 'pie'].includes(this.chartType) && this.form.category === 2) {
        return this.form.type_ids && this.form.type_ids.length > 0
      }

      // 对于表格类型，需要选择属性
      if (this.chartType === 'table' && this.form.tableCategory === 1) {
        return this.form.attr_ids && this.form.attr_ids.length > 0
      }

      return false
    },
    // 聚合选择器的提示信息
    aggregationTooltip() {
      // 首先检查是否满足基本条件（选择了模型和维度）
      if (this.chartType === 'metric') {
        if (!this.form.type_ids || this.form.type_ids.length === 0) {
          return this.$t('cmdb.custom_dashboard.pleaseSelectModel')
        }
        if (!this.form.attr_ids || this.form.attr_ids.length === 0) {
          return this.$t('cmdb.custom_dashboard.pleaseSelectDimension')
        }
      }

      if (['bar', 'line', 'pie'].includes(this.chartType) ||
          (this.chartType === 'table' && this.form.tableCategory === 1)) {
        if (!this.form.attr_ids || this.form.attr_ids.length === 0) {
          return this.$t('cmdb.custom_dashboard.pleaseSelectDimension')
        }
      }

      if (['bar', 'line', 'pie'].includes(this.chartType) && this.form.category === 2) {
        if (!this.form.type_ids || this.form.type_ids.length === 0) {
          return this.$t('cmdb.custom_dashboard.pleaseSelectTargetCIType')
        }
      }

      // 如果满足基本条件，但最后一个维度不是可聚合的，显示相应提示
      if (this.canSelectAggregation && !this.isValueAttrAggregatable) {
        // 如果有值属性但不可聚合
        if (this.valueAttr) {
          return this.$t('cmdb.custom_dashboard.dimensionNotAggregatable')
        } else {
          return this.$t('cmdb.custom_dashboard.onlyNumericDimensionsAggregatable')
        }
      }

      return ''
    },
  },
  inject: ['layout'],
  methods: {
    async open(type, item = {}) {
      this.visible = true
      this.type = type
      this.item = item
      const { category = 0, name, type_id, level } = item
      const chartType = (item.options || {}).chartType || 'metric' // 默认使用 metric 替代 count
      const fontColor = (item.options || {}).fontColor || '#ffffff'
      const bgColor = (item.options || {}).bgColor || ['#6ABFFE', '#5375EB']
      const width = (item.options || {}).w
      const showIcon = (item.options || {}).showIcon
      const type_ids = item?.options?.type_ids || []
      const attr_ids = item?.options?.attr_ids || []
      const ret = item?.options?.ret || ''

      // 获取聚合相关配置
      const aggregation = item?.options?.aggregation || 'count'

      this.width = width
      this.chartType = chartType === 'metric' ? 'metric' : chartType // 将 count 转换为 metric
      this.filterExp = item?.options?.filter ?? ''
      this.chartColor = item?.options?.chartColor ?? '#5DADF2,#86DFB7,#5A6F96,#7BD5FF,#FFB980,#4D58D6,#D9B6E9,#8054FF'
      this.isShadow = item?.options?.isShadow ?? false

      if (chartType === 'metric') {
        this.fontColor = fontColor
        this.bgColor = bgColor

        // 确保 metric 类型设置正确的 category
        if (chartType === 'metric') {
          // 在后面设置 form.category = 1
        }
      }

      // 加载属性列表
      if (type_ids && type_ids.length) {
        await getCITypeAttributesByTypeIds({ type_ids: type_ids.join(',') }).then((res) => {
          this.attributes = res.attributes

          // 筛选数值型属性
          this.numericAttributes = this.attributes.filter(attr =>
            !attr.is_password && (attr.value_type === '0' || attr.value_type === '1')
          )
        })

        if (chartType === 'metric' || (['bar', 'line', 'pie'].includes(chartType) && category === 1) || chartType === 'table') {
          this.barDirection = item?.options?.barDirection ?? 'y'
          this.barStack = item?.options?.barStack ?? 'total'
          await getCITypeCommonAttributesByTypeIds({
            type_ids: type_ids.join(','),
          }).then((res) => {
            this.commonAttributes = res.attributes
          })
        }
      }

      if (type_id) {
        getRecursive_level2children(type_id).then((res) => {
          this.level2children = res
        })
        await getCITypeAttributesByTypeIds({
          type_ids: type_id,
        }).then((res) => {
          this.attributes = res.attributes

          // 筛选数值型属性
          this.numericAttributes = this.attributes.filter(attr =>
            !attr.is_password && (attr.value_type === '0' || attr.value_type === '1')
          )
        })

        await getCITypeCommonAttributesByTypeIds({
          type_ids: type_id,
        }).then((res) => {
          this.commonAttributes = res.attributes
        })
      }

      this.$nextTick(() => {
        this.$refs.filterComp.visibleChange(true, false)
      })

      const default_form = {
        category: 1,
        name: undefined,
        type_id: undefined,
        type_ids: undefined,
        attr_ids: undefined,
        level: undefined,
        showIcon: false,
        tableCategory: 1,
        aggregation: 'count',

      }

      this.form = {
        ...default_form,
        category: chartType === 'metric' ? 1 : category, // metric 类型设置 category 为 1
        name,
        type_id,
        type_ids,
        attr_ids,
        level,
        showIcon,
        tableCategory: ret === 'cis' ? 2 : 1,
        aggregation,
      }
    },
    handleclose() {
      this.attributes = []
      this.$refs.chartForm.clearValidate()
      this.isShowPreview = false
      this.visible = false
    },
    changeCIType(value) {
      this.form.attr_ids = []
      this.commonAttributes = []
      this.numericAttributes = []

      if ((Array.isArray(value) && value.length) || (!Array.isArray(value) && value)) {
        getCITypeAttributesByTypeIds({ type_ids: Array.isArray(value) ? value.join(',') : value }).then((res) => {
          this.attributes = res.attributes

          // 筛选数值型属性
          this.numericAttributes = this.attributes.filter(attr =>
            !attr.is_password && (attr.value_type === '0' || attr.value_type === '1')
          )
        })
      }
      if (!Array.isArray(value) && value) {
        getRecursive_level2children(value).then((res) => {
          this.level2children = res
        })
      }
      if (this.chartType === 'metric' || (['bar', 'line', 'pie'].includes(this.chartType) && this.form.category === 1) || this.chartType === 'table') {
        getCITypeCommonAttributesByTypeIds({ type_ids: Array.isArray(value) ? value.join(',') : value }).then((res) => {
          this.commonAttributes = res.attributes
        })
      }
    },

    handleok() {
      this.$refs.chartForm.validate(async (valid) => {
        if (valid) {
          // 检查是否是默认类型，且需要至少2个维度
          const isDefaultType = this.form.category === 1
          const needsMinTwoDimensions = isDefaultType && ['bar', 'line', 'pie', 'table'].includes(this.chartType)

          if (needsMinTwoDimensions && (!this.form.attr_ids || this.form.attr_ids.length < 2)) {
            this.$message.warning(this.$t('cmdb.custom_dashboard.needsMinTwoDimensions'))
            return false
          }
          const name = this.form.name
          const { chartType, fontColor, bgColor } = this
          this.$refs.filterComp.handleSubmit()
          if (this.item.id) {
            const params = {
              ...this.form,
              options: {
                ...this.item.options,
                name,
                w: this.width,
                chartType: this.chartType,
                showIcon: this.form.showIcon,
                type_ids: this.form.type_ids,
                filter: this.filterExp,
                isShadow: this.isShadow,
                // 添加聚合相关配置
                aggregation: this.form.aggregation,
                value_attr_id: this.form.value_attr_id,
              },
            }
            if (chartType === 'metric') {
              params.options.fontColor = fontColor
              params.options.bgColor = bgColor
              params.attr_ids = this.form.attr_ids
            }
            if (['bar', 'line', 'pie'].includes(chartType)) {
              if (this.form.category === 1) {
                params.options.attr_ids = this.form.attr_ids
              }
              params.options.chartColor = this.chartColor
            }
            if (chartType === 'bar') {
              params.options.barDirection = this.barDirection
              params.options.barStack = this.barStack
            }
            if (chartType === 'table') {
              params.options.attr_ids = this.form.attr_ids
              if (this.form.tableCategory === 2) {
                params.options.ret = 'cis'
              }
            }
            delete params.showIcon
            delete params.type_ids
            delete params.attr_ids
            delete params.tableCategory
            delete params.aggregation
            await putCustomDashboard(this.item.id, params)
            this.$emit('refresh', this.item.id)
          } else {
            const { xLast, yLast, wLast } = getLastLayout(this.layout())
            const w = this.width
            const x = xLast + wLast + w > 12 ? 0 : xLast + wLast
            const y = xLast + wLast + w > 12 ? yLast + 1 : yLast
            const params = {
              ...this.form,
              options: {
                x,
                y,
                w,
                h: this.form.category === 1 ? 3 : 5,
                name,
                chartType: this.chartType,
                showIcon: this.form.showIcon,
                type_ids: this.form.type_ids,
                filter: this.filterExp,
                isShadow: this.isShadow,
                // 添加聚合相关配置
                aggregation: this.form.aggregation,
              },
            }
            if (chartType === 'metric') {
              params.options.fontColor = fontColor
              params.options.bgColor = bgColor
              params.options.attr_ids = this.form.attr_ids
            }
            if (['bar', 'line', 'pie'].includes(chartType)) {
              if (this.form.category === 1) {
                params.options.attr_ids = this.form.attr_ids
              }
              params.options.chartColor = this.chartColor
            }
            if (chartType === 'bar') {
              params.options.barDirection = this.barDirection
              params.options.barStack = this.barStack
            }
            if (chartType === 'table') {
              params.options.attr_ids = this.form.attr_ids
              if (this.form.tableCategory === 2) {
                params.options.ret = 'cis'
              }
            }
            delete params.showIcon
            delete params.type_ids
            delete params.attr_ids
            delete params.tableCategory
            delete params.aggregation
            await postCustomDashboard(params)
          }
          this.handleclose()
          this.$emit('refresh')
        }
      })
    },
    // changeDashboardCategory(value) {
    //   this.$refs.chartForm.clearValidate()
    //   if (value === 1 && this.form.type_id) {
    //     this.changeCIType(this.form.type_id)
    //   }
    // },
    changeChartType(t) {
      // 无论什么情况，都重置表单
      this.resetForm()

      this.chartType = t.value
      this.isShowPreview = false

      // 重置聚合相关字段
      this.form.aggregation = 'count'

      // 无论是什么图表类型，都将 category 设置为 1
      this.form.category = 1
    },
    showPreview() {
      this.$refs.chartForm.validate(async (valid) => {
        if (valid) {
          // 检查是否是默认类型，且需要至少2个维度
          const isDefaultType = this.form.category === 1
          const needsMinTwoDimensions = isDefaultType && ['bar', 'line', 'pie', 'table'].includes(this.chartType)

          if (needsMinTwoDimensions && (!this.form.attr_ids || this.form.attr_ids.length < 2)) {
            this.$message.warning(this.$t('cmdb.custom_dashboard.needsMinTwoDimensions'))
            return false
          }
          this.isShowPreview = false
          const name = this.form.name
          const { chartType, fontColor, bgColor } = this
          this.$refs.filterComp.handleSubmit()
          const params = {
            ...this.form,
            options: {
              name,
              chartType,
              showIcon: this.form.showIcon,
              type_ids: this.form.type_ids,
              filter: this.filterExp,
              isShadow: this.isShadow,
              // 添加聚合相关配置
              aggregation: this.form.aggregation,
            },
          }
          if (chartType === 'metric') {
            params.options.fontColor = fontColor
            params.options.bgColor = bgColor
            params.options.attr_ids = this.form.attr_ids
          }
          if (['bar', 'line', 'pie'].includes(chartType)) {
            if (this.form.category === 1) {
              params.options.attr_ids = this.form.attr_ids
            }
            params.options.chartColor = this.chartColor
          }
          if (chartType === 'bar') {
            params.options.barDirection = this.barDirection
            params.options.barStack = this.barStack
          }
          if (chartType === 'table') {
            params.options.attr_ids = this.form.attr_ids
            if (this.form.tableCategory === 2) {
              params.options.ret = 'cis'
            }
          }
          delete params.showIcon
          delete params.type_ids
          delete params.attr_ids
          delete params.tableCategory
          postCustomDashboardPreview(params).then((res) => {
            this.isShowPreview = true
            this.previewData = res.counter
          })
        }
      })
    },
    setExpFromFilter(filterExp) {
      if (filterExp) {
        this.filterExp = `${filterExp}`
      } else {
        this.filterExp = undefined
      }
    },
    resetForm() {
      this.form.type_id = undefined
      this.form.type_ids = []
      this.form.attr_ids = []
      // 重置聚合相关字段
      this.form.aggregation = 'count'
      this.numericAttributes = []
      this.commonAttributes = [] // 清空属性列表
      this.attributes = [] // 清空属性列表
      this.$refs.chartForm.clearValidate()
    },
    changeAttr(value) {
      if (value && value.length) {
        // 不再在这里检查维度数量，仅在提交和预览时验证

        if (['metric'].includes(this.chartType)) {
          // 对于 metric 类型，只能选择一个维度
          this.form.attr_ids = [value[value.length - 1]]
        } else if (['pie'].includes(this.chartType)) {
          // 对于 pie 类型，最多选择两个维度
          if (value.length > 2) {
            this.form.attr_ids = value.slice(value.length - 2, value.length)
          } else {
            // 不再在这里显示提示信息，仅在提交和预览时验证
            this.form.attr_ids = [...value]
          }
        } else if (['bar', 'line'].includes(this.chartType)) {
          // 对于 bar 和 line 类型，最多选择三个维度
          if (value.length > 3) {
            this.form.attr_ids = value.slice(value.length - 3, value.length)
          } else {
            // 不再在这里显示提示信息，仅在提交和预览时验证
            this.form.attr_ids = [...value]
          }
        } else if (['table'].includes(this.chartType)) {
          if (value.length > 3) {
            this.form.attr_ids = value.slice(value.length - 3, value.length)
          } else {
            // 不再在这里显示提示信息，仅在提交和预览时验证
            this.form.attr_ids = [...value]
          }
        }

        // 检查最后一个属性是否可聚合，并设置聚合方式
        this.$nextTick(() => {
          if (this.valueAttr) {
            if (!this.isValueAttrAggregatable) {
              // 如果不可聚合，则设置为 count
              this.form.aggregation = 'count'
            }
          }
        })
      }
    },
    clickLevel2children(_, citype, level) {
      if (this.form.level !== level) {
        this.$nextTick(() => {
          this.form.type_ids = [citype.id]
        })
      }
      this.form.level = level
    },
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    },
  },
}
</script>

<style lang="less" scoped>
.chart-wrapper {
  display: flex;
  .chart-left {
    width: 50%;
    .chart-left-preview {
      border: 1px solid #e4e7ed;
      border-radius: 2px;
      height: 280px;
      width: 92%;
      position: relative;
      padding: 12px;
      .chart-left-preview-operation {
        color: #86909c;
        position: absolute;
        top: 12px;
        right: 12px;
        cursor: pointer;
      }
      .chart-left-preview-box {
        padding: 6px 12px;
        height: 250px;
        border-radius: 8px;
      }
    }
    .chart-left-preview-empty {
      background: url('../../assets/dashboard_empty.png');
      background-size: contain;
      background-repeat: no-repeat;
      background-position-x: center;
      background-position-y: center;
    }
  }
  .chart-right {
    width: 50%;
    h4 {
      font-weight: 700;
      color: #000;
    }
    .chart-right-type {
      display: flex;
      justify-content: space-between;
      background-color: #f0f5ff;
      padding: 6px 12px;
      .chart-right-type-box {
        cursor: pointer;
        width: 70px;
        height: 60px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        > i {
          font-size: 32px;
        }
        > span {
          font-size: 12px;
        }
      }
      .chart-right-type-box-selected {
        background-color: #e5f1ff;
      }
    }
    .chart-width {
      width: 100%;
      > label {
        width: 25%;
        text-align: center;
      }
    }
  }
}
</style>
<style lang="less">
.chart-wrapper {
  .ant-form-item {
    margin-bottom: 0;
  }

  .aggregation-form-item {
    margin-bottom: 16px !important;
  }
}
</style>
