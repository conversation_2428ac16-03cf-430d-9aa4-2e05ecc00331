import i18n from '@/lang'

export const dashboardCategory = () => {
    return {
        1: { label: i18n.t('cmdb.custom_dashboard.default') },
        2: { label: i18n.t('cmdb.custom_dashboard.relation') }
    }
}

// 聚合类型选项
export const aggregationTypes = () => {
    return [
        {
            value: 'count',
            label: i18n.t('cmdb.custom_dashboard.count'),
            description: i18n.t('cmdb.custom_dashboard.countDescription')
        },
        {
            value: 'sum',
            label: i18n.t('cmdb.custom_dashboard.sum'),
            description: i18n.t('cmdb.custom_dashboard.sumDescription')
        },
        {
            value: 'avg',
            label: i18n.t('cmdb.custom_dashboard.avg'),
            description: i18n.t('cmdb.custom_dashboard.avgDescription')
        },
        {
            value: 'max',
            label: i18n.t('cmdb.custom_dashboard.max'),
            description: i18n.t('cmdb.custom_dashboard.maxDescription')
        },
        {
            value: 'min',
            label: i18n.t('cmdb.custom_dashboard.min'),
            description: i18n.t('cmdb.custom_dashboard.minDescription')
        }
    ]
}
