<template>
  <div class="cmdb-batch-upload" :style="{ height: `${windowHeight - 64}px` }">
    <div class="cmdb-views-header">
      <span>
        <span class="cmdb-views-header-title">{{ $t('cmdb.menu.batchUpload') }}</span>
      </span>
    </div>
    <CiTypeChoice
      v-if="ciTypeGroups.length"
      ref="ciTypeChoice"
      :ciTypeGroups="ciTypeGroups"
      :ci_units_id="ci_units_id"
      :ci_system_id="ci_system_id"
      @getCiTypeAttr="showCiType"
    />
    <p class="cmdb-batch-upload-label"><span>*</span>3. {{ $t('cmdb.batch.uploadFile') }}</p>
    <UploadFileForm
      :isUploading="isUploading"
      :ciType="ciType"
      ref="uploadFileForm"
      @uploadDone="uploadDone"
    ></UploadFileForm>
    <p class="cmdb-batch-upload-label">4. {{ $t('cmdb.batch.dataPreview') }}</p>
    <CiUploadTable :ciTypeAttrs="ciTypeAttrs" ref="ciUploadTable" :uploadData="uploadData"></CiUploadTable>
    <div class="cmdb-batch-upload-action">
      <a-space size="large">
        <a-button :disabled="!(ciType && uploadData.length)" @click="handleUpload" type="primary">{{
          $t('upload')
        }}</a-button>
        <a-button @click="handleCancel">{{ $t('cancel') }}</a-button>
        <a-button v-if="hasError && !isUploading" @click="downloadError" type="primary">{{
          $t('cmdb.batch.downloadFailed')
        }}</a-button>
      </a-space>
    </div>
    <UploadResult
      v-if="ciType"
      ref="uploadResult"
      :upLoadData="uploadData"
      :ci_units_id="ci_units_id"
      :ci_system_id="ci_system_id"
      :ciType="ciType"
      :unique-field="uniqueField"
      :isUploading="isUploading"
      @uploadResultDone="uploadResultDone"
      @uploadResultError="uploadResultError"
    ></UploadResult>
  </div>
</template>

<script>
import moment from 'moment'
import { mapState } from 'vuex'
import CiTypeChoice from './modules/CiTypeChoice'
import CiUploadTable from './modules/CiUploadTable'
import UploadFileForm from './modules/UploadFileForm'
import UploadResult from './modules/UploadResult'
import { filterNull } from '@/modules/cmdb/api/batch'
import { getCITypeGroups } from '@/modules/cmdb/api/ciTypeGroup'
import { customAlphabet } from 'nanoid'

const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
const nanoid = customAlphabet(alphabet, 10)

const convertTime = (value, type) => {
  const timestamp = Math.round((value - 25569) * 86400 * 1000 - 28800000)
  switch (type) {
    case '4': return moment(timestamp).format('YYYY-MM-DD')
    case '3': return moment(timestamp).format('YYYY-MM-DD HH:mm:ss')
    case '5': return moment(Math.round(value * 86400 * 1000 - 28800000)).format('HH:mm:ss')
    default: return value
  }
}

export default {
  name: 'CloudAddResources',
  components: {
    CiTypeChoice,
    CiUploadTable,
    UploadFileForm,
    UploadResult,
  },
  data() {
    return {
      ciTypeAttrs: {},
      uploadData: [],
      ci_units_id: 0,
      ci_system_id: 0,
      ciType: 0,
      uniqueField: '',
      uniqueId: 0,
      isUploading: false,
      hasError: false,
      ciTypeGroups: [],
    }
  },
  computed: {
    ...mapState({
      windowHeight: (state) => state.windowHeight,
    }),
  },
  async created() {
    getCITypeGroups({ need_other: true }).then((res) => {
      console.log('res data:', res)
      this.ciTypeGroups = res
      const _res = res.find(item => item.name === '基础信息')
      this.ci_units_id = _res.ci_types.find(item => item.name === 'units').id
      this.ci_system_id = _res.ci_types.find(item => item.name === 'system').id
      console.log('ci_units_id, ci_system_id:', this.ci_units_id, this.ci_system_id)
    })
  },
  methods: {
    showCiType(message) {
      this.ciTypeAttrs = message ?? {}
      this.ciType = message?.type_id ?? 0
      this.uniqueField = message?.unique ?? ''
      this.uniqueId = message?.unique_id ?? 0
    },
    uploadDone(dataList) {
      const startTime = performance.now()
      console.log('开始处理上传数据:', new Date().toISOString())

      const mapStartTime = performance.now()
      // 创建属性 Map
      const attrMap = new Map(
        this.ciTypeAttrs.attributes.map((attr) => [attr.alias || attr.name, attr])
      )
      console.log(`创建属性 Map 耗时: ${performance.now() - mapStartTime} 毫秒`)

      const [headers, ...data] = dataList
      const processStartTime = performance.now()
      const missingHeaders = new Set() // 用于存储在 attrMap 中找不到的 header

      const _uploadData = filterNull(data).map((item) => {
        const _ele = {}
        item.forEach((ele, j) => {
          if (ele != null) {
            const header = headers[j]
            const attr = attrMap.get(header)
            if (attr) {
              _ele[header] = typeof ele === 'number'
                ? convertTime(ele, attr.value_type)
                : ele
            } else {
              // 如果在 attrMap 中找不到对应的属性，将 header 添加到 missingHeaders 中
              missingHeaders.add(header)
              // 仍然保留原始值，以防后续处理需要
              _ele[header] = ele
            }
          }
        })
        _ele.YunXuHao = `inst_${nanoid(10)}`

        return _ele
      })

      console.log(`处理数据耗时: ${performance.now() - processStartTime} 毫秒`)

      this.uploadData = _uploadData
      this.hasError = false
      this.isUploading = false

      const endTime = performance.now()
      console.log('结束处理上传数据:', new Date().toISOString())
      console.log(`总处理时间: ${endTime - startTime} 毫秒`)
      console.log(`处理的数据条数: ${_uploadData.length}`)
      console.log('处理的数据:', _uploadData)

      // 如果有找不到对应属性的 header，显示警告信息
      if (missingHeaders.size > 0) {
        const missingHeadersArray = Array.from(missingHeaders)
        this.$message.warning(`以下字段在系统中没有对应的属性定义，可能会影响数据处理: ${missingHeadersArray.join(', ')}`)
        console.warn('缺少属性定义的字段:', missingHeadersArray)
      }
    },
    handleUpload() {
      if (!this.ciType) {
        this.$message.error(this.$t('cmdb.batch.unselectCIType'))
        return
      }
      if (this.uploadData && this.uploadData.length > 0) {
        this.isUploading = true
        this.$nextTick(() => {
          this.$refs.uploadResult.upload2Server()
        })
      } else {
        this.$message.error(this.$t('cmdb.batch.pleaseUploadFile'))
      }
    },
    handleCancel() {
      if (!this.isUploading) {
        this.showCiType(null)
        this.$refs.ciTypeChoice.selectNum = undefined
        this.hasError = false
      } else {
        this.$message.warning(this.$t('cmdb.batch.batchUploadCanceled'))
        this.isUploading = false
      }
    },
    uploadResultDone() {
      this.isUploading = false
    },
    uploadResultError(index) {
      this.hasError = true
      this.$refs.ciUploadTable.uploadResultError(index)
    },
    downloadError() {
      this.$refs.ciUploadTable.downloadError()
    },
  },
}
</script>
<style lang="less">
@import '../index.less';
.cmdb-batch-upload-label {
  color: @text-color_1;
  font-weight: bold;
  white-space: pre;
  > span {
    color: red;
  }
}
</style>
<style lang="less" scoped>

.cmdb-batch-upload {
  margin-bottom: -24px;
  padding: 20px;
  background-color: #fff;
  border-radius: @border-radius-box;
  overflow: auto;
  .cmdb-batch-upload-action {
    width: 50%;
    margin: 12px 0;
  }
}
</style>
