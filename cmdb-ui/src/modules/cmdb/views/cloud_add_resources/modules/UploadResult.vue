<template>
  <div class="cmdb-batch-upload-result" v-if="visible">
    <p class="cmdb-batch-upload-label">5. 处理过程：</p>
    <div class="cmdb-batch-upload-result-content">
      <div v-for="(message, index) in progressMessages" :key="index">
        {{ message }}
      </div>
    </div>
    <p class="cmdb-batch-upload-label">6. {{ $t('cmdb.batch.uploadResult') }}</p>
    <div class="cmdb-batch-upload-result-content">
      <h4>
        {{ $t('cmdb.batch.total') }}&nbsp;<span style="color: blue">{{ total }}</span>
        {{ $t('cmdb.batch.successItems') }} <span style="color: lightgreen">{{ success }}</span>
        {{ $t('cmdb.batch.failedItems') }} <span style="color: red">{{ errorNum }} </span>{{ $t('cmdb.batch.items') }}
      </h4>
      <div>
        <span>{{ $t('cmdb.batch.errorTips') }}: </span>
        <ol>
          <li :key="item + index" v-for="(item, index) in errorItems">{{ item }}</li>
        </ol>
      </div>
    </div>
  </div>
</template>

<script>
import { uploadData } from '@/modules/cmdb/api/cloudBatch'
import { searchCI } from '../../../api/ci'
import { addCIRelationView } from '@/modules/cmdb/api/cloudCIRelation' // 导入新的API方法
import { validateIndispensableFields } from '@/modules/cmdb/utils/cloud'

export default {
  name: 'UploadResult',
  props: {
    upLoadData: {
      required: true,
      type: Array,
    },
    ciType: {
      required: true,
      type: Number,
    },
    uniqueField: {
      required: true,
      type: String,
    },
    isUploading: {
      type: Boolean,
      default: false,
    },
    ci_units_id: {
      required: true,
      type: Number,
    },
    ci_system_id: {
      required: true,
      type: Number,
    },
    bussinessType: {
      type: String,
      default: 'normal',
    },
    existPolicy: {
      type: String,
      default: 'reject',
    },
  },
  data() {
    return {
      visible: false,
      complete: 0,
      errorNum: 0,
      success: 0,
      errorItems: [],
      currentProgress: 0,
      progressMessages: [],
      unitData: {},
      systemData: {}, // 新增:用于存储系统ID
    }
  },
  computed: {
    total() {
      return this.upLoadData.length || 0
    },
  },
  watch: {
    ciType: {
      handler() {
        this.visible = false
      },
    },
  },
  methods: {
    async upload2Server() {
      try {
        this.initializeUpload()
        try {
          validateIndispensableFields(this.upLoadData, ['单位名称', '系统名称', '计费调整日期', '计费调整单号'], 'running')
        } catch (error) {
          this.$message.error(`验证必要字段失败: ${error.message}`)
          throw error
        }
        try {
          await this.validateUnits()
        } catch (error) {
          this.$message.error(`验证单位失败: ${error.message}`)
          throw error
        }
        try {
          await this.createSystems()
        } catch (error) {
          this.$message.error(`创建系统失败: ${error.message}`)
          throw error
        }
        try {
          await this.uploadCloudResources()
        } catch (error) {
          this.$message.error(`上传云资源失败: ${error.message}`)
          throw error
        }
        this.finishUpload()
        this.$message.success('上传成功完成')
      } catch (error) {
        console.error('上传过程中发生错误:', error)
      } finally {
        this.$emit('update:isUploading', false)
      }
    },
    initializeUpload() {
      this.visible = true
      this.success = 0
      this.errorNum = 0
      this.errorItems = []
    },
    async validateUnits() {
      const uniqUnits = [...new Set(this.upLoadData.map(x => x['单位名称']))]
      for (const unit of uniqUnits) {
        const res = await searchCI({
          q: `_type:(${this.ci_units_id}),units_name:${unit}`,
        })
        if (res.total !== 1) {
          throw new Error('请先创建单位或单位名称不唯一')
        } else {
          const { _id } = res.result[0]
          this.unitData[unit] = { unit, _id }
        }
      }
    },

    convertPunctuation(text) {
      const PUNCTUATION_MAP = {
        ',': '，',
        '.': '。',
        '?': '？',
        '!': '！',
        ':': '：',
        ';': '；',
        '(': '（',
        ')': '）',
        '[': '【',
        ']': '】',
        '"': '"',
        "'": "'",
      }
      return text.split('').map(char => PUNCTUATION_MAP[char] || char).join('')
    },

    async createSystems() {
      this.currentProgress = 0
      this.progressMessages = ['开始检查并创建系统...']
      const uniqUnitSystemName = [...new Set(this.upLoadData.map(item => {
        const originalSystemName = item['系统名称']
        const convertedSystemName = this.convertPunctuation(originalSystemName)
        console.log('originalSystemName:', originalSystemName)
        console.log('convertedSystemName:', convertedSystemName)
        if (originalSystemName !== convertedSystemName) {
          this.progressMessages.push(`系统名称标点符号已转换: ${originalSystemName} -> ${convertedSystemName}`)
          item['系统名称'] = convertedSystemName
        }
        return JSON.stringify({
          'units_name': item['单位名称'],
          'system_name': convertedSystemName
        })
      }))].map(JSON.parse)

      const totalSystems = uniqUnitSystemName.length
      for (let i = 0; i < totalSystems; i++) {
        const { units_name, system_name } = uniqUnitSystemName[i]
        this.progressMessages.push(`正在检查单位(${units_name})和系统(${system_name})是否存在`)
        const systemExists = await this.checkSystemExists(units_name, system_name)
        if (systemExists) {
          this.progressMessages.push(`系统已存在：${units_name} - ${system_name}，ID: ${systemExists}`)
          this.systemData[`${units_name}-${system_name}`] = systemExists
        } else {
          this.progressMessages.push(`系统不存在，开始创建系统(${units_name} - ${system_name})`)
          await this.createSystem({ units_name, system_name })
        }
        this.currentProgress = Math.round(((i + 1) / totalSystems) * 100)
      }
      this.progressMessages.push('系统检查和创建完成')
    },

    async checkSystemExists(unitName, systemName) {
      const res = await searchCI({
        q: `_type:(${this.ci_system_id}),units_name:${unitName},system_name:${systemName}`,
      })
      if (res.total === 1) {
        return res.result[0]._id
      } else {
        return false
      }
    },

    async createSystem(systemData) {
      try {
        const res = await uploadData(this.ci_system_id, this.bussinessType, systemData, this.existPolicy)
        if (!res.ci_id) {
          throw new Error('系统创建失败')
        }
        const systemId = res.ci_id[0]
        this.progressMessages.push(`成功创建系统：${systemData.system_name}，ID: ${systemId}`)
        const unitId = this.unitData[systemData.units_name]._id
        await this.createUnitSystemRelation(unitId, systemId, systemData.units_name, systemData.system_name)
        this.systemData[`${systemData.units_name}-${systemData.system_name}`] = systemId
      } catch (error) {
        this.progressMessages.push(`创建系统失败：${systemData.system_name}，错误：${error.message}`)
        throw error
      }
    },

    async createUnitSystemRelation(unitId, systemId, unitName, systemName) {
      try {
        await addCIRelationView(unitId, systemId)
        this.progressMessages.push(`已建立单位(${unitName}, ID: ${unitId}) -> 系统(${systemName}, ID: ${systemId})的关系`)
      } catch (error) {
        this.progressMessages.push(`单位(${unitName}) -> 系统(${systemName})关系创建失败，请手动建立关系。错误：${error.message}`)
      }
    },

    async uploadCloudResources() {
      this.progressMessages.push('开始上传云资源...')
      const chunkSize = 6
      for (let i = 0; i < this.upLoadData.length; i += chunkSize) {
        if (!this.isUploading) break
        const chunk = this.upLoadData.slice(i, i + chunkSize)
        await this.uploadChunk(chunk, i)
      }
    },

    async uploadChunk(chunk, startIndex) {
      const promises = chunk.map(x => uploadData(this.ciType, this.bussinessType, x, this.existPolicy))
      const results = await Promise.allSettled(promises)
      results.forEach((r, j) => this.processUploadResult(r, startIndex + j))
      this.complete += chunk.length
    },

    async processUploadResult(result, index) {
      if (result.status === 'fulfilled') {
        this.success++
        // 新增:创建资源与系统的关联
        await this.createResourceSystemRelation(this.upLoadData[index], result.value.ci_id[0])
      } else {
        this.errorItems.push(result?.reason?.response?.data.message ?? this.$t('cmdb.batch.requestFailedTips'))
        this.errorNum++
        this.$emit('uploadResultError', index)
      }
    },

    async createResourceSystemRelation(resourceData, resourceId) {
      const key = `${resourceData['单位名称']}-${resourceData['系统名称']}`
      const systemId = this.systemData[key]
      if (systemId) {
        try {
          await addCIRelationView(systemId, resourceId)
          this.progressMessages.push(`成功创建系统(ID: ${systemId}) -> 资源(ID: ${resourceId})的关联`)
        } catch (error) {
          this.progressMessages.push(`系统 -> 资源关系创建失败，请手动建立关系。系统ID: ${systemId}，资源ID: ${resourceId}，错误: ${error.message}`)
        }
      }
    },

    finishUpload() {
      if (this.isUploading) {
        this.$emit('uploadResultDone')
        this.$message.info(this.$t('cmdb.batch.requestSuccessTips'))
      }
    }
  }
}
</script>

<style lang="less" scoped>
.cmdb-batch-upload-result {
  .cmdb-batch-upload-result-content {
    background-color: rgba(240, 245, 255, 0.35);
    border-radius: 5px;
    padding: 12px;
  }
  .progress-indicator {
    margin-top: 10px;
    padding: 10px;
    background-color: #f0f0f0;
    border-radius: 4px;
    max-height: 200px;
    overflow-y: auto;
    p {
      margin: 5px 0;
    }
  }
}
</style>
