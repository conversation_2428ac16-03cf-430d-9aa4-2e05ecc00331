<template>
  <a-card :bordered="false">
    <div class="action-btn">
      <a-button @click="handleCreate" type="primary" style="margin-bottom: 15px;">
        {{ $t('cmdb.load_attr.addLoadAttr') }}
      </a-button>
    </div>

    <!-- 搜索区域 -->
    <div class="table-search" style="margin-bottom: 15px;">
      <a-form layout="inline">
        <a-form-item :label="$t('cmdb.load_attr.name')">
          <a-input v-model="searchParams.name" @change="handleSearch" allowClear />
        </a-form-item>
        <a-form-item :label="$t('cmdb.load_attr.value_type')">
          <a-select v-model="searchParams.value_type" style="width: 120px" @change="handleSearch" allowClear>
            <a-select-option v-for="type in valueTypes" :key="type.value" :value="type.value">
              {{ type.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </div>

    <div>
      <vxe-table
        ref="loadAttrTable"
        :data="tableData"
        :loading="loading"
        keep-source
        highlight-hover-row
        :edit-config="{ trigger: 'manual', mode: 'row' }"
        @edit-closed="handleEditClose"
        stripe
        class="ops-stripe-table"
        bordered
        :height="`${windowHeight - 300}px`"
      >
        <vxe-table-column
          field="name"
          :title="$t('cmdb.load_attr.name')"
          :edit-render="{ name: 'input', attrs: { type: 'text' }, events: { keyup: customCloseEdit } }"
        ></vxe-table-column>

        <vxe-table-column
          field="value_type"
          :title="$t('cmdb.load_attr.value_type')"
          :edit-render="{
            name: 'select',
            options: valueTypes,
            props: { label: 'label', value: 'value' }
          }"
        >
          <template #default="{row}">
            {{ getValueTypeLabel(row.value_type) }}
          </template>
        </vxe-table-column>

        <vxe-table-column
          field="alias"
          :title="$t('cmdb.load_attr.alias')"
          :edit-render="{ name: 'input', attrs: { type: 'text' }, events: { keyup: customCloseEdit } }"
        ></vxe-table-column>

        <vxe-table-column
          field="is_monthly"
          :title="$t('cmdb.load_attr.period_type')"
          :edit-render="{
            name: 'select',
            options: [
              { label: $t('cmdb.load_attr.daily'), value: 'false' },
              { label: $t('cmdb.load_attr.monthly'), value: 'true' }
            ]
          }"
        >
          <template #default="{row}">
            <a-tag :color="row.is_monthly ? 'blue' : ''">
              {{ row.is_monthly === 'true' ? $t('cmdb.load_attr.monthly') : $t('cmdb.load_attr.daily') }}
            </a-tag>
          </template>
        </vxe-table-column>

        <vxe-table-column field="updateTime" :title="$t('updated_at')">
          <template #default="{row}">
            {{ row.updated_at || row.created_at }}
          </template>
        </vxe-table-column>

        <vxe-table-column field="operation" :title="$t('operation')" align="center">
          <template #default="{row}">
            <template>
              <a><a-icon type="edit" @click="handleEdit(row)"/></a>
              <a-divider type="vertical" />
              <a-popconfirm
                :title="$t('confirmDelete')"
                @confirm="handleDelete(row)"
                :okText="$t('yes')"
                :cancelText="$t('no')"
              >
                <a :style="{ color: 'red' }"><a-icon type="delete"/></a>
              </a-popconfirm>
            </template>
          </template>
        </vxe-table-column>
      </vxe-table>

      <pager
        :current-page.sync="queryParams.page"
        :page-size.sync="queryParams.page_size"
        :page-sizes="[50, 100, 200]"
        :total="pagination.total"
        :isLoading="loading"
        @change="handlePageChange"
        @showSizeChange="handleSizeChange"
        :style="{ marginTop: '10px' }"
      />
    </div>
  </a-card>
</template>

<script>
import Pager from '@/components/Pager'
import moment from 'moment'
import {
  getLoadAttributes,
  createLoadAttribute,
  updateLoadAttribute,
  deleteLoadAttribute,
} from '@/modules/cmdb/api/loadAttribute'
import { LoadValueTypeOptions } from '@/modules/cmdb/constants'

export default {
  name: 'LoadAttributes',
  components: { Pager },

  data() {
    return {
      loading: false,
      tableData: [],
      searchParams: {
        name: '',
        value_type: undefined
      },
      queryParams: {
        page: 1,
        page_size: 50,
        ...this.searchParams
      },
      pagination: {
        total: 0
      },
      valueTypes: LoadValueTypeOptions,
      periodTypes: {
        DAILY: '0',
        MONTHLY: '1'
      }
    }
  },

  mounted() {
    this.loadData()
  },

  computed: {
    windowHeight() {
      return this.$store.state.windowHeight
    }
  },

  methods: {
    loadData() {
      this.loading = true
      getLoadAttributes(this.queryParams)
        .then((res) => {
          this.tableData = res.attrs.map(item => ({
            ...item,
            is_monthly: item.is_monthly ? 'true' : 'false'
          }))
          this.pagination.total = res.total
        })
        .finally(() => {
          this.loading = false
        })
    },

    handleSearch() {
      this.queryParams = {
        ...this.queryParams,
        ...this.searchParams,
        page: 1
      }
      this.loadData()
    },

    handlePageChange(page) {
      this.queryParams.page = page
      this.loadData()
    },

    handleSizeChange(current, size) {
      this.queryParams.page = 1
      this.queryParams.page_size = size
      this.loadData()
    },

    getValueTypeLabel(value) {
      const type = this.valueTypes.find(t => t.value === value)
      return type ? type.label : value
    },

    handleEdit(row) {
      const $table = this.$refs.loadAttrTable
      $table.setActiveRow(row)
    },

    handleCreate() {
      const $table = this.$refs.loadAttrTable
      const newRow = {
        name: '',
        value_type: '2',
        alias: '',
        is_monthly: 'false',
        created_at: moment().format('YYYY-MM-DD HH:mm:ss')
      }
      $table.insert(newRow).then(({ row }) => $table.setActiveRow(row))
    },

    handleEditClose({ row, rowIndex }) {
      const $table = this.$refs.loadAttrTable
      if (row.id) {
        if (row.name && row.value_type && $table.isUpdateByRow(row)) {
          const data = {
            name: row.name,
            value_type: row.value_type,
            alias: row.alias,
            is_monthly: row.is_monthly === 'true'
          }
          this.updateLoadAttribute(row.id, data, row)
        } else {
          $table.revertData(row)
        }
      } else {
        if (row.name && row.value_type) {
          const data = {
            name: row.name,
            value_type: row.value_type,
            alias: row.alias,
            is_monthly: row.is_monthly === 'true'
          }
          this.createLoadAttribute(data, row)
        } else {
          $table.remove(row)
        }
      }
    },

    updateLoadAttribute(id, data, row) {
      updateLoadAttribute(id, data).then(() => {
        this.$message.success(this.$t('updateSuccess'))
        this.loadData()
      }).catch(error => {
        const $table = this.$refs.loadAttrTable
        $table.revertData(row)
        void error
      })
    },

    createLoadAttribute(data, row) {
      createLoadAttribute(data).then(() => {
        this.$message.success(this.$t('addSuccess'))
        this.loadData()
      }).catch(error => {
        const $table = this.$refs.loadAttrTable
        $table.remove(row)
        void error
      })
    },

    handleDelete(record) {
      deleteLoadAttribute(record.id).then(() => {
        this.$message.success(this.$t('deleteSuccess'))
        this.loadData()
      }).catch(error => {
        const $table = this.$refs.loadAttrTable
        $table.revertData(record)
        void error
      })
    },

    customCloseEdit(value, $event) {
      if ($event.keyCode === 13) {
        const $table = this.$refs.loadAttrTable
        $table.clearActived()
      }
    }
  }
}
</script>

<style lang="less" scoped>
.table-search {
  .ant-form-item {
    margin-bottom: 0;
  }
}
</style>
