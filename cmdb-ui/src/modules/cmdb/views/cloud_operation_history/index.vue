<template>
  <div>
    <a-card :bordered="false">
      <a-tabs default-active-key="1">
        <a-tab-pane key="1" :tab="$t('cmdb.history.cloudBillingResourcesChange')">
          <ci-table></ci-table>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script>
import CiTable from './modules/ciTable.vue'
export default {
  name: 'OperationHistory',
  components: {
    CiTable,
  },
  data() {
    return {
      userList: [],
    }
  },
}
</script>

<style></style>
