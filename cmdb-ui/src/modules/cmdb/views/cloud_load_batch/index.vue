<template>
  <div class="cmdb-batch-upload" :style="{ height: `${windowHeight - 64}px` }">
    <div class="cmdb-views-header">
      <span>
        <span class="cmdb-views-header-title">{{ $t('cmdb.menu.batchUpload') }}</span>
      </span>
    </div>
    <CiTypeChoice v-if="ciTypeGroups.length" ref="ciTypeChoice" :ciTypeGroups="ciTypeGroups" @getCiTypeAttr="showCiType" @timeSlotSelectionChange="handleTimeSlotSelectionChange" />

    <!-- 时间段选择器 -->
    <div v-if="hasTimeSlotSelection" class="time-slot-selection">
      <p class="cmdb-batch-upload-label">2.5. {{ $t('cmdb.batch.selectTimeSlot') }}</p>
      <a-radio-group v-model="selectedTimeSlot" :style="{ marginBottom: '1em' }">
        <a-radio :value="TimeSlotEnum.BUSY_TIME">{{ $t('cmdb.batch.busyTime') }}</a-radio>
        <a-radio :value="TimeSlotEnum.IDLE_TIME">{{ $t('cmdb.batch.idleTime') }}</a-radio>
      </a-radio-group>
      <div class="time-slot-tips">
        <p class="tips-text">{{ $t('cmdb.batch.timeSlotTips') }}</p>
      </div>
    </div>

    <p class="cmdb-batch-upload-label"><span>*</span>3. {{ $t('cmdb.batch.uploadFile') }}</p>
    <UploadFileForm
      :isUploading="isUploading"
      :ciType="ciType"
      ref="uploadFileForm"
      @uploadDone="uploadDone"
      @beforeUpload="handleBeforeUpload"
    ></UploadFileForm>
    <div class="cmdb-batch-upload-action">
      <a-space size="large">
        <a-button
          :disabled="!(ciType && uploadData.length && validationErrors.length === 0) || isImported"
          @click="handleUpload"
          type="primary"
        >
          {{ $t('upload') }}
        </a-button>
        <a-button @click="handleCancel">{{ $t('cancel') }}</a-button>
      </a-space>
    </div>
    <TableResult
      v-if="validationErrors.length"
      :title="$t('cmdb.batch.validationResult')"
      :summary="validationSummary"
      :errors="validationErrors"
    />
    <UploadResult
      v-if="ciType"
      ref="uploadResult"
      :upLoadData="uploadData"
      :ciType="ciType"
      :unique-field="uniqueField"
      :isUploading="isUploading"
      :timeSlot="selectedTimeSlot"
      @uploadResultDone="uploadResultDone"
      @uploadResultError="uploadResultError"
      @importComplete="handleImportComplete"
    />
  </div>
</template>

<script>
import moment from 'moment'
import { mapState } from 'vuex'
import CiTypeChoice from './modules/CiTypeChoice'
import UploadFileForm from './modules/UploadFileForm'
import UploadResult from './modules/UploadResult'
import TableResult from './modules/TableResult'
import { getCITypeGroupsWithLoadAttrs } from '@/modules/cmdb/api/ciTypeGroup'
import { LoadValueTypes, TimeSlotEnum } from '@/modules/cmdb/constants'

export default {
  name: 'BatchUpload',
  components: {
    CiTypeChoice,
    UploadFileForm,
    UploadResult,
    TableResult
  },
  data() {
    return {
      ciTypeAttrs: {
        type_id: 0,
        attributes: []
      },
      uploadData: [],
      ciType: 0,
      uniqueField: '',
      uniqueId: 0,
      isUploading: false,
      ciTypeGroups: [],
      validationErrors: [],
      isImported: false,
      selectedTimeSlot: TimeSlotEnum.DEFAULT_SLOT, // 选中的时间段，默认为忙时
      hasTimeSlotSelection: false, // 是否显示时间段选择
      TimeSlotEnum // 暴露给模板使用
    }
  },
  computed: {
    ...mapState({
      windowHeight: (state) => state.windowHeight,
    }),
    validationSummary() {
      const total = this.uploadData.length + this.validationErrors.length
      const success = this.uploadData.length
      const failed = this.validationErrors.length
      return [
        `${this.$t('cmdb.batch.total')}: ${total}`,
        `${this.$t('cmdb.batch.successItems')}: ${success}`,
        `${this.$t('cmdb.batch.failedItems')}: ${failed}`
      ]
    }
  },
  async created() {
    getCITypeGroupsWithLoadAttrs({ need_other: true }).then((res) => {
      this.ciTypeGroups = res
    })
  },
  methods: {
    showCiType(attributes) {
      this.ciTypeAttrs = {
        type_id: this.ciType,
        attributes: attributes
      }
      this.ciType = this.$refs.ciTypeChoice?.selectNum || 0
    },

    handleTimeSlotSelectionChange(needsTimeSlot) {
      this.hasTimeSlotSelection = needsTimeSlot
      // 如果不需要时间段选择，重置为默认值
      if (!needsTimeSlot) {
        this.selectedTimeSlot = null
      } else {
        this.selectedTimeSlot = TimeSlotEnum.DEFAULT_SLOT
      }
    },
    convertExcelDate(value) {
      if (typeof value === 'number') {
        try {
          const date = new Date((value - 25569) * 86400 * 1000)
          return moment(date).format('YYYY-MM-DD')
        } catch (error) {
          console.error('Excel日期转换失败:', error)
          return value
        }
      }
      return value
    },
    normalizeDateString(value) {
      if (!value) return value

      if (typeof value === 'number') {
        return this.convertExcelDate(value)
      }

      const dateStr = String(value).trim()

      if (/^\d{4}\/\d{2}\/\d{2}$/.test(dateStr)) {
        return dateStr.replace(/\//g, '-')
      }

      if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
        return dateStr
      }

      return value
    },
    validateValue(value, valueType, header) {
      if (value === '-') {
        return {
          isValid: true,
          value: '-',
          error: null
        }
      }

      if (header === '主键') {
        const strValue = String(value).trim()
        return {
          isValid: strValue.length > 0,
          value: strValue,
          error: strValue.length > 0 ? null : '主键不能为空'
        }
      }

      if (header === '周期') {
        const normalizedDate = this.normalizeDateString(value)
        const isValidDaily = /^\d{4}-\d{2}-\d{2}$/.test(normalizedDate)
        const isValidMonthly = /^\d{4}-\d{2}$/.test(normalizedDate)

        if (isValidDaily || isValidMonthly) {
          const momentDate = moment(normalizedDate)
          if (!momentDate.isValid()) {
            return {
              isValid: false,
              value: normalizedDate,
              error: '无效的日期'
            }
          }
        }

        return {
          isValid: isValidDaily || isValidMonthly,
          value: normalizedDate,
          error: isValidDaily || isValidMonthly ? null : '日期格式必须是YYYY-MM-DD或YYYY-MM'
        }
      }

      switch (valueType) {
        case LoadValueTypes.INT:
          const intValue = parseInt(value, 10)
          const isValidInt = !isNaN(intValue) && String(intValue) === String(value)
          return {
            isValid: isValidInt,
            value: isValidInt ? intValue : null,
            error: isValidInt ? null : '必须是整数'
          }

        case LoadValueTypes.FLOAT:
          const floatValue = parseFloat(value)
          const isValidFloat = !isNaN(floatValue) && String(floatValue) === String(value)
          return {
            isValid: isValidFloat,
            value: isValidFloat ? floatValue : null,
            error: isValidFloat ? null : '必须是数字'
          }

        case LoadValueTypes.TEXT:
          const strValue = String(value).trim()
          return {
            isValid: true,
            value: strValue,
            error: null
          }

        case LoadValueTypes.LIST:
          if (value === null || value === undefined) {
            return {
              isValid: false,
              value: null,
              error: 'LIST类型不允许空值'
            }
          }

          // 如果是字符串，检查是否为空
          if (typeof value === 'string' && !value.trim()) {
            return {
              isValid: false,
              value: null,
              error: 'LIST类型不允许空字符串'
            }
          }

          // 如果是字符串，检查分割后的值是否包含空值
          if (typeof value === 'string') {
            const items = value.split(',').map(item => item.trim())
            if (items.some(item => !item)) {
              return {
                isValid: false,
                value: null,
                error: 'LIST类型不允许包含空值'
              }
            }
            return {
              isValid: true,
              value: value,
              error: null
            }
          }

          // 如果是数组，直接使用
          if (Array.isArray(value)) {
            return {
              isValid: true,
              value: value.join(','),
              error: null
            }
          }

          // 其他类型转换为字符串
          return {
            isValid: true,
            value: String(value),
            error: null
          }

        default:
          return { isValid: false, value: null, error: '未知的数据类型' }
      }
    },
    uploadDone(dataList) {
      const now = new Date()
      const formattedTime = `${now.toLocaleDateString()} ${now.toLocaleTimeString()}`
      console.log('begin time', formattedTime)
      const headers = dataList[0]
      const attrTypeMap = new Map(
        this.ciTypeAttrs.attributes.map(attr => [
          attr.alias || attr.name,
          attr.value_type
        ])
      )

      const totalRecords = dataList.length - 1
      let validRows = 0
      let invalidRows = 0
      const processedData = []
      const errors = []

      const processRows = function* () {
        for (let i = 1; i < dataList.length; i++) {
          const row = dataList[i]
          // console.log(`处理第 ${i + 1} 行数据:`, row)

          const processedRow = {}
          let isRowValid = true
          const rowErrors = []

          for (let j = 0; j < headers.length; j++) {
            const header = headers[j]
            const value = row[j]
            const isSpecialField = header === '主键' || header === '周期'
            const valueType = isSpecialField ? null : attrTypeMap.get(header)

            const { isValid, value: validatedValue, error } = this.validateValue(value, valueType, header)

            if (!isValid) {
              isRowValid = false
              rowErrors.push(`${header}: ${error}`)
              break
            }

            processedRow[header] = validatedValue
          }

          if (isRowValid) {
            validRows++
            yield processedRow
          } else {
            invalidRows++
            errors.push({
              error: rowErrors.join('; '),
              record: JSON.stringify(Object.fromEntries(
                headers.map((header, index) => [header, row[index]])
              ))
            })
          }
        }
      }

      for (const row of processRows.call(this)) {
        processedData.push(row)
      }

      this.uploadData = processedData
      this.validationErrors = errors
      this.isUploading = false

      this.$nextTick(() => {
        if (this.$refs.uploadResult) {
          this.$refs.uploadResult.updateValidationStats({
            total: totalRecords,
            success: validRows,
            failed: invalidRows,
            errors: errors
          })
        }
      })

      if (invalidRows > 0) {
        this.$message.warning(
          this.$t('cmdb.batch.dataValidationWarning', {
            total: totalRecords,
            valid: validRows,
            invalid: invalidRows
          })
        )
      }
    },
    handleUpload() {
      if (!this.ciType) {
        this.$message.error(this.$t('cmdb.batch.unselectCIType'))
        return
      }
      if (this.uploadData && this.uploadData.length > 0) {
        this.isUploading = true
        this.$nextTick(() => {
          this.$refs.uploadResult.upload2Server()
        })
      } else {
        this.$message.error(this.$t('cmdb.batch.pleaseUploadFile'))
      }
    },
    handleCancel() {
      if (!this.isUploading) {
        this.showCiType(null)
        this.ciType = 0
        this.$refs.ciTypeChoice.selectNum = undefined

        this.uploadData = []
        this.validationErrors = []
        this.isImported = false

        if (this.$refs.uploadFileForm) {
          this.$refs.uploadFileForm.fileList = []
          this.$refs.uploadFileForm.dataList = []
          this.$refs.uploadFileForm.percent = 0
          this.$refs.uploadFileForm.progressStatus = 'active'
        }

        if (this.$refs.uploadResult) {
          this.$refs.uploadResult.resetState()
        }
      } else {
        this.$message.warning(this.$t('cmdb.batch.batchUploadCanceled'))
        this.isUploading = false
      }
    },
    uploadResultDone() {
      this.isUploading = false
    },
    uploadResultError(index) {
      this.hasError = true
      this.$refs.ciUploadTable.uploadResultError(index)
    },
    downloadError() {
      this.$refs.ciUploadTable.downloadError()
    },
    handleBeforeUpload() {
      this.validationErrors = []
      this.uploadData = []
      this.isImported = false
      if (this.$refs.uploadResult) {
        this.$refs.uploadResult.resetState()
      }
    },
    handleImportComplete(success) {
      this.isImported = success
    }
  },
}
</script>
<style lang="less">
@import '../index.less';
.cmdb-batch-upload-label {
  color: @text-color_1;
  font-weight: bold;
  white-space: pre;
  > span {
    color: red;
  }
}

.time-slot-selection {
  margin: 16px 0;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 4px;
  border-left: 4px solid #2f54eb;

  .tips-text {
    color: #666;
    font-size: 12px;
    margin: 8px 0 0 0;
  }
}
</style>
<style lang="less" scoped>

.cmdb-batch-upload {
  margin-bottom: -24px;
  padding: 20px;
  background-color: #fff;
  border-radius: @border-radius-box;
  overflow: auto;
  .cmdb-batch-upload-action {
    width: 50%;
    margin: 12px 0;
  }
  :deep(.table-result) {
    margin: 16px 0;
  }
}
</style>
