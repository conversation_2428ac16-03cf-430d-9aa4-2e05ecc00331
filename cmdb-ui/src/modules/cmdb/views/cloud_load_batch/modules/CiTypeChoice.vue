<template>
  <div>
    <p class="cmdb-batch-upload-label"><span>*</span>1. {{ $t('cmdb.batch.selectCIType') }}</p>
    <a-select
      showSearch
      :placeholder="$t('cmdb.batch.selectCITypeTips')"
      @change="selectCiType"
      :style="{ width: '50%', marginBottom: '1em' }"
      class="ops-select"
      :filter-option="filterOption"
      v-model="selectNum"
    >
      <a-select-opt-group v-for="group in ciTypeGroups" :key="group.name" :label="group.name">
        <a-select-option v-for="ciType in group.ci_types" :key="ciType.name" :value="ciType.id">
          {{ ciType.alias }}
        </a-select-option>
      </a-select-opt-group>
    </a-select>
    <p class="cmdb-batch-upload-label">&nbsp;&nbsp;2. {{ $t('cmdb.batch.downloadTemplate') }}</p>
    <a-button
      :style="{ marginBottom: '1em' }"
      @click="openModal"
      :disabled="!selectNum"
      type="primary"
      ghost
      class="ops-button-ghost"
      icon="download"
    >{{ $t('cmdb.batch.clickDownload') }}</a-button>
    <a-modal
      :bodyStyle="{ paddingTop: '20px' }"
      width="500px"
      :title="`${ciTypeName} ${$t('cmdb.batch.downloadTemplate')}`"
      :visible="visible"
      :footer="null"
      @cancel="handleCancel"
      wrapClassName="ci-type-choice-modal"
    >
      <div class="download-options">
        <a-button
          type="primary"
          ghost
          class="download-btn"
          icon="download"
          :disabled="!dailyAttrs.length"
          @click="handleDownload(false)"
          :style="{ marginBottom: '16px', width: '100%' }"
        >
          {{ $t('cmdb.batch.downloadDailyTemplate') }}
        </a-button>
        <a-button
          type="primary"
          ghost
          class="download-btn"
          icon="download"
          :disabled="!monthlyAttrs.length"
          @click="handleDownload(true)"
          :style="{ width: '100%' }"
        >
          {{ $t('cmdb.batch.downloadMonthlyTemplate') }}
        </a-button>
      </div>
    </a-modal>
  </div>
</template>

<script>
import _ from 'lodash'
import { mapState } from 'vuex'
import ExcelJS from 'exceljs'
import FileSaver from 'file-saver'
import { getLoadAttrByCITypeId } from '@/modules/cmdb/api/loadRelation'
import { searchPermResourceByRoleId } from '@/modules/acl/api/permission'
import { LoadValueTypes } from '@/modules/cmdb/constants'

export default {
  name: 'CiTypeChoice',
  props: {
    ciTypeGroups: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      ciTypeList: [],
      ciTypeName: '',
      selectNum: undefined,
      selectCiTypeAttrList: [],
      visible: false,
      // 按天统计相关数据
      dailyAttrs: [],
      checkedDailyAttrs: [],
      dailyIndeterminate: false,
      dailyCheckAll: true,
      // 按月统计相关数据
      monthlyAttrs: [],
      checkedMonthlyAttrs: [],
      monthlyIndeterminate: false,
      monthlyCheckAll: true,
    }
  },
  computed: {
    ...mapState({
      rid: (state) => state.user.rid,
    }),
  },
  async created() {
    const { resources } = await searchPermResourceByRoleId(this.rid, {
      resource_type_id: 'CIType',
      app_id: 'cmdb',
    })

    this.ciTypeList = this.ciTypeGroups.reduce((acc, group) => {
      if (!Array.isArray(group.ci_types)) {
        console.warn(`组 ${group.name} 的ci_types无效`)
        return acc
      }

      const filteredTypes = group.ci_types.filter((type) => {
        const _findRe = resources.find((re) => re.name === type.name)
        return _findRe?.permissions.includes('create') ?? false
      })
      return [...acc, ...filteredTypes]
    }, [])

    if (!this.ciTypeList.length) {
      console.warn('未找到具有创建权限的CI类型')
    }
  },
  watch: {
    checkedDailyAttrs() {
      this.updateCheckAllState(this.checkedDailyAttrs, this.dailyAttrs, 'daily')
    },
    checkedMonthlyAttrs() {
      this.updateCheckAllState(this.checkedMonthlyAttrs, this.monthlyAttrs, 'monthly')
    }
  },
  methods: {
    updateCheckAllState(checkedAttrs, totalAttrs, type) {
      if (checkedAttrs.length < totalAttrs.length) {
        if (type === 'daily') {
          this.dailyIndeterminate = true
          this.dailyCheckAll = false
        } else {
          this.monthlyIndeterminate = true
          this.monthlyCheckAll = false
        }
      }
      if (checkedAttrs.length === totalAttrs.length) {
        if (type === 'daily') {
          this.dailyIndeterminate = false
          this.dailyCheckAll = true
        } else {
          this.monthlyIndeterminate = false
          this.monthlyCheckAll = true
        }
      }
    },

    async selectCiType(el) {
      try {
        const response = await getLoadAttrByCITypeId(el)
        console.log('Load attributes response:', response)

        const loadAttrs = response?.attrs || []
        this.selectCiTypeAttrList = loadAttrs
        // 根据 is_monthly 分组
        this.dailyAttrs = loadAttrs.filter(attr => !attr.is_monthly)
        this.monthlyAttrs = loadAttrs.filter(attr => attr.is_monthly)
        this.$emit('getCiTypeAttr', this.selectCiTypeAttrList)

        // 检测是否需要显示时间段选择器（有日度属性时才需要）
        const needsTimeSlot = this.dailyAttrs.length > 0
        this.$emit('timeSlotSelectionChange', needsTimeSlot)

        const selectedType = this.ciTypeList.find(item => item.id === this.selectNum)
        if (selectedType) {
          this.ciTypeName = selectedType.alias || selectedType.name
        }
      } catch (error) {
        console.error('获取负载字段失败:', error)
        this.$message.error(this.$t('cmdb.batch.getLoadAttrFailed'))
        this.selectCiTypeAttrList = []
        this.dailyAttrs = []
        this.monthlyAttrs = []
      }
    },

    openModal() {
      if (!Array.isArray(this.dailyAttrs) || !Array.isArray(this.monthlyAttrs)) {
        this.dailyAttrs = []
        this.monthlyAttrs = []
      }

      this.visible = true
      // 初始化选中状态
      this.checkedDailyAttrs = this.dailyAttrs.map(item => item.alias || item.name)
      this.checkedMonthlyAttrs = this.monthlyAttrs.map(item => item.alias || item.name)
    },

    filterOption(input, option) {
      if (option.componentOptions.propsData.label) {
        return false
      }
      const text = option.componentOptions.children[0].text.toLowerCase()
      return text.indexOf(input.toLowerCase()) >= 0
    },

    handleCancel() {
      this.visible = false
    },

    handleDownload(isMonthly) {
      const attrs = isMonthly ? this.monthlyAttrs : this.dailyAttrs
      const excel_name = `${this.ciTypeName}_${isMonthly ? 'monthly' : 'daily'}_load.xlsx`
      const wb = new ExcelJS.Workbook()
      const ws = wb.addWorksheet(this.ciTypeName)

      // 定义固定列
      const fixedColumns = [
        {
          header: '主键',
          key: 'unique_value',
          width: 20
        },
        {
          header: '周期',
          key: 'period',
          width: 20,
          style: { numFmt: '@' }
        }
      ]

      // 添加属性列
      const attrColumns = attrs.map(item => ({
        header: item.alias || item.name,
        key: item.alias || item.name,
        width: 20,
        valueType: item.value_type
      }))

      // 合并所有列
      ws.columns = [...fixedColumns, ...attrColumns]

      // 设置周期列格式
      ws.getColumn(2).eachCell({ includeEmpty: false }, cell => {
        if (cell.row > 3) {
          cell.numFmt = '@'
        }
      })

      // 添加周期格式说明
      ws.getCell('B2').value = isMonthly ? 'YYYY-MM' : 'YYYY-MM-DD'
      ws.getCell('B2').font = { color: { argb: 'FF0000' }, italic: true }

      // 为每个字段添加类型提示
      attrColumns.forEach((col, index) => {
        const cellRef = ws.getCell(2, index + 3)
        let tipText = ''

        switch (col.valueType) {
          case LoadValueTypes.INT:
            tipText = '请输入整数'
            break
          case LoadValueTypes.FLOAT:
            tipText = '请输入数字'
            break
          case LoadValueTypes.TEXT:
            tipText = '请输入文本'
            break
          case LoadValueTypes.LIST:
            tipText = '请输入列表值，多个值用逗号分隔'
            break
          default:
            tipText = '请输入有效值'
        }

        cellRef.value = tipText
        cellRef.font = { color: { argb: '0000FF' }, italic: true }
      })

      wb.xlsx.writeBuffer().then((buffer) => {
        const file = new Blob([buffer], {
          type: 'application/octet-stream',
        })
        FileSaver.saveAs(file, excel_name)
        this.handleCancel()
      })
    },

    changeCheckAll(e, isMonthly) {
      const attrs = isMonthly ? this.monthlyAttrs : this.dailyAttrs
      if (!Array.isArray(attrs)) return

      if (e.target.checked) {
        if (isMonthly) {
          this.checkedMonthlyAttrs = attrs.map(item => item.alias || item.name)
        } else {
          this.checkedDailyAttrs = attrs.map(item => item.alias || item.name)
        }
      } else {
        if (isMonthly) {
          this.checkedMonthlyAttrs = []
        } else {
          this.checkedDailyAttrs = []
        }
      }
    },
  },
}
</script>

<style lang="less" scoped>
.step-form-style-desc {
  padding: 0 56px;
  color: rgba(0, 0, 0, 0.45);
  h3 {
    margin: 0 0 12px;
    color: rgba(0, 0, 0, 0.45);
    font-size: 16px;
    line-height: 32px;
  }
  h4 {
    margin: 0 0 4px;
    color: rgba(0, 0, 0, 0.45);
    font-size: 14px;
    line-height: 22px;
  }
  p {
    margin-top: 0;
    margin-bottom: 12px;
    line-height: 22px;
  }
}
</style>

<style lang="less">
.ci-type-choice-modal {
  .ant-checkbox-disabled .ant-checkbox-inner {
    border-color: #2f54eb !important;
    background-color: #2f54eb;
  }
  .ant-checkbox-disabled.ant-checkbox-checked .ant-checkbox-inner::after {
    border-color: #fff;
  }
}
</style>
