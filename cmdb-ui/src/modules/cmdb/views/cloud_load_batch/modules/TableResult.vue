<template>
  <div class="table-result">
    <!-- 汇总部分 -->
    <div class="summary-section">
      <div class="summary-title">{{ title }}</div>
      <div class="summary-content">
        <div
          v-for="(text, index) in summary"
          :key="index"
          class="summary-item"
          :class="{ 'error-text': hasFailedItems }">
          {{ text }}
        </div>
      </div>
    </div>

    <!-- 校验错误表格 -->
    <div v-if="showValidationErrors" class="table-section">
      <div class="table-header">
        <div class="table-title">{{ $t('cmdb.batch.errorDetails') }}</div>
        <div v-if="totalErrors > displayLimit" class="error-limit-notice">
          <a @click="handleDownloadErrors">{{ $t('cmdb.batch.downloadAllErrors') }}</a>
        </div>
      </div>
      <vxe-table
        ref="errorTable"
        border
        size="small"
        show-overflow="tooltip"
        :data="displayErrors"
        :auto-resize="true"
        class="ops-unstripe-table"
      >
        <vxe-column field="record" :title="$t('cmdb.batch.originalRecord')" min-width="200">
          <template v-slot="{ row }">
            <a-tooltip :title="row.record">
              <span class="record-content">{{ row.record }}</span>
            </a-tooltip>
          </template>
        </vxe-column>
        <vxe-column field="error" :title="$t('cmdb.batch.errorMessage')" min-width="150">
          <template v-slot="{ row }">
            <span class="error-message">{{ row.error }}</span>
          </template>
        </vxe-column>
      </vxe-table>

      <!-- 校验错误分页 -->
      <div :style="{ textAlign: 'right', marginTop: '4px' }">
        <a-pagination
          :current="errorPagination.current"
          :total="totalErrors"
          :pageSize="errorPagination.pageSize"
          size="small"
          @change="handleErrorPageChange"
        />
      </div>
    </div>

    <!-- 导入历史记录表格 -->
    <div v-else-if="showHistoryTable" class="table-section">
      <div class="table-header">
        <a-button
          type="link"
          :loading="loading"
          @click="refreshHistory"
        >
          <template #icon><a-icon type="reload" /></template>
          {{ $t('refresh') }}
        </a-button>
      </div>
      <vxe-table
        ref="xTable"
        :loading="loading"
        border
        size="small"
        show-overflow="tooltip"
        :data="historyData"
        :auto-resize="true"
        class="ops-unstripe-table"
      >
        <vxe-column field="id" :title="$t('ID')" width="80"></vxe-column>
        <vxe-column field="file_name" :title="$t('cmdb.cloud_load.fileName')" min-width="120"></vxe-column>
        <vxe-column field="status" :title="$t('cmdb.cloud_load.status')" width="110">
          <template v-slot="{ row }">
            <a-tag :color="getStatusColor(row.status)">
              {{ getStatusText(row.status) }}
            </a-tag>
          </template>
        </vxe-column>
        <vxe-column field="total_count" :title="$t('cmdb.cloud_load.totalCount')" width="100"></vxe-column>
        <vxe-column field="success_count" :title="$t('cmdb.cloud_load.successCount')" width="100"></vxe-column>
        <vxe-column field="error_count" :title="$t('cmdb.cloud_load.errorCount')" width="100"></vxe-column>
        <vxe-column field="start_time" :title="$t('cmdb.cloud_load.startTime')" width="160">
          <template v-slot="{ row }">
            {{ formatDateTime(row.start_time) }}
          </template>
        </vxe-column>
        <vxe-column field="end_time" :title="$t('cmdb.cloud_load.endTime')" width="160">
          <template v-slot="{ row }">
            {{ formatDateTime(row.end_time) }}
          </template>
        </vxe-column>
        <vxe-column field="user" :title="$t('cmdb.cloud_load.user')" width="100"></vxe-column>
        <vxe-column field="error_message" :title="$t('cmdb.cloud_load.errorMessage')" min-width="200">
          <template v-slot="{ row }">
            <a-tooltip v-if="row.error_message" :title="row.error_message">
              <span class="error-message">{{ row.error_message }}</span>
            </a-tooltip>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
  </div>
</template>

<script>
import ExcelJS from 'exceljs'
import FileSaver from 'file-saver'
import moment from 'moment'
import { getHistoryDetail } from '@/modules/cmdb/api/loadHistory'
import { HistoryStatusEnum, HistoryStatusOptions } from '@/modules/cmdb/constants'

export default {
  name: 'TableResult',
  props: {
    title: {
      type: String,
      required: true
    },
    summary: {
      type: Array,
      default: () => []
    },
    errors: {
      type: Array,
      default: () => []
    },
    historyId: {
      type: [Number, String],
      default: null
    }
  },

  data() {
    return {
      displayLimit: 1000,
      loading: false,
      historyData: [],
      pagination: {
        current: 1,
        pageSize: 50,
        total: 0
      },
      errorPagination: {
        current: 1,
        pageSize: 10
      },
      pageSizeOptions: ['50', '100', '200', '100000']
    }
  },

  computed: {
    showValidationErrors() {
      return this.errors && this.errors.length > 0
    },
    showHistoryTable() {
      return this.historyId !== null
    },
    totalErrors() {
      return this.errors.length
    },
    displayErrors() {
      const start = (this.errorPagination.current - 1) * this.errorPagination.pageSize
      const end = start + this.errorPagination.pageSize
      return this.errors.slice(start, end)
    },
    hasFailedItems() {
      const failedItem = this.summary.find(text => text.includes(this.$t('cmdb.batch.failedItems')))
      if (failedItem) {
        const failedCount = parseInt(failedItem.split(':')[1].trim())
        return failedCount > 0
      }
      return false
    }
  },

  watch: {
    historyId: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.fetchHistoryDetail(newVal)
        }
      }
    }
  },

  methods: {
    refreshHistory() {
      if (this.historyId) {
        this.fetchHistoryDetail(this.historyId)
      }
    },

    async fetchHistoryDetail(historyId) {
      if (!this.showHistoryTable) return

      this.loading = true
      try {
        const result = await getHistoryDetail(historyId)
        if (result) {
          // 将单个历史记录转换为数组格式以适配表格显示
          this.historyData = [result]
          this.pagination.total = 1
        } else {
          this.historyData = []
          this.pagination.total = 0
        }
      } catch (error) {
        console.error('Query history failed:', error)
        this.$message.error(this.$t('cmdb.cloud_load.queryFailed'))
      } finally {
        this.loading = false
      }
    },

    formatDateTime(datetime) {
      return datetime ? moment(datetime).format('YYYY-MM-DD HH:mm:ss') : ''
    },

    getStatusColor(status) {
      const colors = {
        [HistoryStatusEnum.BEGINSYNC]: 'blue',
        [HistoryStatusEnum.PENDING]: 'blue',
        [HistoryStatusEnum.PROCESSING]: 'orange',
        [HistoryStatusEnum.COMPLETED]: 'green',
        [HistoryStatusEnum.FAILED]: 'red'
      }
      return colors[status] || 'default'
    },

    getStatusText(status) {
      return HistoryStatusOptions.find(item => item.value === status)?.label || status
    },

    async handleDownloadErrors() {
      try {
        const workbook = new ExcelJS.Workbook()
        const worksheet = workbook.addWorksheet('错误记录')

        worksheet.columns = [
          { header: '原始记录', key: 'record', width: 80 },
          { header: '错误信息', key: 'error', width: 40 }
        ]

        this.errors.forEach(error => {
          worksheet.addRow({
            record: error.record,
            error: error.error
          })
        })

        const buffer = await workbook.xlsx.writeBuffer()
        const blob = new Blob([buffer], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })
        FileSaver.saveAs(blob, `错误记录-${new Date().getTime()}.xlsx`)

        this.$message.success(this.$t('exportSuccess'))
      } catch (error) {
        console.error('下载错误记录失败:', error)
        this.$message.error(this.$t('cmdb.batch.downloadErrorsFailed'))
      }
    },

    handleErrorPageChange(page) {
      this.errorPagination.current = page
    }
  }
}
</script>

<style lang="less" scoped>
.table-result {
  margin: 16px 0;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

  .summary-section {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;

    .summary-title {
      font-size: 14px;
      font-weight: bold;
      color: rgba(0, 0, 0, 0.85);
      margin-bottom: 16px;
      line-height: 22px;
    }

    .summary-content {
      display: flex;
      flex-wrap: wrap;
      gap: 32px;
      line-height: 20px;

      .summary-item {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);

        &.error-text {
          color: #ff4d4f;
          font-weight: bold;
        }
      }
    }
  }

  .table-section {
    padding: 16px;

    .table-header {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-bottom: 12px;
      padding: 0 4px;

      .ant-btn-link {
        padding: 0 8px;
        height: 24px;
        line-height: 24px;

        .anticon {
          font-size: 14px;
        }
      }

      .error-limit-notice {
        font-size: 12px;
        color: #999;

        a {
          margin-left: 8px;
          color: #1890ff;
          cursor: pointer;
        }
      }
    }
  }

  .ops-unstripe-table {
    :deep(.vxe-table--header-wrapper) {
      background-color: #fafafa;
    }

    :deep(.vxe-table--body-wrapper) {
      background-color: #fff;
    }

    .record-content {
      display: inline-block;
      max-width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .error-message {
      color: #ff4d4f;
    }
  }
}
</style>
