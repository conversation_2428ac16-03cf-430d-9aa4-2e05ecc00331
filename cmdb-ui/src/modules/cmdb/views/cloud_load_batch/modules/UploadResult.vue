<template>
  <TableResult
    v-if="visible"
    :title="$t('cmdb.batch.importResult')"
    :summary="importSummary"
    :errors="errorItems"
    :historyId="importedHistoryId"
  />
</template>

<script>
import TableResult from './TableResult.vue'
import { importLoadData } from '@/modules/cmdb/api/loadData'

export default {
  name: 'UploadResult',
  components: {
    TableResult
  },
  props: {
    upLoadData: {
      required: true,
      type: Array,
    },
    ciType: {
      required: true,
      type: Number,
    },
    uniqueField: {
      required: true,
      type: String,
    },
    isUploading: {
      type: Boolean,
      default: false,
    },
    timeSlot: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      visible: false,
      errorNum: 0,
      success: 0,
      total: 0,
      errorItems: [],
      importResult: null,
      importedHistoryId: null
    }
  },
  computed: {
    importSummary() {
      if (this.importedHistoryId || !this.importResult) return []

      return [
        `${this.$t('cmdb.batch.total')}: ${this.total}`,
        `${this.$t('cmdb.batch.successItems')}: ${this.success}`,
        `${this.$t('cmdb.batch.newRecords')}: ${this.importResult.inserted}`,
        `${this.$t('cmdb.batch.updatedRecords')}: ${this.importResult.updated}`,
        `${this.$t('cmdb.batch.failedItems')}: ${this.errorNum}`
      ]
    }
  },
  methods: {
    resetState() {
      this.visible = false
      this.errorNum = 0
      this.success = 0
      this.total = 0
      this.errorItems = []
      this.importResult = null
      this.importedHistoryId = null
    },

    async upload2Server() {
      try {
        if (!this.isUploading || !this.upLoadData.length) {
          return
        }

        this.visible = true

        // 重构数据结构
        const groupedData = this.upLoadData.reduce((acc, item) => {
          const uniqueValue = item['主键']
          const period = item['周期']

          if (!acc[uniqueValue]) {
            acc[uniqueValue] = {
              unique_value: uniqueValue
            }
          }

          if (!acc[uniqueValue][period]) {
            acc[uniqueValue][period] = {}
          }

          Object.entries(item).forEach(([field, value]) => {
            if (field !== '主键' && field !== '周期' && value !== undefined && value !== null) {
              acc[uniqueValue][period][field] = value
            }
          })

          return acc
        }, {})

        const formattedData = Object.values(groupedData)

        const fileName = this.$parent.$refs.uploadFileForm?.getFileName() || 'unknown.xlsx'
        const response = await importLoadData(this.ciType, formattedData, fileName, this.timeSlot)

        this.importResult = {
          inserted: response.inserted || 0,
          updated: response.updated || 0,
          failed: response.failed || 0
        }

        this.success = response.success || 0
        this.errorNum = response.failed || 0
        this.total = (response.success || 0) + (response.failed || 0)
        this.importedHistoryId = response.history_id

        if (response.errors && response.errors.length) {
          this.errorItems = response.errors.map(error => ({
            error: error.error || error.message || String(error),
            record: typeof error.record === 'string' ? error.record : JSON.stringify(error.record)
          }))
        }

        this.$emit('uploadResultDone')
        this.$emit('validationComplete', this.errorNum === 0)
        this.$emit('importComplete', true)
      } catch (error) {
        console.error('Upload failed:', error)
        this.errorNum = this.total
        this.success = 0
        this.errorItems = [{
          error: error.response?.data?.message || error.message || this.$t('cmdb.batch.requestFailedTips'),
          record: JSON.stringify(error.response?.data?.record || {})
        }]
        this.$emit('uploadResultDone')
        this.$emit('validationComplete', false)
        this.$emit('importComplete', false)
      }
    }
  }
}
</script>
