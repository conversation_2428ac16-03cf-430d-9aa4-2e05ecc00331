<template>
  <CiDetailDrawer ref="ciDetailDrawer" :typeId="typeId" />
</template>

<script>
import CiDetailDrawer from '@/modules/cmdb/views/ci/modules/ciDetailDrawer.vue'
import { getCITypeAttributesById } from '@/modules/cmdb/api/CITypeAttr'

export default {
  name: 'ResourceCiDetailDrawer',
  components: {
    CiDetailDrawer
  },
  props: {
    typeId: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      attrList: [],
      attributes: {
        unique_id: 'name',
        unique: 'name'
      }
    }
  },
  provide() {
    return {
      attributes: () => {
        return this.attributes
      },
      attrList: () => {
        return this.attrList
      }
    }
  },
  methods: {
    async getAttributeList(typeId) {
      try {
        const res = await getCITypeAttributesById(typeId)
        this.attrList = res.attributes
        this.attributes = res
      } catch (error) {
        console.error('Failed to get attributes:', error)
      }
    },

    async create(ciId, activeTabKey, ciDetailRelationKey, customTypeId) {
      // 如果提供了自定义typeId，获取该类型的属性
      if (customTypeId) {
        await this.getAttributeList(customTypeId)
      }

      this.$refs.ciDetailDrawer.create(ciId, activeTabKey, ciDetailRelationKey, customTypeId)
    }
  }
}
</script>
