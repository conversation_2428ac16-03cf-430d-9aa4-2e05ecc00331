<template>
  <div class="resource-search">

    <template v-if="isInit">
      <RelationSearch
        v-show="tabActive === 'relationSearch'"
        :CITypeGroup="CITypeGroup"
        :allCITypes="allCITypes"
      />
    </template>
  </div>
</template>

<script>
import { getCITypeGroups } from '@/modules/cmdb/api/ciTypeGroup'
import { getCITypes } from '@/modules/cmdb/api/CIType'
import { mapState } from 'vuex'

import RelationSearch from './relationSearch/index.vue'

export default {
  name: 'ResourceSearch',
  components: {
    RelationSearch
  },
  data() {
    return {
      tabActive: 'relationSearch',
      tabList: [
        {
          lable: 'cmdb.relationSearch.relationSearch',
          value: 'relationSearch'
        }
      ],
      CITypeGroup: [],
      allCITypes: [],
      isInit: false,
    }
  },
  computed: {
    ...mapState({
      cmdbSearchValue: (state) => state.app.cmdbSearchValue,
    }),
  },
  watch: {
    cmdbSearchValue: {
      immediate: true,
      deep: true,
      handler() {
        this.tabActive = 'relationSearch'
      }
    }
  },
  async mounted() {
    try {
      await Promise.all([
        this.getCITypeGroups(),
        this.getAllCITypes()
      ])
    } catch (error) {
      console.log('resource search mounted fail', error)
    }

    this.isInit = true
  },
  methods: {
    async getCITypeGroups() {
      const res = await getCITypeGroups({ need_other: true })
      console.log('res', res)

      this.CITypeGroup = res
        .filter(group => group.name === '基础信息')
        .map(group => ({
          ...group,
          id: `parent_${group.id || -1}`,
          ci_types: group.ci_types.filter(type => type.name === 'units')
        }))
        .filter(group => group.ci_types.length > 0)
    },

    async getAllCITypes() {
      const res = await getCITypes()
      this.allCITypes = res?.ci_types
    },
  },
}
</script>

<style lang="less" scoped>
.resource-search {
  width: 100%;
  height: 100%;

  &-tab {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    &-item {
      padding-right: 8px;
      margin-right: 8px;
      font-size: 14px;
      font-weight: 400;
      color: #86909C;
      cursor: pointer;

      &:not(:last-child) {
        border-right: solid 1px #E4E7ED;
      }

      &:hover {
        color: #2F54EB;
      }

      &_active {
        color: #2F54EB;
      }
    }
  }
}
</style>
