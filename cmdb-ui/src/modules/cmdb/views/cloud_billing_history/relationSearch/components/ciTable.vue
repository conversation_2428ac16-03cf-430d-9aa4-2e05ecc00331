<template>
  <div class="search-table">
    <div class="search-table-header">
      <div class="table-tab">
        <div
          v-for="(tab) in tabList"
          :key="tab.value"
          :class="['table-tab-item', tabActive === tab.value ? 'table-tab-item_active' : '']"
          @click="clickTab(tab.value)"
        >
          {{ tab.value }}
          (<span class="table-tab-item-count">{{ tab.count }}</span>)
        </div>
      </div>

      <div class="search-table-buttons">
        <a-button
          v-if="tableData.ciList && tableData.ciList.length"
          type="primary"
          class="ops-button-ghost search-table-export"
          ghost
          @click="handleExport"
        >
          <ops-icon type="veops-export" />
          {{ $t('export') }}
        </a-button>
        <a-button
          v-if="showExportAllButton"
          type="primary"
          class="ops-button-ghost search-table-export-all"
          ghost
          :loading="isExportingAll"
          @click="handleExportAll"
        >
          <ops-icon type="veops-export" />
          {{ $t('exportAll') }}({{ totalCount || 0 }})
        </a-button>
      </div>
    </div>

    <ops-table
      ref="xTable"
      show-overflow
      :data="tableData.ciList"
      size="small"
      :height="`${tableHeight}px`"
      :cell-class-name="getCellClassName"
      :header-cell-class-name="getHeaderCellClassName"
      :checkbox-config="{ range: true }"
      :column-config="{ resizable: true }"
      :resizable-config="{ minWidth: 60 }"
      class="checkbox-hover-table"
      :scroll-y="{ enabled: true, gt: 100 }"
      :scroll-x="{ enabled: true }"
      :virtual-scroll="{ enabled: true }"
      :row-config="{
        keyField: 'id',
        height: 48
      }"
    >
      <vxe-table-column
        v-if="tableData.ciList && tableData.ciList.length"
        align="center"
        type="checkbox"
        width="60"
      >
        <template #default="{row}">
          {{ getRowSeq(row) }}
        </template>
      </vxe-table-column>

      <template
        v-if="returnPath && filteredPathList.length"
      >
        <vxe-table-column
          v-for="(path, index) in filteredPathList"
          class="table-path-column"
          :key="`${path.id}-${index}`"
          :title="path.name"
          :field="path.id"
          :show-header-overflow="false"
          :width="path.isMultiField ? 120 : 160"
        >
          <template #header>
            <div class="table-path-header">
              <span
                class="table-path-header-name"
                :style="{
                  maxWidth: path.relation ? '70px' : '100%'
                }"
              >
                <a-tooltip :title="path.name">
                  {{ path.name }}
                </a-tooltip>
              </span>
              <!-- <div
                v-if="path.relation"
                class="table-path-header-right"
              >
                <span class="table-path-header-line">
                  <a-icon
                    type="caret-right"
                    class="table-path-header-line-arrow"
                  />
                </span>
                <span class="table-path-header-relation">
                  <span class="table-path-header-relation-text">
                    <a-tooltip :title="path.relation">
                      {{ path.relation }}
                    </a-tooltip>
                  </span>
                </span>
              </div> -->
            </div>
          </template>
          <template #default="{ row, columnIndex }">
            <template v-if="columnIndex === 1">
              <span v-html="markSearchValue(row.pathCI[path.id])"></span>
            </template>
            <template v-else>
              <span>{{ row.pathCI[path.id] }}</span>
            </template>
          </template>
        </vxe-table-column>
      </template>

      <template v-if="tableData.ciAttr && tableData.ciAttr.length">
        <vxe-table-column
          v-for="(attr, index) in tableData.ciAttr"
          :key="`${attr.name}_${index}`"
          :title="attr.alias || attr.name || ''"
          :field="attr.name"
          :width="attr.width"
          :show-header-overflow="true"
        >
          <template #default="{ row }">
            <AttrDisplay
              :attr="attr"
              :ci="row.targetCI"
              :referenceShowAttrNameMap="referenceShowAttrNameMap"
              :referenceCIIdMap="referenceCIIdMap"
            />
          </template>
        </vxe-table-column>
      </template>
    </ops-table>

    <BatchDownload
      ref="batchDownload"
      :showFileTypeSelect="false"
      @batchDownload="batchDownload"
    />

    <div v-if="isSearchLoading" class="search-loading-overlay">
      <a-spin :spinning="true">
        <div class="loading-content">
          <p>{{ loadingMessage }}</p>
          <a-progress
            v-if="loadingProgress > 0"
            :percent="loadingProgress"
            :status="loadingProgress >= 100 ? 'success' : 'active'"
          />
        </div>
      </a-spin>
    </div>
  </div>
</template>

<script>
import _ from 'lodash'
import moment from 'moment'
import { mapState } from 'vuex'
import ExcelJS from 'exceljs'
import FileSaver from 'file-saver'
import AttrDisplay from '@/modules/cmdb/views/resource_search_2/resourceSearch/components/attrDisplay.vue'
import BatchDownload from '@/modules/cmdb/components/batchDownload/batchDownload.vue'
import { searchCIRelationPath } from '@/modules/cmdb/api/cloudBilling'

export default {
  name: 'CITable',
  components: {
    AttrDisplay,
    BatchDownload
  },
  props: {
    allTableData: {
      type: Object,
      default: () => {}
    },
    tabActive: {
      type: String,
      default: ''
    },
    returnPath: {
      type: Boolean,
      default: false
    },
    isHideSearchCondition: {
      type: Boolean,
      default: false,
    },
    referenceShowAttrNameMap: {
      type: Object,
      default: () => {}
    },
    referenceCIIdMap: {
      type: Object,
      default: () => {}
    },
    searchValue: {
      type: String,
      default: ''
    },
    isSearchLoading: {
      type: Boolean,
      default: false
    },
    totalNumber: {
      type: Number,
      default: 0
    },
    loadingMessage: {
      type: String,
      default: '加载中...'
    },
    loadingProgress: {
      type: Number,
      default: 0
    }
  },
  computed: {
    ...mapState({
      windowHeight: (state) => state.windowHeight,
    }),
    tableHeight() {
      return this.isHideSearchCondition ? this.windowHeight - 308 : this.windowHeight - 458
    },
    tableData() {
      const data = this.allTableData?.[this.tabActive] || {}
      return data
    },
    tabList() {
      const keys = Object.keys(this.allTableData) || []
      return keys.map((key) => {
        return {
          value: key,
          count: this.allTableData?.[key]?.count || 0
        }
      })
    },
    totalCount() {
      return this.totalNumber || 0
    },
    showExportAllButton() {
      return this.$parent.pageSize === 1000000 && Object.values(this.allTableData).some(data => data.count > 0)
    },
    filteredPathList() {
      if (!this.tableData?.pathList) return []
      const filtered = this.tableData.pathList.slice(0, -1)
      return filtered
    }
  },
  data() {
    return {
      scrollEndTimer: null,
      isExportingAll: false,
      exportAllCancelToken: null,
    }
  },
  created() {
    this.debouncedMarkSearchValue = _.debounce(this.markSearchValue, 200)
  },
  mounted() {
    // this.$nextTick(() => {
    //   this.checkVirtualScroll()
    // })
  },
  methods: {
    markSearchValue(text) {
      if (!text || !this.searchValue) {
        return text
      }
      try {
        const regex = new RegExp(`(${_.escapeRegExp(this.searchValue)})`, 'gi')
        return String(text).replace(
          regex,
          `<span style="background-color: #D3EEFE; padding: 0 2px;">$1</span>`
        )
      } catch (e) {
        return text
      }
    },

    handleScrollEnd() {
      if (this.scrollEndTimer) {
        clearTimeout(this.scrollEndTimer)
      }
      this.scrollEndTimer = setTimeout(() => {
        const table = this.$refs.xTable?.getVxetableRef()
        if (table) {
          table.recalculate()
        }
      }, 100)
    },

    clickTab(tab) {
      this.$emit('updateTab', tab)
    },

    getRowSeq(row) {
      const table = this.$refs?.['xTable']?.getVxetableRef?.() || null
      return table?.getRowSeq?.(row)
    },
    getCellClassName({ columnIndex }) {
      const pathLength = this.filteredPathList?.length
      if (columnIndex <= pathLength && this.returnPath) {
        return 'table-path-cell'
      }
      return ''
    },
    getHeaderCellClassName({ columnIndex }) {
      const pathLength = this.filteredPathList?.length
      if (columnIndex <= pathLength && this.returnPath) {
        return 'table-path-header-cell'
      }
      return ''
    },

    handleExport() {
      const preferenceAttrList = []
      if (this.returnPath && this.filteredPathList?.length) {
        preferenceAttrList.push(...this.filteredPathList.map((path) => {
          return {
            name: path.id,
            alias: path.name
          }
        }))
      }

      if (this.tableData?.ciAttr?.length) {
        const ciAttr = _.cloneDeep(this.tableData.ciAttr)
        ciAttr.forEach((attr) => {
          attr.alias = attr.alias || attr.name
        })
        preferenceAttrList.push(...ciAttr)
      }

      this.$refs.batchDownload.open({
        preferenceAttrList,
        ciTypeName: this.tabActive || '',
      })
    },
    async batchDownload({ checkedKeys }) {
      try {
        const excel_name = `cmdb-${this.tabActive}-${moment().format('YYYYMMDDHHmmss')}.xlsx`
        const wb = new ExcelJS.Workbook()
        const ws = wb.addWorksheet(this.tabActive)

        // 获取导出数据
        const tableRef = this.$refs.xTable.getVxetableRef()
        const exportData = _.cloneDeep([
          ...tableRef.getCheckboxReserveRecords(),
          ...tableRef.getCheckboxRecords(true),
        ])
        if (!exportData.length) {
          const { fullData } = tableRef.getTableData()
          const batchSize = 5000
          for (let i = 0; i < fullData.length; i += batchSize) {
            const batch = fullData.slice(i, i + batchSize)
            exportData.push(..._.cloneDeep(batch))
          }
        }

        const columns = []

        // 处理路径列，保持与表格显示一致
        if (this.returnPath && this.filteredPathList?.length) {
          // 只导出选中的列
          this.filteredPathList.forEach((path) => {
            if (checkedKeys.includes(path.id)) {
              columns.push({
                header: path.name,
                key: path.id,
                width: 20
              })
            }
          })
        }

        // 处理属性列
        if (this.tableData?.ciAttr?.length) {
          this.tableData.ciAttr.forEach((attr) => {
            if (checkedKeys.includes(attr.name)) {
              columns.push({
                header: attr.alias || attr.name,
                key: attr.name,
                width: 20
              })
            }
          })
        }

        ws.columns = columns

        // 写入数据
        exportData.forEach(({ pathCI, targetCI }) => {
          const row = {}
          // 写入路径数据，与表显示保持一致
          columns.forEach((column) => {
            if (pathCI && pathCI.hasOwnProperty(column.key)) {
              row[column.key] = pathCI[column.key] ?? ''
            } else if (targetCI && targetCI.hasOwnProperty(column.key)) {
              const value = targetCI[column.key]
              const attr = this.tableData.ciAttr.find(a => a.name === column.key)
              if (attr?.valueType === '6') {
                row[column.key] = value ? JSON.stringify(value) : ''
              } else if (attr?.is_list && Array.isArray(value)) {
                row[column.key] = value.join(',')
              } else {
                row[column.key] = value ?? ''
              }
            }
          })

          ws.addRow(row)
        })

        // 导出文件
        wb.xlsx.writeBuffer().then((buffer) => {
          const file = new Blob([buffer], {
            type: 'application/octet-stream',
          })
          FileSaver.saveAs(file, excel_name)
        })
      } catch (error) {
        console.error('Export failed:', error)
      } finally {
        this.$message.info(this.$t('cmdb.cloudBillingHistory.startExporting')) // 这个页面的消息提醒功能目前都异常。
        this.$refs.xTable.getVxetableRef().clearCheckboxRow()
        this.$refs.xTable.getVxetableRef().clearCheckboxReserve()
      }
    },
    async handleExportAll() {
      try {
        this.isExportingAll = true
        const allTabs = Object.keys(this.allTableData)
        const workbook = new ExcelJS.Workbook()

        for (const tab of allTabs) {
          const tabData = this.allTableData[tab]
          if (!tabData?.count) continue // 跳过没有数据的标签页

          // 直接使用已有数据
          const ws = workbook.addWorksheet(tab)
          const columns = []

          // 处理路径列
          if (this.returnPath && tabData?.pathList?.length) {
            const filteredPaths = tabData.pathList.slice(0, -1)
            filteredPaths.forEach((path) => {
              columns.push({
                header: path.name,
                key: path.id,
                width: 20
              })
            })
          }

          // 处理属性列
          if (tabData?.ciAttr?.length) {
            tabData.ciAttr.forEach((attr) => {
              columns.push({
                header: attr.alias || attr.name,
                key: attr.name,
                width: 20
              })
            })
          }

          ws.columns = columns

          // 写入数据
          const exportData = tabData.ciList || []
          exportData.forEach(({ pathCI, targetCI }) => {
            const row = {}
            // 写入路径数据
            if (this.returnPath) {
              const filteredPaths = tabData.pathList.slice(0, -1)
              filteredPaths.forEach((path) => {
                row[path.id] = pathCI[path.id] ?? ''
              })
            }

            // 写入属性数据
            tabData.ciAttr.forEach((attr) => {
              const value = targetCI[attr.name]
              if (attr.valueType === '6') {
                row[attr.name] = value ? JSON.stringify(value) : ''
              } else if (attr.is_list && Array.isArray(value)) {
                row[attr.name] = value.join(',')
              } else {
                row[attr.name] = value ?? ''
              }
            })

            ws.addRow(row)
          })
        }

        const buffer = await workbook.xlsx.writeBuffer()
        const fileName = `cmdb-all-${moment().format('YYYYMMDDHHmmss')}.xlsx`
        const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
        FileSaver.saveAs(blob, fileName)
        this.$message.success(this.$t('exportSuccess'))
      } catch (error) {
        console.error('Export all failed:', error)
        this.$message.error(this.$t('exportFailed'))
      } finally {
        this.isExportingAll = false
        this.$message.destroy('exportProgress')
      }
    },
    getExportAttrList() {
      const preferenceAttrList = []
      if (this.returnPath && this.tableData?.pathList?.length) {
        preferenceAttrList.push(...this.tableData.pathList.map((path) => ({
          name: path.id,
          alias: path.name
        })))
      }

      if (this.tableData?.ciAttr?.length) {
        const ciAttr = _.cloneDeep(this.tableData.ciAttr)
        ciAttr.forEach((attr) => {
          attr.alias = attr.alias || attr.name
        })
        preferenceAttrList.push(...ciAttr)
      }
      return preferenceAttrList
    },
    beforeDestroy() {
      if (this.exportAllCancelToken) {
        this.exportAllCancelToken.cancel()
      }
    },
    checkVirtualScroll() {
      const tableRef = this.$refs.xTable?.getVxetableRef()
      if (tableRef) {
        // 打印初始状态
        console.log('Table virtual scroll status:', {
          hasVirtualClass: tableRef.$el.classList.contains('vxe-table--virtual-scroll'),
          renderedRows: tableRef.$el.querySelectorAll('.vxe-body--row').length,
          totalRows: this.tableData?.ciList?.length || 0
        })

        const bodyWrapper = tableRef.$el.querySelector('.vxe-table--body-wrapper')
        if (bodyWrapper) {
          bodyWrapper.addEventListener('scroll', _.throttle(() => {
            const visibleRows = tableRef.$el.querySelectorAll('.vxe-body--row').length
            const totalRows = this.tableData?.ciList?.length || 0
            const scrollTop = bodyWrapper.scrollTop
            const scrollHeight = bodyWrapper.scrollHeight
            const clientHeight = bodyWrapper.clientHeight

            console.log('Scroll status:', {
              visibleRows,
              totalRows,
              scrollTop,
              scrollHeight,
              clientHeight,
              scrollPercentage: ((scrollTop + clientHeight) / scrollHeight * 100).toFixed(2) + '%'
            })
          }, 200)) // 使用节流避免过多日志
        }
      }
    },
    getPathCellValue(row, path) {
      if (path.isMultiField) {
        return row.pathCI[path.id] // 使用组合ID获取值
      }
      return row.pathCI[path.typeId] // 使用类型ID获取默认值
    },
    // 添加一个工具函数来统一获取字段值
    getPathFieldValue(ci, pathField) {
      if (!ci || !pathField) return ''
      return ci[pathField.fieldName] ?? ''
    },
    // 添加一个工具函数来统一处理 CI 数据
    processPathCI(ids, response) {
      const pathCI = {}
      ids.forEach((id) => {
        const ci = response?.id2ci?.[id] || {}
        const typeId = ci._type
        const multiShowKeys = response?.type2multishow_key?.[typeId] || []
        const defaultShowKey = response?.type2show_key?.[typeId]
        if (multiShowKeys.length) {
          multiShowKeys.forEach(key => {
            pathCI[`${typeId}_${key}`] = ci[key]
          })
        } else {
          pathCI[String(typeId)] = ci[defaultShowKey] ?? ''
        }
      })
      return pathCI
    }
  }
}
</script>

<style lang="less" scoped>
.search-table {
  width: 100%;

  &-header {
    display: flex;
    align-items: baseline;
    justify-content: space-between
  }

  &-export {
    flex-shrink: 0;
    margin-left: 12px;
  }

  &-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  &-export-all {
    margin-left: 8px;
  }

  .table-tab {
    display: flex;
    align-items: center;
    column-gap: 35px;
    padding-bottom: 6px;
    margin-bottom: 18px;
    max-width: 100%;
    overflow-x: auto;
    overflow-y: hidden;

    &-item {
      font-size: 14px;
      font-weight: 400;
      color: #4E5969;
      cursor: pointer;
      flex-shrink: 0;

      &-count {
        color: #2F54EB;
      }

      &_active {
        color: #2F54EB;
      }

      &:hover {
        color: #2F54EB;
      }
    }
  }

  .table-path-header {
    position: relative;
    display: flex;
    align-items: center;

    &-name {
      max-width: 100% !important; // 修改最大宽度，因为现在每列只显示一个字段
      overflow: hidden;
      text-overflow: ellipsis;
      text-wrap: nowrap;
      position: relative;
      z-index: 1;
      flex-shrink: 0;
    }

    &-right {
      display: flex;
      align-items: center;
      width: 100%;
      margin-left: 10px;
      margin-right: -5px;
      position: relative;
    }

    &-line {
      width: 100%;
      height: 1px;
      position: relative;
      background-color: #CACDD9;
      z-index: 0;

      &-arrow {
        position: absolute;
        right: -6px;
        top: -6px;
        font-size: 12px;
        color: #CACDD9;
      }
    }

    &-relation {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-color: #FFFFFF;
      border: solid 1px #E4E7ED;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 8px;
      border-radius: 22px;
      z-index: 2;
      max-width: 70px;
      width: fit-content;

      &-text {
        font-size: 12px;
        font-weight: 400;
        color: #A5A9BC;

        overflow: hidden;
        text-overflow: ellipsis;
        text-wrap: nowrap;
        width: 100%;
      }
    }
  }

  .checkbox-hover-table {
    /deep/ .vxe-table--body-wrapper {
      .vxe-checkbox--label {
        display: inline;
        padding-left: 0px !important;
        color: #bfbfbf;
      }

      .vxe-icon-checkbox-unchecked {
        display: none;
      }

      .vxe-icon-checkbox-checked ~ .vxe-checkbox--label {
        display: none;
      }

      .vxe-cell--checkbox {
        &:hover {
          .vxe-icon-checkbox-unchecked {
            display: inline;
          }

          .vxe-checkbox--label {
            display: none;
          }
        }
      }
    }
  }

  /deep/ .table-path-header-cell {
    background-color: #EBEFF8 !important;

    .vxe-cell--title {
      width: 100%;
      overflow: visible;
    }
  }

  /deep/ .table-path-cell {
    background-color: #F9FBFF;
  }

  /deep/ .attr-display {
    display: inline;
  }

  .multi-field-cell {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 4px;
  }

  .multi-field-item {
    display: inline-flex;
    align-items: center;
  }

  .multi-field-separator {
    margin: 0 4px;
    color: #8c8c8c;
  }

  .search-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;

    .loading-content {
      text-align: center;

      p {
        margin-bottom: 16px;
      }
    }
  }
}
</style>
