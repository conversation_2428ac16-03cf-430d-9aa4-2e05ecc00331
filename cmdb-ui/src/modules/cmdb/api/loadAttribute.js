import { axios } from '@/utils/request'

/**
 * 获取负载字段列表
 * @param {Object} params 查询参数
 * @param {number} params.page 页码
 * @param {number} params.page_size 每页大小
 * @param {string} params.name 字段名称(可选)
 * @param {string} params.value_type 值类型(可选)
 * @returns {Promise} 返回字段列表和分页信息
 */
export function getLoadAttributes(params) {
  return axios({
    url: '/v0.1/load/attributes',
    method: 'GET',
    params: params
  })
}

/**
 * 创建负载字段
 * @param {Object} payload 字段信息
 * @param {string} payload.name 字段名称
 * @param {string} payload.value_type 值类型
 * @param {string} payload.alias 字段别名(可选)
 * @param {boolean} payload.is_monthly 是否为月度字段(可选)
 * @returns {Promise} 返回创建的字段信息
 */
export function createLoadAttribute(payload) {
  return axios({
    url: '/v0.1/load/attributes',
    method: 'POST',
    data: payload
  })
}

/**
 * 更新负载字段
 * @param {number} attrId 字段ID
 * @param {Object} payload 更新信息
 * @param {string} payload.name 字段名称(可选)
 * @param {string} payload.value_type 值类型(可选)
 * @param {string} payload.alias 字段别名(可选)
 * @param {boolean} payload.is_monthly 是否为月度字段(可选)
 * @returns {Promise} 返回更新后的字段信息
 */
export function updateLoadAttribute(attrId, payload) {
  return axios({
    url: `/v0.1/load/attributes/${attrId}`,
    method: 'PUT',
    data: payload
  })
}

/**
 * 删除负载字段
 * @param {number} attrId 字段ID
 * @returns {Promise} 返回删除结果
 */
export function deleteLoadAttribute(attrId) {
  return axios({
    url: `/v0.1/load/attributes/${attrId}`,
    method: 'DELETE'
  })
}
