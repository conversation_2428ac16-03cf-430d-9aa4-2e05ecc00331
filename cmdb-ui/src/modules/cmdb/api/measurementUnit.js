import { axios } from '@/utils/request'

export function getMeasurementUnit () {
  return axios({
    url: '/v0.1/measurement_unit',
    method: 'GET'
  })
}

export function addMeasurementUnit (payload) {
  return axios({
    url: `/v0.1/measurement_unit`,
    method: 'POST',
    data: payload
  })
}

export function updateMeasurementUnit (muid, payload) {
  return axios({
    url: `/v0.1/measurement_unit/${muid}`,
    method: 'PUT',
    data: payload
  })
}

export function deleteMeasurementUnit (muid) {
  return axios({
    url: `/v0.1/measurement_unit/${muid}`,
    method: 'DELETE'
  })
}
