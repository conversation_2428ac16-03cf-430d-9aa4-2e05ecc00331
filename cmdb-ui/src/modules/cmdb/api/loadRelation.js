import { axios } from '@/utils/request'

/**
 * 获取 ci_type 的属性
 * @param CITypeId
 * @param parameter
 * @returns {AxiosPromise}
 */
export function getLoadAttrByCITypeId(CITypeId) {
  return axios({
    url: `/v0.1/load/relations/${CITypeId}`,
    method: 'get',
  })
}

/**
 * 获取CI类型关联的负载字段列表
 * @param {number} typeId CI类型ID
 * @returns {Promise} 返回关联的字段列表
 */
export function getTypeLoadAttrs(typeId) {
  return axios({
    url: `/v0.1/load/relations/${typeId}`,
    method: 'GET'
  })
}

/**
 * 批量添加或更新CI类型的负载字段关联
 * @param {number} typeId CI类型ID
 * @param {Array} attrConfigs 字段配置列表
 * @param {number} attrConfigs[].attr_id 字段ID
 * @param {number} attrConfigs[].order 排序值
 * @returns {Promise} 返回更新后的字段列表
 */
export function addTypeLoadAttrs(typeId, attrConfigs) {
  return axios({
    url: '/v0.1/load/relations',
    method: 'POST',
    data: {
      type_id: typeId,
      attr_configs: attrConfigs
    }
  })
}

/**
 * 删除CI类型的负载字段关联
 * @param {number} typeId CI类型ID
 * @param {number} attrId 字段ID
 * @returns {Promise} 返回删除结果
 */
export function deleteTypeLoadAttr(typeId, attrId) {
  return axios({
    url: `/v0.1/load/relations/${typeId}`,
    method: 'DELETE',
    params: {
      attr_id: attrId
    }
  })
}

/**
 * 删除CI类型的所有字段关联
 * @param {number} typeId CI类型ID
 * @returns {Promise} 返回删除结果
 */
export function deleteAllTypeLoadAttrs(typeId) {
  return axios({
    url: `/v0.1/load/relations/${typeId}`,
    method: 'DELETE'
  })
}
