import { axios } from '@/utils/request'

// 导入负载数据
export function importLoadData(typeId, uploadData, fileName, timeSlot = null) {
  const data = {
    upload_data: uploadData,
    file_name: fileName
  }

  // 只在有时间段参数时才添加
  if (timeSlot) {
    data.time_slot = timeSlot
  }

  return axios({
    url: `/v0.1/load/data/${typeId}`,
    method: 'post',
    data,
    timeout: 60 * 1000
  })
}

// 查询负载数据
export function queryLoadData(params) {
  return axios({
    url: '/v0.1/load/data',
    method: 'get',
    params
  })
}

// 获取时间段信息
export function getTimeSlotInfo() {
  return axios({
    url: '/v0.1/load/time_slots',
    method: 'get'
  })
}
