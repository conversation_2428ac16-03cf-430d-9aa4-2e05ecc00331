import { axios } from '@/utils/request'

export function getCIHistoryTable(params) {
  return axios({
    url: `/v0.1/cloud_history/records/attribute`,
    method: 'GET',
    params: params,
    timeout: 45000
  })
}

export function getUsers(params) {
  return axios({
    url: `/v1/acl/users/employee`,
    method: 'GET',
    params: params
  })
}

export function getCloudHistory(params) {
  return axios({
    url: `/v0.1/cloud_history/bussiness_log`,
    method: 'GET',
    params: params,
    timeout: 45000
  })
}

export function updateCloudHistoryById(id, params) {
  return axios({
    url: `/v0.1/cloud_history/bussiness_log/${id}`,
    method: 'PUT',
    params: params
  })
}
