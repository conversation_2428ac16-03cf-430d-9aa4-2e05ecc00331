import { axios } from '@/utils/request'

export function searchCIRelationPath(data) {
  return axios({
    url: `/v0.1/cloud_history/billing`,
    method: 'POST',
    data,
  })
}

export function clearCache(data) {
  return axios({
    url: `/v0.1/cloud_history/billing`,
    method: 'DELETE',
    data,
  })
}

export function checkBillingTask(taskId) {
  return axios({
    url: `/v0.1/cloud_history/billing/${taskId}`,
    method: 'GET'
  })
}
