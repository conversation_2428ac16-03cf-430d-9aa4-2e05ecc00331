import { axios } from '@/utils/request'

export function getCITypeChildren(CITypeID, parameter) {
  return axios({
    url: '/v0.1/ci_type_cost/' + CITypeID + '/children',
    method: 'get',
    params: parameter
  })
}

export function getCITypeParent(CITypeID) {
  return axios({
    url: '/v0.1/ci_type_cost/' + CITypeID + '/parents',
    method: 'get'
  })
}

export function getCost(CITypeID) {
  return axios({
    url: `/v0.1/ci_type_cost/${CITypeID}`,
    method: 'GET'
  })
}

export function createCost(CITypeID, data) {
  return axios({
    url: `/v0.1/ci_type_cost/${CITypeID}`,
    method: 'post',
    data
  })
}

export function deleteCost(CostID) {
  return axios({
    url: `/v0.1/ci_type_cost/del/${CostID}`,
    method: 'delete'
  })
}

export function grantTypeRelation(first_type_id, second_type_id, rid, data) {
  return axios({
    url: `/v0.1/ci_type_cost/${first_type_id}/${second_type_id}/roles/${rid}/grant`,
    method: 'post',
    data
  })
}

export function revokeTypeRelation(first_type_id, second_type_id, rid, data) {
  return axios({
    url: `/v0.1/ci_type_cost/${first_type_id}/${second_type_id}/roles/${rid}/revoke`,
    method: 'post',
    data
  })
}

export function getRecursive_level2children(type_id) {
  return axios({
    url: `/v0.1/ci_type_cost/${type_id}/recursive_level2children`,
    method: 'GET'
  })
}

export function getCanEditByParentIdChildId(parent_id, child_id) {
  return axios({
    url: `/v0.1/ci_type_cost/${parent_id}/${child_id}/can_edit`,
    method: 'GET'
  })
}
