import XLSX from 'xlsx'
import { axios } from '@/utils/request'

export function processFile(fileObj, onProgress) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = function (e) {
      try {
        onProgress(0.5) // 文件读取完成，进度设为50%
        const data = e.target.result
        const workbook = XLSX.read(data, { type: 'array' })
        onProgress(0.7) // XLSX读取完成，进度设为70%
        const sheet = workbook.Sheets[workbook.SheetNames[0]]
        const lt = XLSX.utils.sheet_to_json(sheet, { header: 1 })
        onProgress(1) // 处理完成，进度设为100%
        resolve(lt)
      } catch (error) {
        reject(new Error(`文件解析失败: ${error.message}`))
      }
    }
    reader.onerror = function () {
      reject(new Error('文件读取失败'))
    }
    reader.onprogress = function (event) {
      if (event.lengthComputable && onProgress) {
        // 文件读取阶段，进度最多到50%
        onProgress(event.loaded / event.total * 0.5)
      }
    }
    reader.readAsArrayBuffer(fileObj)
  })
}

export function uploadData(ciId, data) {
  return axios({
    url: '/v0.1/ci',
    method: 'POST',
    data: {
      ...data,
      ci_type: ciId,
      exist_policy: 'replace'
    },
    isShowMessage: false
  })
}

export function writeCsv(columns) {
  const { Parser } = require('json2csv')
  const opts = { columns }
  const p = new Parser(opts)
  return p.parse([])
}

export function writeExcel(columns, name) {
  const worksheet = XLSX.utils.aoa_to_sheet([columns])
  const newWorkBoot = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(newWorkBoot, worksheet, name)
  const s = XLSX.write(newWorkBoot, { type: 'array' })
  console.log(s)
  return s
}

// 判断一个数组元素是否都为空的
export function any(ArrayList) {
  let flag = false
  for (let i = 0; i < ArrayList.length; i++) {
    if (ArrayList[i]) {
      flag = true
      return flag
    }
  }
  return false
}

// 去除一个二维数组 底下为空的部分
export function filterNull(twoDimArray) {
  console.log(twoDimArray)
  const newArray = []
  for (let i = 0; i < twoDimArray.length; i++) {
    if (any(twoDimArray[i])) {
      newArray.push(twoDimArray[i])
    }
  }
  return newArray
}
