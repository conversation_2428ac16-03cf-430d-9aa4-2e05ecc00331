<template>
  <div ref="seeksRelationGraph" :style="{width: '100%',height : '100%'}" style="box-sizing:border-box;" @resize="refreshNVAnalysisInfo()">
<!--    <svg aria-hidden="true" style="position: absolute; width: 0px; height: 0px; overflow: hidden;"><symbol id="icon-iconset0444" viewBox="0 0 1024 1024"><path d="M960 682.666667l-106.666667 0C844.8 569.6 763.733333 42.666667 64 42.666667 51.2 42.666667 42.666667 51.2 42.666667 64c0 12.8 8.533333 21.333333 21.333333 21.333333 219.733333 0 377.6 106.666667 465.066667 315.733333 51.2 123.733333 64 236.8 66.133333 281.6L490.666667 682.666667c-12.8 0-21.333333 8.533333-21.333333 21.333333 0 6.4 2.133333 10.666667 6.4 14.933333l234.666667 256c4.266667 4.266667 8.533333 6.4 14.933333 6.4 6.4 0 10.666667-2.133333 14.933333-6.4l234.666667-256c4.266667-4.266667 6.4-8.533333 6.4-14.933333C981.333333 691.2 972.8 682.666667 960 682.666667zM725.333333 928 539.733333 725.333333 618.666667 725.333333c12.8 0 21.333333-8.533333 21.333333-21.333333l0-21.333333c-4.266667-87.466667-38.4-396.8-273.066667-554.666667C755.2 251.733333 804.266667 588.8 810.666667 682.666667l0 21.333333c0 12.8 8.533333 21.333333 21.333333 21.333333l78.933333 0L725.333333 928z"></path></symbol><symbol id="icon-iconset0445" viewBox="0 0 1024 1024"><path d="M974.933333 305.066667l-234.666667-256C736 44.8 731.733333 42.666667 725.333333 42.666667c-6.4 0-10.666667 2.133333-14.933333 6.4l-234.666667 256C471.466667 309.333333 469.333333 313.6 469.333333 320c0 12.8 8.533333 21.333333 21.333333 21.333333l104.533333 0c-2.133333 44.8-14.933333 157.866667-66.133333 281.6C441.6 832 283.733333 938.666667 64 938.666667c-12.8 0-21.333333 8.533333-21.333333 21.333333 0 12.8 8.533333 21.333333 21.333333 21.333333 699.733333 0 780.8-526.933333 789.333333-640l106.666667 0c12.8 0 21.333333-8.533333 21.333333-21.333333C981.333333 313.6 979.2 309.333333 974.933333 305.066667zM832 298.666667c-12.8 0-21.333333 8.533333-21.333333 21.333333l0 21.333333c-6.4 93.866667-55.466667 430.933333-443.733333 554.666667 234.666667-157.866667 268.8-467.2 273.066667-554.666667l0-21.333333c0-12.8-8.533333-21.333333-21.333333-21.333333l-78.933333 0L725.333333 96l185.6 202.666667L832 298.666667z"></path></symbol><symbol id="icon-yuanquanfenxiang" viewBox="0 0 1024 1024"><path d="M751.148705 578.773799c-61.987796 0-117.066229 30.121018-151.388942 76.488045L398.287145 536.382317c1.991354-10.171664 3.046383-20.673855 3.046383-31.419594 0-18.776645-3.196809-36.817534-9.058306-53.621245l193.486736-107.492176c31.949666 58.566884 94.104261 98.391914 165.386748 98.391914 103.803157 0 188.254571-84.450391 188.254571-188.254571S854.951863 65.73105 751.148705 65.73105c-103.804181 0-188.255595 84.450391-188.255595 188.254571 0 14.246469 1.600451 28.125571 4.613064 41.4746L368.032074 406.279167c-29.841655-39.193651-76.976162-64.544015-129.927141-64.544015-90.004897 0-163.228595 73.223698-163.228595 163.228595 0 90.003873 73.223698 163.228595 163.228595 163.228595 60.882626 0 114.078175-33.510208 142.134161-83.049484L575.173808 700.16452c-7.925507 20.788465-12.280698 43.325761-12.280698 66.86385 0 103.803157 84.450391 188.254571 188.255595 188.254571 103.803157 0 188.254571-84.450391 188.254571-188.254571S854.951863 578.773799 751.148705 578.773799zM751.148705 116.89636c75.590606 0 137.089261 61.498656 137.089261 137.089261s-61.498656 137.089261-137.089261 137.089261c-75.591629 0-137.090285-61.498656-137.090285-137.089261S675.557076 116.89636 751.148705 116.89636zM238.105956 617.026008c-61.791321 0-112.063285-50.27094-112.063285-112.063285 0-61.791321 50.27094-112.063285 112.063285-112.063285s112.063285 50.27094 112.063285 112.063285C350.168218 566.755068 299.897278 617.026008 238.105956 617.026008zM751.148705 904.117632c-75.591629 0-137.090285-61.498656-137.090285-137.089261s61.498656-137.089261 137.090285-137.089261c75.590606 0 137.089261 61.498656 137.089261 137.089261S826.739311 904.117632 751.148705 904.117632z"></path></symbol><symbol id="icon-hj2" viewBox="0 0 1024 1024"><path d="M963.66 256.607c0-0.244 0-0.607-0.126-0.851 0-0.369 0-0.614-0.119-0.978 0-0.369-0.119-0.733-0.119-1.104 0-0.244 0-0.487-0.126-0.725 0-0.369-0.119-0.859-0.244-1.222 0-0.244-0.119-0.369-0.119-0.613-0.126-0.365-0.126-0.852-0.245-1.216 0-0.244-0.126-0.369-0.126-0.613-0.119-0.365-0.244-0.734-0.364-1.222-0.126-0.244-0.126-0.49-0.244-0.609l-0.369-1.095c-0.119-0.245-0.119-0.49-0.244-0.734-0.121-0.369-0.245-0.613-0.365-0.978-0.126-0.244-0.244-0.613-0.369-0.851-0.119-0.244-0.238-0.613-0.364-0.859-0.119-0.364-0.244-0.607-0.364-0.977l-0.371-0.734c-0.119-0.364-0.364-0.725-0.487-0.971-0.119-0.245-0.244-0.371-0.364-0.614-0.244-0.364-0.369-0.733-0.614-1.097-0.119-0.126-0.244-0.369-0.364-0.487l-0.733-1.097c-0.119-0.126-0.244-0.369-0.365-0.487-0.244-0.369-0.487-0.733-0.733-0.978-0.126-0.244-0.244-0.369-0.487-0.613-0.245-0.238-0.49-0.607-0.734-0.851-0.244-0.245-0.487-0.49-0.607-0.734-0.244-0.244-0.369-0.487-0.614-0.733-0.851-0.972-1.829-1.83-2.681-2.682-0.245-0.244-0.49-0.369-0.734-0.614-0.244-0.244-0.487-0.487-0.733-0.607-0.245-0.244-0.614-0.487-0.859-0.733-0.238-0.121-0.364-0.365-0.607-0.49-0.365-0.244-0.734-0.487-0.978-0.733-0.119-0.119-0.364-0.244-0.487-0.365l-1.097-0.733c-0.119-0.119-0.364-0.244-0.487-0.364-0.365-0.244-0.734-0.369-1.097-0.614-0.244-0.119-0.369-0.244-0.613-0.364-0.365-0.126-0.734-0.369-0.972-0.487l-0.734-0.369c-0.369-0.121-0.613-0.245-0.977-0.365-0.244-0.126-0.614-0.244-0.859-0.369-0.244-0.119-0.607-0.238-0.851-0.364-0.365-0.119-0.609-0.244-0.978-0.364-0.244-0.126-0.487-0.126-0.733-0.245l-1.097-0.369c-0.244-0.119-0.487-0.119-0.609-0.244-0.369-0.119-0.733-0.244-1.22-0.364-0.245 0-0.371-0.126-0.614-0.126-0.364-0.119-0.851-0.244-1.216-0.244-0.244 0-0.487-0.121-0.613-0.121-0.364-0.126-0.852-0.126-1.222-0.244-0.244 0-0.482-0.126-0.725-0.126-0.369 0-0.734-0.119-1.104-0.119-0.364 0-0.607 0-0.978-0.126-0.244 0-0.607 0-0.851-0.119h-242.407c-21.604 0-39.062 17.456-39.062 39.062 0 21.604 17.458 39.055 39.062 39.055h146.102l-3.171 3.177-217.27 217.265 0.369 0.369-49.676 49.676-155.87-155.87c-7.694-7.689-17.827-11.472-27.953-11.472-10.13-0.126-20.263 3.659-27.952 11.472l-282.446 282.322c-15.258 15.258-15.258 40.041 0 55.298 15.258 15.258 40.033 15.258 55.291 0l255.108-255.11 155.746 155.751c7.813 7.815 17.945 11.593 28.076 11.474 10.126 0.119 20.382-3.659 28.070-11.474l182.964-182.969c0.734-0.734 1.347-1.342 1.956-2.075l111.564-111.564v146.109c0 21.604 17.451 39.060 39.055 39.060s39.062-17.456 39.062-39.060v-235.085c0-0.244 0.126-0.49 0.126-0.614 0-0.244 0.119-0.487 0.119-0.733 0-0.364 0.119-0.734 0.119-1.097s0-0.607 0.126-0.977c0-0.245 0-0.609 0.119-0.852-0.122-1.466-0.122-2.807-0.122-4.030v0zM963.66 256.607z"></path></symbol><symbol id="icon-circleyuanquan" viewBox="0 0 1024 1024"><path d="M150.1184 150.1184C250.2656 50.0736 370.8928 0 512 0c141.1072 0 261.7344 50.0736 361.8816 150.1184C973.9264 250.2656 1024 370.8928 1024 512c0 141.2096-50.0736 261.8368-150.1184 361.8816C773.7344 973.9264 653.1072 1024 512 1024c-141.1072 0-261.7344-50.0736-361.8816-150.1184C50.0736 773.8368 0 653.2096 0 512 0 370.8928 50.0736 250.2656 150.1184 150.1184zM512 41.8816c-85.2992 0-164.1472 20.8896-236.3392 62.7712C203.4688 146.432 146.432 203.4688 104.6528 275.6608 62.7712 347.9552 41.8816 426.7008 41.8816 512c0 129.6384 45.9776 240.4352 137.8304 332.288C271.5648 936.2432 382.3616 982.1184 512 982.1184c129.6384 0 240.4352-45.8752 332.288-137.8304C936.2432 752.4352 982.1184 641.6384 982.1184 512c0-129.6384-45.9776-240.3328-137.8304-332.288C752.4352 87.7568 641.6384 41.8816 512 41.8816zM512 256c-36.1472 0-70.144 7.0656-102.1952 20.8896C377.856 290.9184 349.4912 309.3504 324.9152 332.288c-22.9376 24.576-41.472 52.9408-55.3984 84.8896S248.6272 483.328 248.6272 519.3728c0 36.1472 6.9632 70.144 20.8896 102.1952s32.4608 60.3136 55.3984 84.8896C349.4912 729.4976 377.856 747.9296 409.8048 761.856 441.856 775.7824 475.8528 782.7456 512 782.7456c36.1472 0 70.144-6.9632 102.1952-20.8896C646.144 747.9296 674.5088 729.4976 699.0848 706.4576c22.9376-24.576 41.472-52.9408 55.3984-84.8896s20.8896-66.048 20.8896-102.1952c0-36.0448-6.9632-70.144-20.8896-102.1952S722.0224 356.9664 699.0848 332.288C674.5088 309.3504 646.144 290.9184 614.1952 276.8896 582.144 263.0656 548.1472 256 512 256z"></path></symbol><symbol id="icon-bhjlink" viewBox="0 0 1024 1024"><path d="M901.920618 465.785468c-50.993571 0-94.522533 32.698367-110.688942 78.273953L702.244627 544.059421c1.764013-10.590222 2.890616-21.423121 2.890616-32.694527 0-72.177856-40.480145-134.923579-99.898281-166.857822l34.099901-112.776268c8.829281 2.126492 18.1347 3.327588 27.724265 3.327588 64.833817 0 117.472367-52.597848 117.472367-117.589097C784.532727 52.594008 731.894177 0 667.06036 0c-64.911381 0-117.509229 52.594008-117.509229 117.470063 0 49.305587 30.248562 91.349307 73.177745 108.842756l-33.419485 110.692013c-22.548956-9.588797-47.462473-14.807112-73.540224-14.807112-49.18732 0-93.841349 18.658451-127.540374 49.309426L213.348053 196.583401l-1.886119 2.008226c19.940183-21.103648 32.256788-49.588197 32.256788-81.003297 0-64.872983-52.517212-117.470063-117.470063-117.470063-64.913685 0-117.511533 52.59708-117.511533 117.470063 0 65.034255 52.59708 117.592169 117.511533 117.592169 31.412796 0 59.898881-12.358075 80.921125-32.335888l-6.179038 6.056931 174.960609 174.879973c-30.729308 33.66293-49.506025 78.47516-49.506025 127.661712 0 43.008283 14.401627 82.685138 38.593258 114.501114l-149.845117 120.999625c-21.503757-28.044506-55.285722-46.218372-93.357532-46.218372-64.874519 0-117.430897 52.719186-117.430897 117.592169 0 64.833049 52.556378 117.470063 117.430897 117.470063 64.872983 0 117.549163-52.637014 117.549163-117.470063 0-20.541499-5.335813-39.878062-14.602834-56.68956l151.369526-122.203792c34.584487 37.633303 84.29095 61.384123 139.618142 61.384123 28.082136 0 54.602234-6.057699 78.553492-17.091805l52.716114 119.514383c-35.585143 20.222026-59.497236 58.335306-59.497236 102.106945 0 64.912149 52.637014 117.549163 117.592169 117.549163 64.872983 0 117.388659-52.637014 117.388659-117.549163 0-64.833049-52.516444-117.513069-117.388659-117.513069-15.047485 0-29.248673 2.929782-42.446133 7.903885l-52.798286-120.034294c42.807844-24.554878 75.182898-65.518073 88.46253-114.221576l88.222926 0c-1.405374 7.018422-1.96522 14.241123-1.96522 21.743362 0 64.872983 52.557914 117.509997 117.387123 117.509997 64.995089 0 117.596009-52.637014 117.596009-117.509997C1019.508947 518.383316 966.994039 465.785468 901.920618 465.785468L901.920618 465.785468zM901.920618 465.785468"></path></symbol><symbol id="icon-lianjie_connecting5" viewBox="0 0 1024 1024"><path d="M801.25952 251.74016l-28.9792-28.99968-150.79424 150.8352-35.1232-35.10272-99.14368 99.16416 99.14368 99.16416 99.16416-99.16416-35.10272-35.1232zM487.19872 476.8768l-29.94176 29.94176-19.61984-19.61984-99.16416 99.16416 35.10272 35.1232-150.8352 150.79424 28.95872 28.95872 150.8352-150.79424 35.10272 35.10272 99.14368-99.14368-19.61984-19.64032 29.96224-29.96224-59.92448-59.92448z m-15.48288 44.4416l15.4624-15.4624 30.98624 30.98624-15.4624 15.44192-30.98624-30.96576z" fill=""></path></symbol><symbol id="icon-xiazai" viewBox="0 0 1024 1024"><path d="M840.5 900.3h-657c-16.5 0-29.9 13.4-29.9 29.9s13.4 29.9 29.9 29.9h657.1c16.5 0 29.9-13.4 29.9-29.9-0.1-16.6-13.5-29.9-30-29.9zM631.5 123.7V541.8H763.1L512 769.9l-251.2-228h131.7V123.7h239m0-59.7h-239c-33 0-59.7 26.7-59.7 59.7v358.4H183.5c-26.6 0-39.9 32.2-21.1 51l328.5 298.2c5.8 5.8 13.5 8.7 21.1 8.7s15.3-2.9 21.1-8.7l328.5-298.2c18.8-18.8 5.5-51-21.1-51H691.2V123.7c0-33-26.7-59.7-59.7-59.7z"></path></symbol><symbol id="icon-ico_reset" viewBox="0 0 1024 1024"><path d="M925.97087933 277.33133334a479.997 479.997 0 1 0 54.33566 255.9984H916.05094133a415.9974 415.9974 0 1 1-64.191599-255.9984h74.047537z"></path><path d="M978.64255033 61.01268534L725.33213333 371.09074734h297.59814z"></path></symbol><symbol id="icon-fangda" viewBox="0 0 1024 1024"><path d="M768 448a320 320 0 1 0-320 320 320 320 0 0 0 320-320z m64 0A384 384 0 1 1 448 64a384 384 0 0 1 384 384z"></path><path d="M681.28 726.72a32 32 0 0 1 45.44-45.44l160 160a32 32 0 0 1-45.44 45.44zM320 480a32 32 0 0 1 0-64h256a32 32 0 0 1 0 64z"></path><path d="M480 576a32 32 0 0 1-64 0V320a32 32 0 0 1 64 0z"></path></symbol><symbol id="icon-zidong" viewBox="0 0 1024 1024"><path d="M131.3 363.3c-19.2 47.3-28.9 97.3-28.9 148.7 0 51.3 9.7 101.3 28.9 148.7 7 17.2 15.1 33.8 24.3 49.7l15.8-49.1 42.8 13.8-39.8 123.8-42.8-13.9-80.9-26 13.8-42.8 52.3 16.8c-98-168.4-74.8-387.9 69.4-532.1C342.4 44.7 586.9 30.5 759.1 158.3l-32.2 32.2c-25.1-18-52.1-32.9-80.9-44.6-47.3-19.2-97.3-28.9-148.7-28.9-51.3 0-101.3 9.7-148.7 28.9-49 19.9-92.9 49.1-130.6 86.8s-66.9 81.7-86.7 130.6z m828.3-60.2l13.8-42.8-80.9-26-42.8-13.8-39.8 123.8 42.8 13.8 15-46.7c9.8 16.6 18.4 34 25.8 52.1 19.2 47.3 28.9 97.3 28.9 148.7 0 51.3-9.7 101.3-28.9 148.7-19.9 49-49.1 92.9-86.8 130.6C768.9 829 725 858.2 676 878.1c-47.3 19.2-97.3 28.9-148.7 28.9-51.3 0-101.3-9.7-148.7-28.9-28.8-11.7-55.9-26.6-80.9-44.6l-32.2 32.2c126.3 93.7 382.6 147.7 572.9-42.6 145.8-145.8 167.9-368.6 66.1-537.7l55.1 17.7zM474 241.2L295 730.4h64.8l50.5-148.2h195.3l54 148.2h70.3L539.6 241.2H474z m-45.9 288.3L479.8 386c11-30.9 19.4-62.1 25.4-93.4 6.9 26 17.9 59.9 32.8 101.5l48.2 135.5H428.1z" fill=""></path></symbol><symbol id="icon-quanping" viewBox="0 0 1024 1024"><path d="M795.5 192H581c-19.6 0-35.6 15.7-36 35.3-0.4 20.3 16.4 36.7 36.7 36.7h128.4L581 393.1c-14 14-14 36.9 0 50.9s36.9 14 50.9 0L760 315.9v129c0 19.6 15.8 35.6 35.3 36 20.2 0.4 36.7-16.4 36.7-36.7V228.5c0-20.1-16.3-36.5-36.5-36.5zM442.2 760H313.8L443 630.9c14-14 14-36.9 0-50.9s-36.9-14-50.9 0L264 708.1V579c0-19.6-15.8-35.6-35.3-36-20.2-0.4-36.7 16.4-36.7 36.7v215.6c0 20.3 16.4 36.7 36.7 36.7H443c19.6 0 35.6-15.7 36-35.3 0.3-20.3-16.5-36.7-36.8-36.7z"></path><path d="M838 136c27.6 0 50 22.4 50 50v652c0 27.6-22.4 50-50 50H186c-27.6 0-50-22.4-50-50V186c0-27.6 22.4-50 50-50h652m0-72H186c-16.4 0-32.4 3.2-47.5 9.6-14.5 6.1-27.6 14.9-38.8 26.1-11.2 11.2-20 24.2-26.1 38.8-6.4 15.1-9.6 31.1-9.6 47.5v652c0 16.4 3.2 32.4 9.6 47.5 6.1 14.5 14.9 27.6 26.1 38.8 11.2 11.2 24.2 20 38.8 26.1 15.1 6.4 31.1 9.6 47.5 9.6h652c16.4 0 32.4-3.2 47.5-9.6 14.5-6.1 27.6-14.9 38.8-26.1 11.2-11.2 20-24.2 26.1-38.8 6.4-15.1 9.6-31.1 9.6-47.5V186c0-16.4-3.2-32.4-9.6-47.5-6.1-14.5-14.9-27.6-26.1-38.8-11.2-11.2-24.2-20-38.8-26.1-15.1-6.4-31.1-9.6-47.5-9.6z"></path></symbol><symbol id="icon-lianjieliu" viewBox="0 0 1024 1024"><path d="M280.224 425.856h348.608a29.536 29.536 0 1 0 0-59.072H280.224c-52.448 0-93.152-34.304-93.152-73.856s40.704-73.856 93.152-73.856h140.128C432.096 258.816 468.448 288 512 288s79.904-29.184 91.648-68.928h178.08a29.536 29.536 0 1 0 0-59.072h-179.584C588.896 122.784 553.728 96 512 96s-76.896 26.784-90.112 64H280.224C197.184 160 128 218.272 128 292.928s69.184 132.928 152.224 132.928z"></path><path d="M895.936 415.2A96 96 0 1 0 800 512c30.656 0 57.632-14.624 75.2-36.992 10.56 12.064 16.832 26.56 16.832 41.92 0 39.552-40.704 73.856-93.152 73.856H306.016A95.584 95.584 0 0 0 224 544a95.68 95.68 0 0 0-95.232 88.352C89.888 656.224 64 695.424 64 740.928c0 74.656 69.184 132.928 152.224 132.928h241.728A95.808 95.808 0 0 0 544 928a96 96 0 1 0 0-192 95.904 95.904 0 0 0-94.272 78.752H216.224c-52.448 0-93.152-34.304-93.152-73.856 0-17.504 8.32-33.792 21.76-46.72A95.808 95.808 0 0 0 224 736a95.68 95.68 0 0 0 95.008-86.144h479.84c83.072 0 152.224-58.272 152.224-132.928 0.032-41.536-21.824-77.568-55.136-101.728z"></path></symbol><symbol id="icon-shuaxin" viewBox="0 0 1024 1024"><path d="M512 919.552c-224.768 0-407.552-182.784-407.552-407.552 0-8.704 0.512-17.408 1.024-26.112l71.68 4.608c-0.512 7.168-0.512 14.336-0.512 21.504 0 185.344 150.528 335.872 335.872 335.872 86.528 0 168.448-32.768 230.912-92.16l49.152 52.224C716.288 880.128 616.96 919.552 512 919.552zM919.552 512h-71.68c0-11.776-0.512-23.552-2.048-35.328-17.92-171.52-161.28-300.544-334.336-300.544-67.584 0-132.096 19.968-187.904 57.344L284.16 174.08c67.072-45.568 145.92-69.632 227.84-69.632 209.408 0 384 156.672 405.504 365.056 1.536 13.824 2.048 28.16 2.048 42.496z" fill="#707070"></path><path d="M140.288 290.816L28.16 491.52c-3.072 5.12 1.024 11.776 6.656 11.776H258.56c6.144 0 9.728-6.144 6.656-11.776L153.6 290.816c-3.072-5.632-10.752-5.632-13.312 0zM870.4 675.84L758.272 475.136c-3.072-5.12 1.024-11.776 6.656-11.776h223.744c6.144 0 9.728 6.144 6.656 11.776L883.712 675.84c-2.56 5.12-10.24 5.12-13.312 0zM270.336 202.24a35.84 35.84 0 1 0 71.68 0 35.84 35.84 0 1 0-71.68 0zM728.576 784.896a35.84 35.84 0 1 0 71.68 0 35.84 35.84 0 1 0-71.68 0z" fill="#707070"></path></symbol><symbol id="icon-resize-" viewBox="0 0 1024 1024"><path d="M410.816 673.514667L230.997333 853.333333H384v85.333334H85.333333V640h85.333334v153.002667l179.818666-179.84 60.330667 60.373333z m-53.632-256L170.666667 230.997333V384H85.333333V85.333333h298.666667v85.333334h-153.002667l186.517334 186.517333-60.330667 60.330667z m234.666667-45.696L793.002667 170.666667H640V85.333333h298.666667v298.666667h-85.333334v-153.002667l-201.152 201.173334-60.330666-60.373334z m67.029333 226.709333L853.333333 793.002667V640h85.333334v298.666667H640v-85.333334h153.002667l-194.474667-194.453333 60.352-60.352z"></path></symbol><symbol id="icon-guanxitu" viewBox="0 0 1024 1024"><path d="M209.003789 401.084632l29.749895-44.948211 107.843369 71.464421-29.749895 44.894316zM682.469053 546.600421l-14.551579-51.846737 124.496842-35.031579 14.551579 51.846737zM315.068632 812.840421l-40.367158-35.678316 85.692631-96.902737 40.421053 35.732211zM597.962105 390.251789l-46.672842-26.947368 43.11579-74.64421 46.672842 26.947368zM615.639579 728.764632l41.121684-34.816 83.536842 98.735157-41.121684 34.816z" fill="#444A5C"></path><path d="M501.221053 765.305263c-118.568421 0-215.578947-97.010526-215.578948-215.578947s97.010526-215.578947 215.578948-215.578948 215.578947 97.010526 215.578947 215.578948-97.010526 215.578947-215.578947 215.578947z m0-377.263158c-91.621053 0-161.684211 70.063158-161.684211 161.684211s70.063158 161.684211 161.684211 161.68421 161.684211-70.063158 161.68421-161.68421-75.452632-161.684211-161.68421-161.684211zM167.073684 452.715789c-59.284211 0-107.789474-48.505263-107.789473-107.789473s48.505263-107.789474 107.789473-107.789474 107.789474 48.505263 107.789474 107.789474-48.505263 107.789474-107.789474 107.789473z m0-161.68421c-32.336842 0-53.894737 21.557895-53.894737 53.894737s21.557895 53.894737 53.894737 53.894737 53.894737-21.557895 53.894737-53.894737-26.947368-53.894737-53.894737-53.894737zM253.305263 948.547368c-59.284211 0-107.789474-48.505263-107.789474-107.789473s48.505263-107.789474 107.789474-107.789474 107.789474 48.505263 107.789474 107.789474-48.505263 107.789474-107.789474 107.789473z m0-161.68421c-32.336842 0-53.894737 21.557895-53.894737 53.894737s21.557895 53.894737 53.894737 53.894737 53.894737-21.557895 53.894737-53.894737-21.557895-53.894737-53.894737-53.894737zM856.926316 576.673684c-59.284211 0-107.789474-48.505263-107.789474-107.789473s48.505263-107.789474 107.789474-107.789474 107.789474 48.505263 107.789473 107.789474-48.505263 107.789474-107.789473 107.789473z m0-161.68421c-32.336842 0-53.894737 21.557895-53.894737 53.894737s21.557895 53.894737 53.894737 53.894736 53.894737-21.557895 53.894737-53.894736-21.557895-53.894737-53.894737-53.894737zM662.905263 350.315789C592.842105 350.315789 528.168421 291.031579 528.168421 215.578947s59.284211-134.736842 134.736842-134.736842 134.736842 59.284211 134.736842 134.736842-59.284211 134.736842-134.736842 134.736842z m0-215.578947c-43.115789 0-80.842105 37.726316-80.842105 80.842105s37.726316 80.842105 80.842105 80.842106 80.842105-37.726316 80.842105-80.842106-32.336842-80.842105-80.842105-80.842105zM749.136842 921.6c-43.115789 0-80.842105-37.726316-80.842105-80.842105s37.726316-80.842105 80.842105-80.842106 80.842105 37.726316 80.842105 80.842106-37.726316 80.842105-80.842105 80.842105z m0-107.789474c-16.168421 0-26.947368 10.778947-26.947368 26.947369s10.778947 26.947368 26.947368 26.947368 26.947368-10.778947 26.947369-26.947368-10.778947-26.947368-26.947369-26.947369z" fill="#444A5C"></path></symbol><symbol id="icon-tupian" viewBox="0 0 1024 1024"><path d="M938.666667 553.92V768c0 64.8-52.533333 117.333333-117.333334 117.333333H202.666667c-64.8 0-117.333333-52.533333-117.333334-117.333333V256c0-64.8 52.533333-117.333333 117.333334-117.333333h618.666666c64.8 0 117.333333 52.533333 117.333334 117.333333v297.92z m-64-74.624V256a53.333333 53.333333 0 0 0-53.333334-53.333333H202.666667a53.333333 53.333333 0 0 0-53.333334 53.333333v344.48A290.090667 290.090667 0 0 1 192 597.333333a286.88 286.88 0 0 1 183.296 65.845334C427.029333 528.384 556.906667 437.333333 704 437.333333c65.706667 0 126.997333 16.778667 170.666667 41.962667z m0 82.24c-5.333333-8.32-21.130667-21.653333-43.648-32.917333C796.768 511.488 753.045333 501.333333 704 501.333333c-121.770667 0-229.130667 76.266667-270.432 188.693334-2.730667 7.445333-7.402667 20.32-13.994667 38.581333-7.68 21.301333-34.453333 28.106667-51.370666 13.056-16.437333-14.634667-28.554667-25.066667-36.138667-31.146667A222.890667 222.890667 0 0 0 192 661.333333c-14.464 0-28.725333 1.365333-42.666667 4.053334V768a53.333333 53.333333 0 0 0 53.333334 53.333333h618.666666a53.333333 53.333333 0 0 0 53.333334-53.333333V561.525333zM320 480a96 96 0 1 1 0-192 96 96 0 0 1 0 192z m0-64a32 32 0 1 0 0-64 32 32 0 0 0 0 64z"></path></symbol><symbol id="icon-juhejiedian" viewBox="0 0 1024 1024"><path d="M533.333333 725.333333a64 64 0 1 1-64 64 64 64 0 0 1 64-64m0-85.333333a149.333333 149.333333 0 1 0 149.333334 149.333333 149.333333 149.333333 0 0 0-149.333334-149.333333z" fill="#666666"></path><path d="M533.333333 277.333333m-106.666666 0a106.666667 106.666667 0 1 0 213.333333 0 106.666667 106.666667 0 1 0-213.333333 0Z" fill="#666666"></path><path d="M277.333333 320m-106.666666 0a106.666667 106.666667 0 1 0 213.333333 0 106.666667 106.666667 0 1 0-213.333333 0Z" fill="#666666"></path><path d="M789.333333 320m-106.666666 0a106.666667 106.666667 0 1 0 213.333333 0 106.666667 106.666667 0 1 0-213.333333 0Z" fill="#666666"></path><path d="M512 341.333333h42.666667v384h-42.666667z" fill="#666666"></path><path d="M741.290667 357.504l30.165333 30.165333-225.066667 225.066667-30.165333-30.165333z" fill="#666666"></path><path d="M558.08 582.016l-30.165333 30.165333-228.693334-228.693333 30.165334-30.165333z" fill="#666666"></path></symbol><symbol id="icon-ziyuan" viewBox="0 0 1024 1024"><path d="M236.615854 752.86913h-0.484162A241.500139 241.500139 0 0 1 0.538332 527.733676a238.78883 238.78883 0 0 1 64.684079-180.011531 242.081134 242.081134 0 0 1 149.025146-74.367324 298.534454 298.534454 0 0 1 596.003751 25.951097V300.95207a221.262156 221.262156 0 0 1 213.031398 233.075715 222.714643 222.714643 0 0 1-221.262156 209.1581h-3.098639a27.500417 27.500417 0 0 1 0-54.904001h2.711309A167.520145 167.520145 0 0 0 968.378705 531.219644a166.261323 166.261323 0 0 0-166.16449-175.363573 151.058627 151.058627 0 0 0-18.204502 1.065157 27.500417 27.500417 0 0 1-30.211725-30.308558 240.822312 240.822312 0 0 0 1.549319-27.306752 243.727285 243.727285 0 0 0-487.357738 0 27.500417 27.500417 0 0 1-27.403584 27.500417 185.627813 185.627813 0 0 0-185.240484 197.538205A186.30564 186.30564 0 0 0 237.100016 697.965129a27.500417 27.500417 0 0 1-0.484162 54.904001z"></path><path d="M513.266174 1021.869686a27.500417 27.500417 0 0 1-27.500417-27.403584v-484.162268a27.500417 27.500417 0 1 1 54.904001 0v484.162268a27.403584 27.403584 0 0 1-27.403584 27.403584z"></path><path d="M513.266174 1024a27.500417 27.500417 0 0 1-19.366491-8.037094L342.260061 864.129619a27.500417 27.500417 0 1 1 38.732981-38.732981L513.266174 957.766602l130.530147-130.336483a27.500417 27.500417 0 0 1 38.732981 38.732982L532.632664 1015.962906a27.403584 27.403584 0 0 1-19.36649 8.037094z"></path></symbol><symbol id="icon-icon_shuaxin" viewBox="0 0 1024 1024"><path d="M512 938.666667c-55.371852 0-109.226667-10.903704-159.857778-32.237037-48.924444-20.66963-92.823704-50.251852-130.465185-87.988149-37.736296-37.736296-67.318519-81.635556-87.988148-130.465185-21.428148-50.631111-32.237037-104.391111-32.237037-159.857777s10.903704-109.226667 32.237037-159.857778c20.66963-48.924444 50.251852-92.823704 87.988148-130.465185 37.736296-37.736296 81.635556-67.318519 130.465185-87.988149 50.631111-21.428148 104.391111-32.237037 159.857778-32.237037 63.81037 0 125.060741 14.222222 181.854815 42.382223 54.139259 26.832593 102.684444 66.085926 140.325926 113.682963 7.300741 9.197037 5.783704 22.660741-3.508148 29.961481-9.197037 7.300741-22.660741 5.783704-29.961482-3.508148-70.257778-88.936296-175.502222-139.946667-288.711111-139.946667-202.808889 0-367.881481 165.072593-367.881481 367.881482s165.072593 367.881481 367.881481 367.881481 367.881481-165.072593 367.881481-367.881481c0-11.757037 9.576296-21.333333 21.333334-21.333334S922.548148 516.361481 922.548148 528.118519c0 55.371852-10.903704 109.226667-32.237037 159.857777-20.66963 48.924444-50.251852 92.823704-87.988148 130.465185-37.736296 37.736296-81.635556 67.318519-130.465185 87.988149-50.631111 21.428148-104.485926 32.237037-159.857778 32.237037z"></path><path d="M817.398519 308.242963c-11.757037 0-21.333333-9.576296-21.333334-21.333333V106.666667c0-11.757037 9.576296-21.333333 21.333334-21.333334s21.333333 9.576296 21.333333 21.333334v180.242963c0 11.757037-9.481481 21.333333-21.333333 21.333333z"></path><path d="M817.398519 308.242963H637.155556c-11.757037 0-21.333333-9.576296-21.333334-21.333333s9.576296-21.333333 21.333334-21.333334h180.242963c11.757037 0 21.333333 9.576296 21.333333 21.333334s-9.481481 21.333333-21.333333 21.333333z"></path></symbol><symbol id="icon-lianjiezhong" viewBox="0 0 1024 1024"><path d="M883.396923 298.141538a99.721846 99.721846 0 0 1-142.414769 0 103.187692 103.187692 0 0 1 0-144.423384 99.721846 99.721846 0 0 1 142.414769 0 103.187692 103.187692 0 0 1 0 144.423384zM285.144615 760.438154a87.276308 87.276308 0 0 0-124.652307 0 90.269538 90.269538 0 0 0 0 126.385231c34.422154 34.894769 90.230154 34.894769 124.652307 0a90.269538 90.269538 0 0 0 0-126.424616z m482.579693 108.307692a64.472615 64.472615 0 0 1 0-90.269538 62.345846 62.345846 0 0 1 89.00923 0c24.576 24.930462 24.576 65.378462 0 90.269538a62.345846 62.345846 0 0 1-89.00923 0zM294.006154 298.141538a103.187692 103.187692 0 0 0 0-144.423384 99.721846 99.721846 0 0 0-142.454154 0 103.187692 103.187692 0 0 0 0 144.423384 99.721846 99.721846 0 0 0 142.454154 0z m324.214154-196.01723c0 56.438154-45.095385 102.163692-100.745846 102.163692-55.611077 0-100.706462-45.725538-100.706462-102.163692C416.768 45.725538 461.863385 0 517.474462 0c55.650462 0 100.745846 45.725538 100.745846 102.124308zM517.474462 870.793846c-41.747692 0-75.539692 34.264615-75.539693 76.603077 0 42.299077 33.831385 76.603077 75.539693 76.603077 41.747692 0 75.539692-34.264615 75.539692-76.603077 0-42.299077-33.792-76.603077-75.539692-76.603077z m416.768-294.990769c-27.805538 0-50.333538-22.843077-50.333539-51.042462 0-28.199385 22.528-51.042462 50.333539-51.042461 27.844923 0 50.412308 22.843077 50.372923 51.042461 0 28.199385-22.567385 51.081846-50.372923 51.081847zM201.452308 524.8c0-56.398769-45.095385-102.124308-100.745846-102.124308C45.095385 422.596923 0 468.283077 0 524.760615c0 56.398769 45.095385 102.124308 100.706462 102.124308 55.650462 0 100.745846-45.686154 100.745846-102.124308z"></path></symbol><symbol id="icon-loading" viewBox="0 0 1024 1024"><path d="M563.2 819.2a102.4 102.4 0 1 1 0 204.8 102.4 102.4 0 0 1 0-204.8z m-320.4608-153.6a128 128 0 1 1 0 256 128 128 0 0 1 0-256z m592.7936 25.6a102.4 102.4 0 1 1 0 204.8 102.4 102.4 0 0 1 0-204.8zM947.2 477.1328a76.8 76.8 0 1 1 0 153.6 76.8 76.8 0 0 1 0-153.6zM128 307.2a128 128 0 1 1 0 256 128 128 0 0 1 0-256z m782.6432-40.6016a51.2 51.2 0 1 1 0 102.4 51.2 51.2 0 0 1 0-102.4zM409.6 0a153.6 153.6 0 1 1 0 307.2 153.6 153.6 0 0 1 0-307.2z m384 153.6a25.6 25.6 0 1 1 0 51.2 25.6 25.6 0 0 1 0-51.2z" fill="#555555"></path></symbol><symbol id="icon-tupushujuyuan" viewBox="0 0 1024 1024"><path d="M851.456 588.288c-13.312 0-25.6 2.048-37.888 5.12l-90.112-155.648 55.296-31.744c18.944 17.92 44.032 29.184 72.192 29.184 57.344 0 103.936-46.592 103.936-103.936s-46.592-103.936-103.936-103.936c-57.344 0-103.936 46.592-103.936 103.936 0 5.12 0.512 10.24 1.536 15.36l-58.368 33.792-82.944-143.872c26.112-25.088 41.984-59.904 41.984-98.816 0-75.776-61.44-137.216-137.216-137.216s-137.216 61.44-137.216 137.216c0 38.912 15.872 73.728 41.984 98.816l-84.992 146.944-56.832-32.768c1.024-6.144 2.048-12.8 2.048-19.456 0-57.344-46.592-103.936-103.936-103.936-57.344 0-103.936 46.592-103.936 103.936s46.592 103.936 103.936 103.936c26.624 0 51.2-10.24 69.12-26.624l56.32 32.768-88.064 152.576c-11.776-3.584-24.576-5.12-37.888-5.12-75.776 0-137.216 61.44-137.216 137.216s61.44 137.216 137.216 137.216c64.512 0 118.272-44.544 133.12-103.936h173.056v61.44c-40.96 13.824-70.656 52.736-70.656 98.816 0 57.344 46.592 103.936 103.936 103.936s103.936-46.592 103.936-103.936c0-46.08-29.696-84.48-70.656-98.816v-61.44h173.056c14.848 59.904 69.12 103.936 133.12 103.936 75.776 0 137.216-61.44 137.216-137.216s-61.44-137.728-137.216-137.728z m-410.112-450.048c0-38.912 31.744-70.656 70.656-70.656s70.656 31.744 70.656 70.656c0 13.824-4.096 26.624-10.752 37.376-12.288 19.456-33.28 32.256-57.856 33.28h-5.12c-24.576-1.024-45.568-13.824-57.856-33.28-6.144-10.752-9.728-24.064-9.728-37.376z m32.768 132.096c11.776 3.584 24.576 5.12 37.888 5.12s25.6-2.048 37.888-5.12l82.944 143.872-124.416 72.192-118.784-68.608 84.48-147.456z m-301.568 526.336c-38.912 0-70.656-31.744-70.656-70.656 0-38.912 31.744-70.656 70.656-70.656h2.56c24.576 1.024 45.568 13.824 57.856 33.28 1.024 1.536 1.536 2.56 2.56 4.096 5.12 9.728 8.192 20.992 8.192 33.28 0 11.776-3.072 23.552-8.192 33.28-12.288 22.016-35.84 37.376-62.976 37.376z m133.12-103.936c-6.144-25.6-19.456-48.128-37.888-65.536l88.064-152.576 122.88 70.656v146.944h-173.056z m239.616-151.552l120.832-69.632 90.112 155.648c-18.432 17.408-31.744 39.936-37.888 65.536h-173.056v-151.552z m306.176 255.488c-27.136 0-50.688-15.36-62.464-37.376-5.12-9.728-8.192-21.504-8.192-33.28 0-12.288 3.072-23.552 8.192-33.28 0.512-1.536 1.536-3.072 2.56-4.096 12.288-19.456 33.28-32.768 57.856-33.28h2.56c38.912 0 70.656 31.744 70.656 70.656-0.512 38.912-32.256 70.656-71.168 70.656z"></path></symbol><symbol id="icon-jiedian" viewBox="0 0 1024 1024"><path d="M882.1914 744.849921c-60.501005 0-112.097749 38.645092-131.425292 92.560342H244.912146c-80.468136 0-145.955927-62.61964-145.955926-139.570043 0-77.000371 65.487791-139.570043 145.955926-139.570043h136.312143c19.497433 53.585462 70.934281 91.950735 131.205434 91.950735 60.241173 0 111.688014-38.375266 131.205434-91.950735h136.452052C914.210763 558.280171 1023.340425 453.92743 1023.340425 325.660102S914.210763 93.040033 780.077216 93.040033H273.093982c-19.197627-54.165089-70.944274-93.040033-131.595182-93.040033C64.508422 0 1.928757 62.569672 1.928757 139.560049c0 76.950403 62.579665 139.570043 139.570043 139.570043 60.640915 0 112.377569-38.894931 131.585189-93.040033h506.993227c80.468136 0 145.955927 62.61964 145.955927 139.570043 0 77.000371-65.487791 139.570043-145.955927 139.570043H644.414655c-18.887826-54.734722-70.924287-94.139324-131.974938-94.139325-61.090625 0-113.097105 39.394609-131.974938 94.139325H244.912146c-134.133547 0-243.263209 104.352741-243.263209 232.620069 0 128.267328 109.129663 232.620069 243.263209 232.620069h505.524174c19.057717 54.394941 70.9043 93.529717 131.75508 93.529717 76.950403 0 139.570043-62.61964 139.570043-139.570043 0.009994-77.000371-62.61964-139.580036-139.570043-139.580036zM141.428845 207.036558c-37.166045 0-67.376574-30.230515-67.376574-67.376573 0-37.166045 30.210528-67.376574 67.376574-67.376574 37.146058 0 67.376574 30.210528 67.376573 67.376574 0 37.146058-30.230515 67.376574-67.376573 67.376573z m371.000878 236.257725c37.146058 0 67.376574 30.210528 67.376574 67.376574 0 37.146058-30.230515 67.376574-67.376574 67.376573-37.166045 0-67.376574-30.230515-67.376574-67.376573 0.009994-37.176039 30.210528-67.376574 67.376574-67.376574z m369.761677 508.502254c-37.166045 0-67.376574-30.230515-67.376574-67.376573 0-37.166045 30.210528-67.376574 67.376574-67.376574 37.146058 0 67.376574 30.210528 67.376574 67.376574 0 37.146058-30.230515 67.376574-67.376574 67.376573z"></path></symbol><symbol id="icon-shitujiedianxianshi" viewBox="0 0 1367 1024"><path d="M1235.403267 512.03593a130.596433 130.596433 0 0 0-42.68749 7.796802l-193.295715-290.430872A149.113837 149.113837 0 1 0 724.647767 149.48464a151.777744 151.777744 0 0 0 37.229729 97.849864l-194.920048 357.873209a220.519548 220.519548 0 0 0-55.162374-8.511508 207.914718 207.914718 0 0 0-114.807909 34.046035L240.401393 443.683967a126.763005 126.763005 0 0 0-75.564005-182.380192 127.737605 127.737605 0 1 0 12.99467 239.816633l157.625345 188.422713A213.307506 213.307506 0 1 0 643.23616 643.282096l190.956674-350.856087a164.187654 164.187654 0 0 0 39.56877 6.172468 143.591102 143.591102 0 0 0 60.555162-12.99467l192.516034 288.026859a121.760057 121.760057 0 0 0-18.582377 64.973349 128.127445 128.127445 0 1 0 127.152844-126.568085zM129.621832 448.362048a68.157044 68.157044 0 1 1 68.157044-68.092071A68.157044 68.157044 0 0 1 129.621832 448.362048z m528.948039 365.670011a145.86517 145.86517 0 1 1-145.86517-145.86517 145.86517 145.86517 0 0 1 145.86517 145.86517z m215.12676-577.937944a87.389155 87.389155 0 1 1 87.389155-87.389155 87.389155 87.389155 0 0 1-87.389155 87.389155z m365.085251 474.305451a68.157044 68.157044 0 1 1 68.222017-68.416937 68.157044 68.157044 0 0 1-68.222017 68.157044z"></path></symbol><symbol id="icon-shituxianshiquanbujiedian" viewBox="0 0 1367 1024"><path d="M1235.403267 512.03593a130.596433 130.596433 0 0 0-42.68749 7.796802l-193.295715-290.430872A149.113837 149.113837 0 1 0 724.647767 149.48464a151.777744 151.777744 0 0 0 37.229729 97.849864l-194.920048 357.873209a220.519548 220.519548 0 0 0-55.162374-8.511508 207.914718 207.914718 0 0 0-114.807909 34.046035L240.401393 443.683967a126.763005 126.763005 0 0 0-75.564005-182.380192 127.737605 127.737605 0 1 0 12.99467 239.816633l157.625345 188.422713A213.307506 213.307506 0 1 0 643.23616 643.282096l190.956674-350.856087a164.187654 164.187654 0 0 0 39.56877 6.172468 143.591102 143.591102 0 0 0 60.555162-12.99467l192.516034 288.026859a121.760057 121.760057 0 0 0-18.582377 64.973349 128.127445 128.127445 0 1 0 127.152844-126.568085zM129.621832 448.362048a68.157044 68.157044 0 1 1 68.157044-68.092071A68.157044 68.157044 0 0 1 129.621832 448.362048z m528.948039 365.670011a145.86517 145.86517 0 1 1-145.86517-145.86517 145.86517 145.86517 0 0 1 145.86517 145.86517z m215.12676-577.937944a87.389155 87.389155 0 1 1 87.389155-87.389155 87.389155 87.389155 0 0 1-87.389155 87.389155z m365.085251 474.305451a68.157044 68.157044 0 1 1 68.222017-68.416937 68.157044 68.157044 0 0 1-68.222017 68.157044z"></path></symbol><symbol id="icon-suoxiao" viewBox="0 0 1024 1024"><path d="M768 448a320 320 0 1 0-320 320 320 320 0 0 0 320-320z m64 0A384 384 0 1 1 448 64a384 384 0 0 1 384 384z"></path><path d="M681.28 726.72a32 32 0 0 1 45.44-45.44l160 160a32 32 0 0 1-45.44 45.44zM288 480a32 32 0 0 1 0-64h320a32 32 0 0 1 0 64z"></path></symbol><symbol id="icon-lianjie" viewBox="0 0 1024 1024"><path d="M922.243 97.755c11.49 11.489 11.673 30.002 0.547 41.713l-0.547 0.562-109.119 109.1c57.22 78.348 42.673 195.388-35.778 273.839l-92.83 92.585c-11.572 11.54-30.264 11.653-41.97 0.253l-45.746-44.55-18.089 18.086a29.891 29.891 0 0 1-7.171 5.3l48.764 47.489c11.947 11.638 12.06 30.804 0.248 42.585l-95.875 95.629c-77.416 77.417-192.412 92.603-270.72 38.004L144.04 928.245c-11.676 11.673-30.606 11.673-42.282 0-11.49-11.489-11.673-30.002-0.547-41.713l0.547-0.562 109.677-109.657c-55.981-78.335-41.131-194.413 36.833-272.377l92.831-92.585c11.571-11.54 30.263-11.653 41.969-0.254l42.587 41.474a29.935 29.935 0 0 1 3.678-4.741l0.548-0.562 19.54-19.538-44.11-42.957c-11.828-11.522-12.057-30.422-0.6-42.23l0.351-0.355 95.875-95.63c76.927-76.926 190.96-92.408 269.23-39.03L879.961 97.755c11.676-11.673 30.606-11.673 42.282 0zM362.451 474.49l-71.916 71.727-1.884 1.911c-61.822 63.545-67.916 156.289-16.185 208.02C324.72 808.4 418.82 801.655 482.43 738.044l74.422-74.234-194.401-189.32z m129.807-5.04l-20.096 20.094a30.035 30.035 0 0 1-4.643 3.8l63.25 61.595a29.82 29.82 0 0 1 5.113-7.308l0.547-0.561 17.532-17.531-61.703-60.089z m260.889-198.691c-52.254-52.254-146.353-45.509-209.964 18.102l-74.422 74.234 194.4 189.322 71.939-71.75 1.862-1.888c61.822-63.545 67.916-156.29 16.185-208.02z" fill="#333333"></path></symbol><symbol id="icon-xiantiao" viewBox="0 0 1024 1024"><path d="M827.076923 157.538462a39.384615 39.384615 0 1 1-14.375385 76.051692L233.590154 812.701538a39.384615 39.384615 0 1 1-22.291692-22.291692L790.409846 211.298462A39.384615 39.384615 0 0 1 827.076923 157.538462z" fill="#333333"></path></symbol></svg>-->
    <GraphSettingPanel v-if="graphSetting.allowShowSettingPanel" :graph-setting="graphSetting">
      <div slot="settingPanelPlus" slot-scope="{setting}">
        <slot :setting="setting" name="settingPanelPlus" />
      </div>
    </GraphSettingPanel>
    <graph-mini-name-filter v-show="graphSetting.allowShowMiniNameFilter===true" :graph-setting="graphSetting" />
    <graph-mini-tool-bar v-show="graphSetting.allowShowMiniToolBar===true" :graph-setting="graphSetting" />
    <graph-mini-view v-if="graphSetting.allowShowMiniView===true" :graph-setting="graphSetting" />
    <slot :graph="this" name="graphPlug" />
    <div :style="{width: '100%',height : '100%', 'background-image': 'url('+graphSetting.backgrounImage+')'}" :class="[graphSetting.layoutClassName, (graphSetting.backgrounImageNoRepeat?'rel-map-background-norepeat':'')]" class="rel-map" @mousedown.left.stop="onDragStart($event)" @mousewheel="mouseListener">
      <div ref="seeksRGCanvas" :style="canvasSizeAndPosition" class="rel-map-canvas">
<!--        <svg aria-hidden="true" style="position: absolute; width: 0px; height: 0px; overflow: hidden;">-->
<!--          <symbol id="icon-add-select" viewBox="0 0 128 128"><path d="M544 213.333333v266.666667H810.666667v64H544V810.666667h-64V544H213.333333v-64h266.666667V213.333333z"></path></symbol>-->
<!--          <symbol id="icon-sami-select" viewBox="0 0 128 128"><path d="M810.666667 480v64H213.333333v-64z"></path></symbol>-->
<!--        </svg>-->
        <div class="rel-nodediv rel-nodediv-for-webkit">
          <SeeksRGNode v-for="thisNode in nodeViewList" :key="thisNode.seeks_id" :node-props="thisNode" :on-node-click="onRGNodeClick" :graph-setting="graphSetting">
            <template slot="node" slot-scope="{node}">
              <slot :node="node" name="node" />
            </template>
          </SeeksRGNode>
        </div>
        <div ref="rgCanvas" class="rel-linediv" style="overflow: visible">
          <svg :style="{width : graphSetting.canvasSize.width + 'px',height: graphSetting.canvasSize.height + 'px'}" style="overflow: visible" xmlns="http://www.w3.org/2000/svg" version="1.1">
            <defs>
              <linearGradient :id="graphSetting.instanceId+'-lineStyle'" x1="1" y1="0" x2="0" y2="0">
                <stop offset="0%" stop-color="#e52c5c" />
                <stop offset="100%" stop-color="#FD8B37" />
              </linearGradient>
              <!--              <marker-->
              <!--                :id="graphSetting.instanceId+'-arrow-default'"-->
              <!--                markerUnits="strokeWidth"-->
              <!--                markerWidth="12"-->
              <!--                markerHeight="12"-->
              <!--                viewBox="0 0 12 12"-->
              <!--                refX="6"-->
              <!--                refY="6"-->
              <!--                orient="auto"-->
              <!--              >-->
              <!--                <path :style="{fill: graphSetting.defaultLineColor}" d="M2,2 L10,6 L2,10 L6,6 L2,2" />-->
              <!--              </marker>-->
              <marker
                      :id="graphSetting.instanceId+'-arrow-default'"
                      :markerWidth="graphSetting.defaultLineMarker.markerWidth"
                      :markerHeight="graphSetting.defaultLineMarker.markerHeight"
                      :refX="graphSetting.defaultLineMarker.refX"
                      :refY="graphSetting.defaultLineMarker.refY"
                      marker-units="strokeWidth"
                      orient="auto"
                      viewBox="0 0 12 12"
              >
                <path :style="{fill: graphSetting.defaultLineColor}" :d="graphSetting.defaultLineMarker.data" />
              </marker>
              <marker
                      :id="graphSetting.instanceId+'-arrow-checked'"
                      markerUnits="strokeWidth"
                      markerWidth="12"
                      markerHeight="12"
                      viewBox="0 0 12 12"
                      refX="6"
                      refY="6"
                      orient="auto"
              >
                <path :style="{fill: '#FD8B37'}" d="M2,2 L10,6 L2,10 L6,6 L2,2" />
              </marker>
              <marker
                      v-for="thisColor in allLineColors"
                      :id="graphSetting.instanceId+'-arrow-'+thisColor.id"
                      :key="thisColor.id"
                      :markerWidth="graphSetting.defaultLineMarker.markerWidth"
                      :markerHeight="graphSetting.defaultLineMarker.markerHeight"
                      :refX="graphSetting.defaultLineMarker.refX"
                      :refY="graphSetting.defaultLineMarker.refY"
                      marker-units="strokeWidth"
                      orient="auto"
              >
                <path :fill="graphSetting.defaultLineMarker.color || thisColor.color" :d="graphSetting.defaultLineMarker.data" />
              </marker>
              <!--<marker-->
              <!--v-for="thisColor in allLineColors"-->
              <!--:id="graphSetting.instanceId+'-arrow-'+thisColor.id"-->
              <!--:key="thisColor.id"-->
              <!--marker-units="strokeWidth"-->
              <!--orient="auto"-->
              <!--markerWidth="15"-->
              <!--markerHeight="15"-->
              <!--refX="50"-->
              <!--refY="7"-->
              <!--&gt;-->
              <!--<path :fill="thisColor.color" d="M 14 7 L 1 .3 L 4 7 L .4 13 L 14 7, Z" />-->
              <!--</marker>-->
            </defs>
            <SeeksRGLink v-for="thisLine in lineViewList" :key="thisLine.seeks_id" :line-props="thisLine" :graph-setting="graphSetting" :on-line-click="onRGLineClick" />
          </svg>
        </div>
      </div>
    </div>
    <graph-bottom-panel v-if="$scopedSlots.bottomPanel" :graph-setting="graphSetting">
      <template slot="bottomPanel">
        <slot :graph="this" name="bottomPanel" />
      </template>
    </graph-bottom-panel>
    <div style="clear: both;height:1px;" />
    <div v-if="isShowZoomCenter" :style="{left:(debugPanelPosition?'':'0px'),right:(debugPanelPosition?'0px':'')}" style="position: fixed;top:0px;right:0px;font-size: 12px;background-color: #333333;color:#ffffff;z-index: 9999;padding:20px;" @click="moveDebugPanel">
      <div><pre>{{ JSON.stringify(graphSetting.canvasOffset, null, 2) }}</pre></div>
      <div><pre>{{ JSON.stringify(graphSetting.viewNVInfo, null, 2) }}</pre></div>
      <div><pre>{{ JSON.stringify(graphSetting.canvasNVInfo, null, 2) }}</pre></div>
    </div>
    <!--<div v-if="isShowZoomCenter" :style="{'left': (currentZoomSet.NMCanvasStart.x + currentZoomSet.NMViewPosition.x) + 'px', top: currentZoomSet.NMViewPosition.y + 'px' }" style="position: fixed;top:0px;width:2px;height:2000px;z-index: 99999;background-color: blue;opacity: 0.6;"/>-->
    <!--<div v-if="isShowZoomCenter" :style="{'top': (currentZoomSet.NMCanvasStart.y + currentZoomSet.NMViewPosition.y) + 'px', left: currentZoomSet.NMViewPosition.x + 'px' }" style="position: fixed;left:0px;height:2px;width:2000px;z-index: 99999;background-color: blue;opacity: 0.6;"/>-->
    <!--<div v-if="isShowZoomCenter" :style="{'left': (currentZoomSet.NMCanvasCenter.x + currentZoomSet.NMViewPosition.x) + 'px', top: currentZoomSet.NMViewPosition.y + 'px' }" style="position: fixed;top:0px;width:4px;height:2000px;z-index: 99999;background-color: green;opacity: 0.4;"/>-->
    <!--<div v-if="isShowZoomCenter" :style="{'top': (currentZoomSet.NMCanvasCenter.y + currentZoomSet.NMViewPosition.y) + 'px', left: currentZoomSet.NMViewPosition.x + 'px' }" style="position: fixed;left:0px;height:4px;width:2000px;z-index: 99999;background-color: green;opacity: 0.4;"/>-->
    <!--<div v-if="isShowZoomCenter" :style="{'left': (currentZoomSet.NMCanvasEnd.x + currentZoomSet.NMViewPosition.x) + 'px', top: currentZoomSet.NMViewPosition.y + 'px' }" style="position: fixed;top:0px;width:2px;height:2000px;z-index: 99999;background-color: blue;opacity: 0.6;"/>-->
    <!--<div v-if="isShowZoomCenter" :style="{'top': (currentZoomSet.NMCanvasEnd.y + currentZoomSet.NMViewPosition.y) + 'px', left: currentZoomSet.NMViewPosition.x + 'px' }" style="position: fixed;left:0px;height:2px;width:2000px;z-index: 99999;background-color: blue;opacity: 0.6;"/>-->
    <!--<div v-if="isShowZoomCenter" :style="{'left': (currentZoomSet.NMZoomCenter.x + currentZoomSet.NMViewPosition.x - 10) + 'px', top: currentZoomSet.NMViewPosition.y + 'px' }" style="position: fixed;top:0px;width:2px;height:2000px;z-index: 99999;background-color: red;opacity: 0.6;"/>-->
    <!--<div v-if="isShowZoomCenter" :style="{'top': (currentZoomSet.NMZoomCenter.y + currentZoomSet.NMViewPosition.y - 10) + 'px', left: currentZoomSet.NMViewPosition.x + 'px' }" style="position: fixed;left:0px;height:2px;width:2000px;z-index: 99999;background-color: red;opacity: 0.6;"/>-->
    <!--<div v-if="isShowZoomCenter" :style="{'left': (currentZoomSet.NMViewCenter.x + currentZoomSet.NMViewPosition.x) + 'px', top: currentZoomSet.NMViewPosition.y + 'px' }" style="position: fixed;top:0px;width:2px;height:2000px;z-index: 99999;background-color: black;opacity: 0.6;"/>-->
    <!--<div v-if="isShowZoomCenter" :style="{'top': (currentZoomSet.NMViewCenter.y + currentZoomSet.NMViewPosition.y) + 'px', left: currentZoomSet.NMViewPosition.x + 'px' }" style="position: fixed;left:0px;height:2px;width:2000px;z-index: 99999;background-color: black;opacity: 0.6;"/>-->

    <!--<div v-if="isShowZoomCenter" :style="{'left': (newZoomSet.NMCanvasStart.x + newZoomSet.NMViewPosition.x) + 'px', top: newZoomSet.NMViewPosition.y + 'px' }" style="position: fixed;top:0px;width:2px;height:2000px;z-index: 99999;border-left: blue dotted 2px;opacity: 0.6;"/>-->
    <!--<div v-if="isShowZoomCenter" :style="{'top': (newZoomSet.NMCanvasStart.y + newZoomSet.NMViewPosition.y) + 'px', left: newZoomSet.NMViewPosition.x + 'px' }" style="position: fixed;left:0px;height:2px;width:2000px;z-index: 99999;border-top: blue dotted 2px;opacity: 0.6;"/>-->
    <!--<div v-if="isShowZoomCenter" :style="{'left': (newZoomSet.NMCanvasCenter.x + newZoomSet.NMViewPosition.x) + 'px', top: newZoomSet.NMViewPosition.y + 'px' }" style="position: fixed;top:0px;width:4px;height:2000px;z-index: 99999;border-left: green dotted 2px;opacity: 0.4;"/>-->
    <!--<div v-if="isShowZoomCenter" :style="{'top': (newZoomSet.NMCanvasCenter.y + newZoomSet.NMViewPosition.y) + 'px', left: newZoomSet.NMViewPosition.x + 'px' }" style="position: fixed;left:0px;height:4px;width:2000px;z-index: 99999;border-top: green dotted 2px;opacity: 0.4;"/>-->
    <!--<div v-if="isShowZoomCenter" :style="{'left': (newZoomSet.NMCanvasEnd.x + newZoomSet.NMViewPosition.x) + 'px', top: newZoomSet.NMViewPosition.y + 'px' }" style="position: fixed;top:0px;width:2px;height:2000px;z-index: 99999;border-left: blue dotted 2px;opacity: 0.6;"/>-->
    <!--<div v-if="isShowZoomCenter" :style="{'top': (newZoomSet.NMCanvasEnd.y + newZoomSet.NMViewPosition.y) + 'px', left: newZoomSet.NMViewPosition.x + 'px' }" style="position: fixed;left:0px;height:2px;width:2000px;z-index: 99999;border-top: blue dotted 2px;opacity: 0.6;"/>-->
    <!--<div v-if="isShowZoomCenter" :style="{'left': (zoomCenter_of_newSize.x + newZoomSet.NMViewPosition.x - 10) + 'px', top: newZoomSet.NMViewPosition.y + 'px' }" style="position: fixed;top:0px;width:2px;height:2000px;z-index: 99999;border-left: red dotted 2px;opacity: 0.6;"/>-->
    <!--<div v-if="isShowZoomCenter" :style="{'top': (zoomCenter_of_newSize.y + newZoomSet.NMViewPosition.y - 10) + 'px', left: newZoomSet.NMViewPosition.x + 'px' }" style="position: fixed;left:0px;height:2px;width:2000px;z-index: 99999;border-top: red dotted 2px;opacity: 0.6;"/>-->
    <div v-if="isShowZoomCenter" :style="{'left': (graphSetting.canvasNVInfo.x + graphSetting.viewNVInfo.x) + 'px', top: graphSetting.viewNVInfo.y + 'px' }" style="position: fixed;top:0px;width:2px;height:2000px;z-index: 99999;border-left: blue dotted 2px;opacity: 0.6;" />
    <div v-if="isShowZoomCenter" :style="{'top': (graphSetting.canvasNVInfo.y + graphSetting.viewNVInfo.y) + 'px', left: graphSetting.viewNVInfo.x + 'px' }" style="position: fixed;left:0px;height:2px;width:2000px;z-index: 99999;border-top: blue dotted 2px;opacity: 0.6;" />
    <div v-if="isShowZoomCenter" :style="{'left': (graphSetting.canvasNVInfo.x + graphSetting.canvasNVInfo.width/2 + graphSetting.viewNVInfo.x) + 'px', top: graphSetting.viewNVInfo.y + 'px' }" style="position: fixed;top:0px;width:4px;height:2000px;z-index: 99999;border-left: green dotted 2px;opacity: 0.4;" />
    <div v-if="isShowZoomCenter" :style="{'top': (graphSetting.canvasNVInfo.y + graphSetting.canvasNVInfo.height/2 + graphSetting.viewNVInfo.y) + 'px', left: graphSetting.viewNVInfo.x + 'px' }" style="position: fixed;left:0px;height:4px;width:2000px;z-index: 99999;border-top: green dotted 2px;opacity: 0.4;" />
    <div v-if="isShowZoomCenter" :style="{'left': (graphSetting.canvasNVInfo.x + graphSetting.canvasNVInfo.width + graphSetting.viewNVInfo.x) + 'px', top: graphSetting.viewNVInfo.y + 'px' }" style="position: fixed;top:0px;width:2px;height:2000px;z-index: 99999;border-left: blue dotted 2px;opacity: 0.6;" />
    <div v-if="isShowZoomCenter" :style="{'top': (graphSetting.canvasNVInfo.y + graphSetting.canvasNVInfo.height + graphSetting.viewNVInfo.y) + 'px', left: graphSetting.viewNVInfo.x + 'px' }" style="position: fixed;left:0px;height:2px;width:2000px;z-index: 99999;border-top: blue dotted 2px;opacity: 0.6;" />
  </div>
</template>

<script>
/* eslint-disable */
  import './core4vue/SeeksGraphIconfont'
  import Vue from 'vue'
  import screenfull from 'screenfull'
  import html2canvas from 'html2canvas'
  import SeeksRGLayouters from './core4vue/SeeksRGLayouters'
  import SeeksRGUtils from './core4vue/SeeksRGUtils'
  import SeeksRGStore from './core4vue/SeeksRGStore'
  import SeeksRGNode from './core4vue/SeeksRGNode'
  import SeeksRGLink from './core4vue/SeeksRGLink'
  import GraphSettingPanel from './GraphSettingPanel'
  import GraphMiniView from './GraphMiniView'
  import GraphMiniToolBar from './GraphMiniToolBar'
  import GraphMiniNameFilter from './GraphMiniNameFilter'
  import GraphBottomPanel from './GraphBottomPanel'
  export default {
    name: 'SeeksRelationGraph',
    components: { GraphBottomPanel, GraphMiniNameFilter, GraphMiniToolBar, GraphMiniView, SeeksRGNode, SeeksRGLink, GraphSettingPanel },
    props: {
      options: {
        mustUseProp: false,
        default: () => { return {} },
        type: Object
      },
      onNodeClick: {
        mustUseProp: false,
        default: () => { return () => {} },
        type: Function
      },
      onNodeExpand: {
        mustUseProp: false,
        default: () => { return () => {} },
        type: Function
      },
      onNodeCollapse: {
        mustUseProp: false,
        default: () => { return () => {} },
        type: Function
      },
      onLineClick: {
        mustUseProp: false,
        default: () => { return () => {} },
        type: Function
      },
      onDownloadExcel: {
        mustUseProp: false,
        default: null,
        type: Function
      },
      beforeDownloadImage: {
        mustUseProp: false,
        default: null,
        type: Function
      }
    },
    data() {
      var wheelEvent = {}
      // console.log('this.options.disableZoom:', this.options.disableZoom)
      // if (this.options.disableZoom) {
      //   wheelEvent = {
      //     'mousewheel':this.mouseListener
      //   }
      // } else {
      //   wheelEvent = {
      //     'mousewheel':this.mouseListener
      //   }
      //   console.log('wheelEvent:', wheelEvent)
      // }
      return {
        wheelEvent,
        version: '1.1.0',
        el: {
          offsetWidth: 500,
          offsetHeight: 500,
          offsetTop: 0,
          offsetLeft: 0
        },
        canvasBackgroundColor: 'transparent',
        isNeedFixedTools: false,
        isNeedFixedTools4Bottom: false,
        seeksNodeIdIndex: 1,
        search_text: '',
        instanceId: '',
        SeeksRGStore: null,
        graphSetting: {},
        graphData: {
          nodes: [],
          lines: [],
          rootNode: null,
          nodes_map: {},
          lines_map: {}
        },
        nodeViewList: [],
        lineViewList: [],
        allLineColors: [],
        viewOffset: {
          windowHeight: 500,
          positionTop: 100,
          left: 0,
          top: 0
        },
        viewSizeIsInited: false,
        isShowZoomCenter: false,
        debugPanelPosition: true,
        zoomCenter_of_newSize: { x: 0, y: 0 },
        currentZoomSet: null,
        newZoomSet: null,
        alive: true
      }
    },
    computed: {
      canvasSizeAndPosition() {
        return {
          'width': this.graphSetting.canvasSize.width + 'px',
          'height': this.graphSetting.canvasSize.height + 'px',
          'margin-left': (this.graphSetting.canvasOffset.x) + 'px',
          'margin-top': (this.graphSetting.canvasOffset.y) + 'px',
          'background-color': this.canvasBackgroundColor,
          'transform': 'scale(' + this.graphSetting.canvasZoom / 100 + ',' + this.graphSetting.canvasZoom / 100 + ')'
          // 'transform-origin': (this.graphSetting.canvasOffset.zoom_buff_x * 100).toFixed(2) + '% ' + (this.graphSetting.canvasOffset.zoom_buff_y * 100).toFixed(2) + '%'
        }
      }
    },
    watch: {
      'graphSetting.fullscreen': function(newV, oldV) {
        if (oldV === true || oldV === false) {
          screenfull.toggle(this.$refs.seeksRelationGraph)
        }
      }
    },
    created() {
      this.SeeksRGStore = SeeksRGStore.createNewStore(this.options || {})
      this.graphSetting = this.SeeksRGStore.graphSetting
      this.graphSetting.instanceId = 'SRG' + parseInt(Math.random() * 100000)
      console.log(
              `%c relation-graph %c Version v${this.version} %c More info: https://github.com/seeksdream/relation-graph %c`,
              'background:#35495e ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff',
              'background:#41b883 ; padding: 1px; border-radius: 0 3px 3px 0;  color: #fff',
              'background:#fff ; padding: 1px; border-radius: 0 3px 3px 0;  color: #41b883',
              'background:transparent'
      )
      if (Vue.version.substring(0,4) === '2.5.') {
        console.log('注意：当你使用的vue版本低于2.6时，你只能通过插槽slot[node]来显示节点内容，示例请参考：http://relation-graph.com/#/demo/adv-slot')
      }
    },
    mounted() {
      this.init()
      window.addEventListener('scroll', function() {
        this.syncToolsPosition()
      }.bind(this))
      // setInterval(function() {
      //   this.showZoomCenter()
      // }.bind(this), 1000)
    },
    beforeDestroy() {
      this.alive = false
      const elx = this.$refs.seeksRelationGraph
      elx.remove()
    },
    show() {
      this.resetViewSize()
      this.refreshNVAnalysisInfo()
      this.syncToolsPosition()
    },
    methods: {
      // getCanvasSizeAndPosition() {
      //   console.log('重新计算:getCanvasSizeAndPosition')
      //   return {
      //     'width': this.graphSetting.canvasSize.width + 'px',
      //     'height': this.graphSetting.canvasSize.height + 'px',
      //     'margin-left': (this.graphSetting.canvasOffset.x) + 'px',
      //     'margin-top': (this.graphSetting.canvasOffset.y) + 'px',
      //     'background-color': this.canvasBackgroundColor,
      //     'transform': 'scale(' + this.graphSetting.canvasZoom / 100 + ',' + this.graphSetting.canvasZoom / 100 + ')'
      //     // 'transform-origin': (this.graphSetting.canvasOffset.zoom_buff_x * 100).toFixed(2) + '% ' + (this.graphSetting.canvasOffset.zoom_buff_y * 100).toFixed(2) + '%'
      //   }
      // },
      init() {
        this.$refs.rgCanvas.style.setProperty('--stroke', 'url(\'#' + this.graphSetting.instanceId + '-lineStyle\')')
        this.$refs.rgCanvas.style.setProperty('--markerEnd', 'url(\'#' + this.graphSetting.instanceId + '-arrow-default\')')
        this.$refs.rgCanvas.style.setProperty('--markerEndChecked', 'url(\'#' + this.graphSetting.instanceId + '-arrow-checked\')')
        // console.log('#############Seeks graph viewSize:', this.graphSetting.viewSize.width, this.graphSetting.viewSize.height)
        this.cycleTask()
        this.resetViewSize()
        this.refreshNVAnalysisInfo()
        this.syncToolsPosition()
      },
      cycleTask() {
        if (this.alive) {
          var _box = this.$refs.seeksRelationGraph.getBoundingClientRect()
          if (_box.width !== this.graphSetting.viewSize.width || _box.height !== this.graphSetting.viewSize.height) {
            if (window.SeeksGraphDebug) console.log('relation-graph:view-size-changed:', [this.graphSetting.viewSize.width, this.graphSetting.viewSize.height], [_box.width, _box.height])
            this.graphSetting.viewSize.width = _box.width
            this.graphSetting.viewSize.height = _box.height
            this.resetViewSize()
            this.refreshNVAnalysisInfo()
            this.wow()
            if (this.viewSizeIsInited && this.graphSetting.moveToCenterWhenResize) {
              if (window.SeeksGraphDebug) console.log('relation-graph:move to center:', [this.graphSetting.viewSize.width, this.graphSetting.viewSize.height], [_box.width, _box.height])
              this.$nextTick(() => {
                // this.focusRootNode()
                var _min_x = 9999999
                var _min_y = 9999999
                var _max_x = 0
                var _max_y = 0
                this.nodeViewList.forEach(thisNode => {
                  if (thisNode.x < _min_x) {
                    _min_x = thisNode.x
                  }
                  if (thisNode.x > _max_x) {
                    _max_x = thisNode.x + thisNode.el.offsetWidth
                  }
                  if (thisNode.y < _min_y) {
                    _min_y = thisNode.y
                  }
                  if (thisNode.y > _max_y) {
                    _max_y = thisNode.y + thisNode.el.offsetHeight
                  }
                })
                var _stuff_width = _max_x - _min_x + 50
                var _stuff_height = _max_y - _min_y + 50
                var _final_x = (this.graphSetting.viewSize.width - _stuff_width)/2 - _min_x
                var _final_y = (this.graphSetting.viewSize.height - _stuff_height)/2 - _min_y
                // if (window.SeeksGraphDebug) console.log('_min_x, _min_y, _stuff_width, _stuff_height:', _min_x, _min_y, _stuff_width, _stuff_height)
                // var _final_x = -_min_x
                // var _final_y = -_min_y
                this.animateGoto(_final_x, _final_y, 500, () => {
                  // this.graphSetting.checkedNodeId = thisNode.id
                  this.refreshNVAnalysisInfo()
                })
              })
            }
          }
          setTimeout(() => {
            this.cycleTask()
          }, 1000)
        }
      },
      setOptions(options, callback) {
        this.SeeksRGStore = SeeksRGStore.createNewStore(options)
        this.graphSetting = this.SeeksRGStore.graphSetting
        this.graphSetting.instanceId = 'SRG' + parseInt(Math.random() * 100000)
        this.init()
        if (this.graphSetting.layouts && this.graphSetting.layouts.length > 0) {
          var _defaultLayoutSetting = this.graphSetting.layouts[0]
          if (window.SeeksGraphDebug) console.log('创建默认布局器：', this.graphSetting.layoutName)
          if (_defaultLayoutSetting.layouter) {
            this.graphSetting.layouter = _defaultLayoutSetting.layouter
          } else {
            this.graphSetting.layouter = SeeksRGLayouters.createLayout(_defaultLayoutSetting, this.graphSetting)
          }
        } else {
          console.log('你需要设置layouts来指定当前图谱可以使用的布局器！')
        }
        this.doLayout()
        callback(this)
      },
      moveDebugPanel() {
        this.debugPanelPosition = !this.debugPanelPosition
      },
      mouseListenerEmpty() {
        console.log('mouseListenerEmpty')
      },
      mouseListener(e) {
        // if (e.target !== this.$refs.seeksRGCanvas) {
        //   return
        // }
        // e.stopPropagation()
        // console.log('mouseListener')
        if (this.graphSetting.disableZoom) {
          e.cancelBubble = false
          return true
        }
        try{
          e.cancelBubble = true
          e.preventDefault()
          e.stopPropagation()
        }catch (e) {
          // xxx
        }
        var userZoomCenter = {
          x: e.clientX,
          y: e.clientY
        }
        // console.log('---- center:', userZoomCenter.x, userZoomCenter.y)
        // var _isMac = /macintosh|mac os x/i.test(navigator.userAgent)
        var _isMac = false
        var _deltaY = e.deltaY
        if (_deltaY === undefined) {
          _deltaY = e.wheelDelta
        }
        // console.log('mouseListenerEmpty:', _isMac, e.deltaY, e.wheelDelta, e.which, e.detail)
        var _zoomDirection = _isMac ? 1 : -1
        if (_deltaY > 0) {
          this.zoom(5 * _zoomDirection, userZoomCenter)
        } else {
          this.zoom(-5 * _zoomDirection, userZoomCenter)
        }

      },
      getPositionOfCanvas(e) {
        var userZoomCenter = {
          x: e.offsetX,
          y: e.offsetY
        }
        if (window.SeeksGraphDebug) console.log('[F]', userZoomCenter.x, userZoomCenter.y)
        var currentNode = e.target.parentNode
        for (var i = 0; i < 8; i++) {
          if (i > 6) {
            if (window.SeeksGraphDebug) console.log('getPositionOfCanvas error', e)
          }
          if (currentNode.classList.contains('rel-map-canvas')) {
            if (window.SeeksGraphDebug) console.log('[S]', currentNode.tagName + '.' + currentNode.className)
            break
          } else {
            userZoomCenter.x += currentNode.offsetLeft || 0
            userZoomCenter.y += currentNode.offsetTop || 0
            if (window.SeeksGraphDebug) console.log('[' + i + ']', currentNode.tagName + '.' + currentNode.className, ':', currentNode.offsetLeft, currentNode.offsetTop)
            currentNode = currentNode.parentNode
          }
        }
        if (window.SeeksGraphDebug) console.log('[F]', userZoomCenter.x, userZoomCenter.y)
        return userZoomCenter
      },
      zoom(buff, userZoomCenter) {
        if ((this.graphSetting.canvasZoom + buff) < 10) {
          return
        }
        var __new_zoom_value = this.graphSetting.canvasZoom + buff
        var zoomCenter = this.showZoomCenter(userZoomCenter, buff)
        // console.log('zoomCenter:', zoomCenter.x, zoomCenter.y)
        // if (userZoomCenter) return
        // this.graphSetting.canvasOffset.zoom_buff_x = zoomCenter.buff_x
        // this.graphSetting.canvasOffset.zoom_buff_y = zoomCenter.buff_y
        // var _buff_x = this.canvasCenter.x - zoomCenter.x
        // var _buff_y = this.canvasCenter.y - zoomCenter.y
        // console.log('offset buff:', _buff_x.toFixed(0), _buff_y.toFixed(0))
        // this.graphSetting.canvasOffset.zoom_buff_x = _buff_x * ((this.graphSetting.canvasZoom - 100) / 100)
        // this.graphSetting.canvasOffset.zoom_buff_y = _buff_y * ((this.graphSetting.canvasZoom - 100) / 100)
        this.graphSetting.canvasOffset.x += zoomCenter.buff_x
        this.graphSetting.canvasOffset.y += zoomCenter.buff_y
        this.graphSetting.canvasZoom = __new_zoom_value
        this.refreshNVAnalysisInfo()
      },
      showZoomCenter(userZoomCenter, zoomBuff) {
        if (!this.$refs.seeksRelationGraph) {
          return
        }
        var _current_zoom = this.graphSetting.canvasZoom / 100
        var _new_zoom = (this.graphSetting.canvasZoom + zoomBuff) / 100
        this.currentZoomSet = this.analysisByZoom(_current_zoom, userZoomCenter)
        this.newZoomSet = this.analysisByZoom(_new_zoom, userZoomCenter)
        // console.log('this.currentZoomSet:', this.currentZoomSet)
        // console.log('this.currentZoomSet:', this.newZoomSet)
        const a = _new_zoom / _current_zoom
        const b = 0
        const c = 0
        const d = _new_zoom / _current_zoom
        var e = 0
        var f = 0
        this.zoomCenter_of_newSize.x = a * this.currentZoomSet.NMViewBuff.x + c * this.currentZoomSet.NMViewBuff.y + e
        this.zoomCenter_of_newSize.y = b * this.currentZoomSet.NMViewBuff.x + d * this.currentZoomSet.NMViewBuff.y + f
        var buff_x = this.currentZoomSet.NMViewBuff.x - this.zoomCenter_of_newSize.x
        var buff_y = this.currentZoomSet.NMViewBuff.y - this.zoomCenter_of_newSize.y
        this.zoomCenter_of_newSize.x += this.currentZoomSet.NMCanvasCenter.x
        this.zoomCenter_of_newSize.y += this.currentZoomSet.NMCanvasCenter.y
        // e = this.currentZoomSet.NMViewBuff.x
        // f = this.currentZoomSet.NMViewBuff.y
        // new start
        var old_x = this.currentZoomSet.NMCanvasStart.x - this.currentZoomSet.NMCanvasCenter.x
        var old_y = this.currentZoomSet.NMCanvasStart.y - this.currentZoomSet.NMCanvasCenter.y
        var new_x = a * old_x + c * old_y + e
        var new_y = b * old_x + d * old_y + f
        this.newZoomSet.NMCanvasStart.x = buff_x + this.currentZoomSet.NMCanvasCenter.x + new_x
        this.newZoomSet.NMCanvasStart.y = buff_x + this.currentZoomSet.NMCanvasCenter.y + new_y
        // new end
        old_x = this.currentZoomSet.NMCanvasEnd.x - this.currentZoomSet.NMCanvasCenter.x
        old_y = this.currentZoomSet.NMCanvasEnd.y - this.currentZoomSet.NMCanvasCenter.y
        new_x = a * old_x + c * old_y + e
        new_y = b * old_x + d * old_y + f
        this.newZoomSet.NMCanvasEnd.x = buff_x + this.currentZoomSet.NMCanvasCenter.x + new_x
        this.newZoomSet.NMCanvasEnd.y = buff_x + this.currentZoomSet.NMCanvasCenter.y + new_y
        this.currentZoomSet.NMCanvasOffsetBuff.x = buff_x
        this.currentZoomSet.NMCanvasOffsetBuff.y = buff_y
        // this.isShowZoomCenter = true
        return {
          buff_x, buff_y
        }
      },
      refreshNVAnalysisInfo() {
        if (!this.$refs.seeksRelationGraph) {
          console.error('cannot get view size !')
          return
        }
        // console.log('reanalysis NV info...')
        var result = {
          NMCanvasCenter: { x: 0, y: 0 }
        }
        var _view_info = this.$refs.seeksRelationGraph.getBoundingClientRect()
        this.graphSetting.viewNVInfo.x = _view_info.left
        this.graphSetting.viewNVInfo.y = _view_info.top
        this.graphSetting.viewNVInfo.width = _view_info.width
        this.graphSetting.viewNVInfo.height = _view_info.height
        var _NM_canvas_width = this.graphSetting.canvasSize.width * (this.graphSetting.canvasZoom / 100)
        var _NM_canvas_height = this.graphSetting.canvasSize.height * (this.graphSetting.canvasZoom / 100)
        result.NMCanvasCenter.x = this.graphSetting.canvasOffset.x + (this.graphSetting.canvasSize.width / 2)
        result.NMCanvasCenter.y = this.graphSetting.canvasOffset.y + (this.graphSetting.canvasSize.height / 2)
        this.graphSetting.canvasNVInfo.x = result.NMCanvasCenter.x - _NM_canvas_width / 2
        this.graphSetting.canvasNVInfo.y = result.NMCanvasCenter.y - _NM_canvas_height / 2
        this.graphSetting.canvasNVInfo.width = _NM_canvas_width
        this.graphSetting.canvasNVInfo.height = _NM_canvas_height
        this.graphSetting.viewELSize.width = _view_info.width
        this.graphSetting.viewELSize.height = _view_info.height
        this.graphSetting.viewELSize.left = _view_info.left
        this.graphSetting.viewELSize.top = _view_info.top
      },
      analysisByZoom(zoom, userZoomCenter) {
        var result = {
          NMViewPosition: { x: 0, y: 0 },
          NMViewCenter: { x: 0, y: 0 },
          NMCanvasCenter: { x: 0, y: 0 },
          NMCanvasStart: { x: 0, y: 0 },
          NMCanvasEnd: { x: 0, y: 0 },
          NMZoomCenter: { x: 0, y: 0 },
          NMViewBuff: { x: 0, y: 0 },
          NMCanvasOffsetBuff: { x: 0, y: 0 },
          NMCanvasSize: { width: 0, height: 0 }
        }
        const windowWidth = this.getWindowWidth()
        const windowHeight = this.getWindowHeight()
        var _view_info = this.$refs.seeksRelationGraph.getBoundingClientRect()
        result.NMViewPosition.x = _view_info.left
        result.NMViewPosition.y = _view_info.top
        if (_view_info.width + result.NMViewPosition.x > windowWidth) {
          result.NMViewCenter.x = (windowWidth - _view_info.left) / 2
        } else {
          result.NMViewCenter.x = _view_info.width / 2
        }
        if (_view_info.height + result.NMViewPosition.y > windowHeight) {
          result.NMViewCenter.y = (windowHeight - _view_info.top) / 2
        } else {
          result.NMViewCenter.y = _view_info.height / 2
        }
        var _NM_canvas_width = this.graphSetting.canvasSize.width * zoom
        var _NM_canvas_height = this.graphSetting.canvasSize.height * zoom
        result.NMCanvasCenter.x = this.graphSetting.canvasOffset.x + (this.graphSetting.canvasSize.width / 2) // + (this.graphSetting.canvasOffset.zoom_buff_x * _NM_canvas_width)
        result.NMCanvasCenter.y = this.graphSetting.canvasOffset.y + (this.graphSetting.canvasSize.height / 2) // + (this.graphSetting.canvasOffset.zoom_buff_y * _NM_canvas_height)
        result.NMCanvasStart.x = result.NMCanvasCenter.x - _NM_canvas_width / 2
        result.NMCanvasStart.y = result.NMCanvasCenter.y - _NM_canvas_height / 2
        result.NMCanvasEnd.x = result.NMCanvasCenter.x + _NM_canvas_width / 2
        result.NMCanvasEnd.y = result.NMCanvasCenter.y + _NM_canvas_height / 2
        result.NMZoomCenter.x = result.NMViewCenter.x
        result.NMZoomCenter.y = result.NMViewCenter.y
        if (userZoomCenter) {
          result.NMZoomCenter.x = userZoomCenter.x - result.NMViewPosition.x
          result.NMZoomCenter.y = userZoomCenter.y - result.NMViewPosition.y
        }
        var _NM_buff_x = result.NMViewCenter.x - result.NMCanvasCenter.x
        var _NM_buff_y = result.NMViewCenter.y - result.NMCanvasCenter.y
        if (userZoomCenter) {
          _NM_buff_x = result.NMZoomCenter.x - result.NMCanvasCenter.x
          _NM_buff_y = result.NMZoomCenter.y - result.NMCanvasCenter.y
        }
        result.NMViewBuff.x = _NM_buff_x
        result.NMViewBuff.y = _NM_buff_y
        result.NMCanvasSize.width = _NM_canvas_width
        result.NMCanvasSize.height = _NM_canvas_height
        return result
      },
      syncToolsPosition() {
        if (window.SeeksGraphDebug) console.log('on scroll...')
        if (!this.$refs.seeksRelationGraph) return
        const windowHeight = this.getWindowHeight()
        var _box_info = this.$refs.seeksRelationGraph.getBoundingClientRect()
        if (window.SeeksGraphDebug) console.log('syncToolsPosition...')
        // console.log('change layout:', __top, this.$refs.seeksRelationGraph.offsetHeight)
        // console.log(_box_info.top, this.viewOffset.positionTop, (this.viewOffset.windowHeight - this.viewOffset.top))
        var __top = _box_info.top
        if (this.isNeedFixedTools === false) {
          if ((__top + this.$refs.seeksRelationGraph.offsetHeight) < 0) {
            this.isNeedFixedTools = false
          } else {
            if (__top < 0) {
              this.isNeedFixedTools = true
            }
          }
        } else {
          if (__top > 0) {
            this.isNeedFixedTools = false
          }
          if ((__top + this.$refs.seeksRelationGraph.offsetHeight) < 0) {
            this.isNeedFixedTools = false
          }
        }
        if ((__top + this.$refs.seeksRelationGraph.offsetHeight) > windowHeight) {
          this.isNeedFixedTools4Bottom = true
        } else {
          this.isNeedFixedTools4Bottom = false
        }
      },
      getWindowWidth() {
        return window.innerWidth ? window.innerWidth : document.documentElement.clientWidth ? document.documentElement.clientWidth : screen.width
      },
      getWindowHeight() {
        return window.innerHeight ? window.innerHeight : document.documentElement.clientHeight ? document.documentElement.clientHeight : screen.height
      },
      getNodePositionTop(node) {
        if (!node.offsetTop) return 0
        return node.offsetTop + (node.parentNode ? this.getNodePositionTop(node.parentNode) : 0)
      },
      getNodePositionLeft(node) {
        if (!node.offsetLeft) return 0
        return node.offsetLeft + (node.parentNode ? this.getNodePositionLeft(node.parentNode) : 0)
      },
      resetViewSize() {
        this.graphSetting.viewSize.width = this.$refs.seeksRelationGraph.getBoundingClientRect().width
        this.graphSetting.viewSize.height = this.$refs.seeksRelationGraph.getBoundingClientRect().height
        this.graphSetting.canvasZoom = 100
        this.SeeksRGStore.resetViewSize()
        this.refreshNVAnalysisInfo()
      },
      loadNodes(_nodes) {
        _nodes.forEach(thisNodeJson => {
          let thisNode = SeeksRGUtils.json2Node(thisNodeJson)
          let __isNew = false
          if (this.graphData.nodes_map[thisNode.id]) {
            thisNode = this.graphData.nodes_map[thisNode.id]
          } else {
            __isNew = true
          }
          if (__isNew) {
            this.graphData.nodes_map[thisNode.id] = thisNode
            this.graphData.nodes.push(thisNode)
            thisNode.seeks_id = this.seeksNodeIdIndex++
            thisNode.appended = false
          }
        })
      },
      loadLinks(_links) {
        _links.forEach(thisLinkJson => {
          let __isNew = false
          var __from
          var __to
          if (typeof thisLinkJson.from === 'object') {
            __from = thisLinkJson.from
          } else {
            __from = this.graphData.nodes_map[thisLinkJson.from]
          }
          if (typeof thisLinkJson.to === 'object') {
            __to = thisLinkJson.to
          } else {
            __to = this.graphData.nodes_map[thisLinkJson.to]
          }
          if (!__from) {
            console.error('找不到from:', thisLinkJson)
            return
          }
          if (!__to) {
            console.error('找不到to:', thisLinkJson)
            return
          }
          // console.log('[add link]', __from.text, __to.text, __from.seeks_id, __to.seeks_id, thisLink)
          const lineId1 = __from.seeks_id + '-' + __to.seeks_id
          const lineId2 = __to.seeks_id + '-' + __from.seeks_id
          var thisLink = SeeksRGUtils.json2Link(thisLinkJson)
          var thisLine
          var thisLinkIsReserve = false
          if (this.graphData.lines_map[lineId1]) {
            thisLine = this.graphData.lines_map[lineId1]
          } else {
            if (this.graphData.lines_map[lineId2]) {
              thisLine = this.graphData.lines_map[lineId2]
              thisLinkIsReserve = true
            } else {
              __isNew = true
              thisLine = {
                seeks_id: lineId1,
                fromNode: __from,
                toNode: __to,
                appended: false,
                relations: []
              }
            }
          }
          // console.log('new Line Color:', thisLine.color, thisLine.arrow)
          var _arrow = thisLink.arrow
          if (thisLink.isHideArrow) {
            // do nothing
          } else {
            _arrow = this.getLineArrow(thisLink.color)
          }
          if (!__from.targetNodes)__from.targetNodes = []
          if (!__to.targetNodes)__to.targetNodes = []
          if (__from.targetNodes.indexOf(__to) === -1) {
            __from.targetNodes.push(__to)
          }
          if (__to.targetNodes.indexOf(__from) === -1) {
            __to.targetNodes.push(__from)
          }
          if (__from.targetTo.indexOf(__to) === -1) {
            __from.targetTo.push(__to)
          }
          if (__to.targetFrom.indexOf(__from) === -1) {
            __to.targetFrom.push(__from)
          }
          var isDuplicate = false
          for (var i = 0; i < thisLine.relations.length; i++) {
            if (thisLine.relations[i].id === thisLink.id) {
              isDuplicate = true
              break
            }
          }
          if (isDuplicate === false) {
            if (!thisLink.id) thisLink.id = thisLine.seeks_id + '-' + thisLine.relations.length
            thisLink.isReverse = thisLinkIsReserve
            thisLink.arrow = _arrow
            thisLink.textPositon = { x: 0, y: 0 }
            thisLine.relations.push(thisLink)
          }
          // console.log('addLine:', thisLine)
          if (__isNew) {
            this.graphData.lines.push(thisLine)
            this.graphData.lines_map[lineId1] = thisLine
            thisLine.appended = false
          }
        })
      },
      getLineArrow(_color) {
        if (_color) {
          var thisColorId = SeeksRGUtils.getColorId(_color)
          if (this.allLineColors.map(thisColorObj => {
            return thisColorObj.id
          }).indexOf(thisColorId) === -1) {
            this.allLineColors.push({ id: thisColorId, color: _color })
          }
          return this.graphSetting.instanceId + '-arrow-' + thisColorId
        } else {
          return this.graphSetting.instanceId + '-arrow-default'
        }
      },
      flatNodeData(orign_nodes, parentNode, nodes_collect, links_collect) {
        orign_nodes.forEach(thisOrignNode => {
          if (!thisOrignNode.flated) {
            thisOrignNode.flated = true
            nodes_collect.push(thisOrignNode)
            if (parentNode) {
              links_collect.push({
                from: parentNode.id,
                to: thisOrignNode.id,
              })
            }
            var _childs = thisOrignNode.childs || thisOrignNode.children
            if (_childs && _childs.length > 0) {
              this.flatNodeData(_childs, thisOrignNode, nodes_collect, links_collect)
            }
          }
        })
      },
      loadGraphJsonData(jsonData) {
        // 兼容以前的配置
        if (!jsonData.links) jsonData.links = jsonData.lines
        if (!jsonData.links) jsonData.links = jsonData.relations
        var _orign_nodes = jsonData.nodes
        var _nodes = []
        var _links = []
        this.flatNodeData(_orign_nodes, null, _nodes, _links)
        jsonData.links = _links.concat(jsonData.links)
        this.loadNodes(_nodes)
        if (window.SeeksGraphDebug) console.log('节点预处理完毕')
        this.loadLinks(jsonData.links)
      },
      setJsonData(jsonData, callback) {
        this.viewSizeIsInited = true
        this.nodeViewList = []
        this.lineViewList = []
        this.graphData.nodes = []
        this.graphData.lines = []
        this.graphData.nodes_map = {}
        this.graphData.lines_map = {}
        this.graphData.rootNode = null
        console.log('set jsonData::', jsonData)
        this.resetViewSize()
        if (this.graphSetting.layouts && this.graphSetting.layouts.length > 0) {
          var _defaultLayoutSetting = this.graphSetting.layouts[0]
          if (window.SeeksGraphDebug) console.log('创建默认布局器：', this.graphSetting.layoutName)
          if (_defaultLayoutSetting.layouter) {
            this.graphSetting.layouter = _defaultLayoutSetting.layouter
          } else {
            this.graphSetting.layouter = SeeksRGLayouters.createLayout(_defaultLayoutSetting, this.graphSetting)
          }
        } else {
          console.log('你需要设置layouts来指定当前图谱可以使用的布局器！')
        }
        var __root_id = jsonData['rootId']
        this.loadGraphJsonData(jsonData)
        // console.log('graphData:', this.graphData)
        if (__root_id) {
          this.graphData.rootNode = this.graphData.nodes_map[__root_id]
        }
        if (!this.graphData.rootNode && this.graphData.nodes.length > 0) {
          this.graphData.rootNode = this.graphData.nodes[0]
        }
        this.applyNewDataToCanvas()
        this.resetViewSize()
        this.doLayout()
        if (callback)callback(this)
      },
      applyNewDataToCanvas() {
        this.graphData.nodes.forEach(thisNode => {
          if (thisNode.appended === false) {
            thisNode.appended = true
            this.nodeViewList.push(thisNode)
          }
        })
        this.graphData.lines.forEach(thisLine => {
          if (thisLine.appended === false) {
            thisLine.appended = true
            this.lineViewList.push(thisLine)
          }
        })
        if (this.graphData.rootNode) {
          if (this.graphSetting.defaultFocusRootNode) {
            this.graphSetting.checkedNodeId = this.graphData.rootNode.id
          }
        } else {
          throw Error('没有设置根节点[rootId]！也无法获取根节点!')
        }
      },
      appendJsonData(jsonData, isRelayout, callback) {
        if (arguments.length === 2 && typeof isRelayout === 'function') {
          callback = isRelayout
          isRelayout = true
        }
        console.log('appendData:', jsonData)
        this.loadGraphJsonData(jsonData)
        this.applyNewDataToCanvas()
        // this.resetViewSize()
        if (isRelayout) this.doLayout()
        if (callback) callback(this)
      },
      doLayout() {
        if (this.graphSetting.layouter && this.graphData.rootNode) {
          console.log('需要布局的节点数量：', this.graphData.nodes.length)
          this.graphSetting.layouter.placeNodes(this.graphData.nodes, this.graphData.rootNode, this.graphSetting)
        }
        document.body.addEventListener('mousemove', this.wow)
        // document.body.removeEventListener('mousemove', this.graphOnClick)
      },
      refresh() {
        this.resetViewSize()
        this.$nextTick(() => {
          this.graphSetting.layouter.refresh()
          this.refreshNVAnalysisInfo()
          document.body.addEventListener('mousemove', this.wow)
        })
      },
      onDragStart(e) {
        SeeksRGUtils.startDrag(e, this.graphSetting.canvasOffset, this.onDragEnd)
      },
      onDragEnd() {
        this.refreshNVAnalysisInfo()
      },
      addEventClick() {
        // window.addEventListener('click', this.graphOnClick)
      },
      // graphOnClick(evt) {
      //   console.log('click graph')
      // },
      wow() {
        if (window.SeeksGraphDebug) console.log('wow.....')
        this.graphSetting.canvasOffset.x = this.graphSetting.canvasOffset.x + 1
        this.graphSetting.canvasOffset.y = this.graphSetting.canvasOffset.y + 1
        this.graphSetting.canvasOffset.x = this.graphSetting.canvasOffset.x - 1
        this.graphSetting.canvasOffset.y = this.graphSetting.canvasOffset.y - 1
        document.body.removeEventListener('mousemove', this.wow)
      },
      onRGNodeClick(nodeData, e) {
        if (this.onNodeClick) {
          this.onNodeClick(nodeData, e)
        }
        // for (let i = 0; i < this.lineViewList.length; i++) {
        //   var thisLine = this.lineViewList[i]
        //   if (thisLine.fromNode.id === nodeData.id || thisLine.toNode.id === nodeData.id) {
        //     thisLine.flash = thisLine.flash + 1
        //   }
        // }
      },
      onRGLineClick(lineData, e) {
        if (this.onLineClick) {
          this.onLineClick(lineData, e)
        }
        // for (let i = 0; i < this.lineViewList.length; i++) {
        //   var thisLine = this.lineViewList[i]
        //   if (thisLine.fromNode.id === nodeData.id || thisLine.toNode.id === nodeData.id) {
        //     thisLine.flash = thisLine.flash + 1
        //   }
        // }
      },
      getNodeById(nodeId) {
        for (let i = 0; i < this.nodeViewList.length; i++) {
          if (this.nodeViewList[i].id === nodeId) {
            return this.nodeViewList[i]
          }
        }
      },
      removeNodeById(nodeId) {
        let __removed_lines = 0
        for (let i = 0; i < this.lineViewList.length; i++) {
          var thisLine = this.lineViewList[i]
          if (thisLine.fromNode.id === nodeId || thisLine.toNode.id === nodeId) {
            // console.log(this.lineViewList[i])
            thisLine.hidden = true
            this.lineViewList.splice(i, 1)
            i--
            __removed_lines++
            // console.log(this.lineViewList[i])
          }
        }
        console.log('删除对应的线个数：', nodeId, __removed_lines)
        let __removed_nodes = 0
        for (let i = 0; i < this.nodeViewList.length; i++) {
          if (this.nodeViewList[i].id === nodeId) {
            const thisNode = this.nodeViewList[i]
            thisNode.targetNodes.forEach(thisTNode => {
              const t_i = thisTNode.targetNodes.indexOf(thisNode)
              if (t_i !== -1) {
                thisTNode.targetNodes.splice(t_i, 1)
              }
            })
            // thisNode.isShow = false
            this.nodeViewList.splice(i, 1)
            delete this.graphData.nodes_map[thisNode.id]
            const d_i = this.graphData.nodes.findIndex(k => k.id === thisNode.id);
            this.graphData.nodes.splice(d_i, 1)
            __removed_nodes++
            break
          }
        }
        console.log('删除对应的节点个数：', nodeId, __removed_nodes)
      },
      dataURLToBlob(dataurl) { // ie 图片转格式
        var arr = dataurl.split(',')
        var mime = arr[0].match(/:(.*?);/)[1]
        var bstr = atob(arr[1])
        var n = bstr.length
        var u8arr = new Uint8Array(n)
        while (n--) {
          u8arr[n] = bstr.charCodeAt(n)
        }
        return new Blob([u8arr], { type: mime })
      },
      downloadAsImage(format) {
        if (this.beforeDownloadImage) {
          this.beforeDownloadImage()
        }
        // console.log('window.navigator.msSaveOrOpenBlob:', window.navigator.msSaveOrOpenBlob)
        // console.log('window.navigator.msSaveBlob：', window.navigator.msSaveBlob)
        // if (!!window.ActiveXObject || 'ActiveXObject' in window) {
        //   this.$message('无法生成并下载图片，请使用非IE浏览器!')
        //   return
        // }
        if (this.nodeViewList.length === 0) {
          throw Error('没有节点，没有内容需要导出！')
        }
        if (!format)format = 'png'
        this.graphSetting.checkedNodeId = ''
        this.graphSetting.canvasZoom = 100
        const exportDom = this.$refs.seeksRGCanvas
        var orign_width = exportDom.clientWidth // 获取dom 宽度
        var orign_height = exportDom.clientHeight // 获取dom 高度
        var _min_x = 999999
        var _min_y = 999999
        var _max_x = 0
        var _max_y = 0
        var _padding = 100
        this.nodeViewList.forEach(thisNode => {
          if (thisNode.x < _min_x) {
            _min_x = thisNode.x
          }
          if (thisNode.x > _max_x) {
            _max_x = thisNode.x + thisNode.el.offsetWidth
          }
          if (thisNode.y < _min_y) {
            _min_y = thisNode.y
          }
          if (thisNode.y > _max_y) {
            _max_y = thisNode.y + thisNode.el.offsetHeight
          }
        })
        this.nodeViewList.forEach(thisNode => {
          thisNode.x = thisNode.x - _min_x + _padding
          thisNode.y = thisNode.y - _min_y + _padding
        })
        var _origin_offset_x = this.graphSetting.canvasOffset.x + _min_x - _padding
        var _origin_offset_y = this.graphSetting.canvasOffset.y + _min_y - _padding
        this.graphSetting.canvasOffset.x = _padding * -1
        this.graphSetting.canvasOffset.y = _padding * -1
        console.log('offset:', {_origin_offset_x, _origin_offset_y, _min_x, _min_y, _max_x, _max_y})

        var _image_width = _max_x - _min_x + 200 + _padding * 2
        var _image_height = _max_y - _min_y + 100 + _padding * 2
        var pixelRatio = window.devicePixelRatio // 定义任意放大倍数 支持小数
        this.graphSetting.canvasSize.width = _image_width * pixelRatio
        this.graphSetting.canvasSize.height = _image_height * pixelRatio
        this.canvasBackgroundColor = '#ffffff'

        var relationGraphPosition = {
          left: this.$refs.seeksRelationGraph.offsetLeft - exportDom.getBoundingClientRect().left,
          top: this.$refs.seeksRelationGraph.offsetTop - exportDom.getBoundingClientRect().top,
          canvas_offsetLeft: exportDom.offsetLeft,
          canvas_offsetTop: exportDom.offsetTop,
          canvas_left: exportDom.getBoundingClientRect().left,
          canvas_top: exportDom.getBoundingClientRect().top,
        }
        // exportDom.style.position ='absolute'
        // exportDom.style.left ='0px'
        // exportDom.style.top ='0px'
        // exportDom.style.zIndex ='999'
        window.scrollTo(0,0);
        console.log('export image:', { relationGraphPosition, orign_width, orign_height, _image_width, _image_height, _min_x, _min_y, _max_x, _max_y, devicePixelRatio: window.devicePixelRatio })
        this.$nextTick(() => {
          var fileName = 'SeeksRelationGraph-' + (Math.random() * 100000).toFixed(0)
          var canvas = document.createElement('canvas') // 创建一个canvas节点
          canvas.width = _image_width * pixelRatio // 定义canvas 宽度 * 缩放
          canvas.height = _image_height * pixelRatio // 定义canvas高度 *缩放
          canvas.style.width = _image_width * pixelRatio + 'px'
          canvas.style.height = _image_height * pixelRatio + 'px'
          canvas.getContext('2d').scale(1, 1) // 获取context,设置scale
          const opts = {
            backgroundColor: null,
            scale: pixelRatio, // 添加的scale 参数
            canvas: canvas, // 自定义 canvas
            logging: true, // 日志开关，便于查看html2canvas的内部执行流程
            // windowWidth: _image_width,
            // windowHeight: _image_height,
            width: _image_width, // dom 原始宽度
            height: _image_height,
            // x: relationGraphPosition.left,
            // y: relationGraphPosition.top,
            useCORS: true // 【重要】开启跨域配置
          }
          console.log('html2canvas:', opts)
          html2canvas(exportDom, opts).then(canvas => {
            const dom = document.body.appendChild(canvas)
            // console.log('canvas:', fileName, dom)
            dom.style.display = 'none'
            const blob = this.dataURLToBlob(dom.toDataURL('image/' + format))
            document.body.removeChild(dom)
            const a = document.createElement('a')
            a.style.display = 'none'
            try {
              if (window.navigator.msSaveOrOpenBlob) {
                window.navigator.msSaveOrOpenBlob(blob, fileName + '.' + format)
                // console.log('this is IE');
                // var URL=window.URL;
                // var BlobBuilder = window.MSBlobBuilder;
                // navigator.saveBlob=navigator.msSaveBlob;
                // var imgBlob = canvas.msToBlob();
                // if (BlobBuilder && navigator.saveBlob) {
                //   var showSave =  function (data, name, mimetype) {
                //     var builder = new BlobBuilder();
                //     builder.append(data);
                //     var blob = builder.getBlob(mimetype||"application/octet-stream");
                //     if (!name)
                //       name = "Download.bin";
                //     navigator.saveBlob(blob, name);
                //   };
                //   showSave(imgBlob, 'barchart.png',"image/png");
                // }
              } else {
                a.setAttribute('href', URL.createObjectURL(blob))
                a.setAttribute('download', fileName + '.' + format)
                document.body.appendChild(a)
                // console.log('click to download:', opts)
                a.click()
                console.log('click ok!')
                URL.revokeObjectURL(blob)
                console.log('revokeObjectURL ok!')
                document.body.removeChild(a)
                console.log('removeChild ok!')
              }
              this.graphSetting.canvasSize.width = orign_width
              this.graphSetting.canvasSize.height = orign_height
              this.graphSetting.canvasOffset.x = _origin_offset_x
              this.graphSetting.canvasOffset.y = _origin_offset_y
              this.canvasBackgroundColor = 'transparent'
            } catch (e) {
              console.log('[SEEKS Graph]Create and download image error:', e)
            }
          })
        })
      },
      querySearchAsync(queryString, callback) {
        console.log('fetch-suggestions', queryString)
        queryString = queryString.trim()
        if (queryString === '') {
          return
        }
        var rst = []
        this.nodeViewList.forEach(thisNode => {
          console.log('fetch:', thisNode.text)
          if (thisNode.text.indexOf(queryString) !== -1) {
            rst.push(thisNode)
          }
        })
        console.log('fetched:', rst.length)
        callback(rst)
      },
      focusRootNode() {
        if (window.SeeksGraphDebug) console.log('relation-graph:focusRootNode')
        this.handleSelect(this.graphData.rootNode)
      },
      focusNodeById(nodeId) {
        this.graphData.nodes.forEach(thisNode => {
          if (thisNode.id === nodeId) {
            this.handleSelect(thisNode)
          }
        })
      },
      handleSelect(thisNode) {
        console.log('checked:', thisNode)
        scrollTo({
          top: this.$refs.seeksRelationGraph.offsetTop
        })
        this.animateToZoom(100, 300, () => {
          var _n_width = thisNode.width || 50
          var _n_height = thisNode.height || 50
          var _final_x = thisNode.x * -1 + this.graphSetting.viewSize.width / 2 - _n_width / 2
          var _final_y = thisNode.y * -1 + this.graphSetting.viewSize.height / 2 - _n_height / 2
          this.animateGoto(_final_x, _final_y, 500, () => {
            this.graphSetting.checkedNodeId = thisNode.id
            this.refreshNVAnalysisInfo()
          })
        })
      },
      animateGoto(x, y, time, callback) {
        var _distance_x = x - this.graphSetting.canvasOffset.x
        var _distance_y = y - this.graphSetting.canvasOffset.y
        var _allTime = time
        var _allStepNum = 5
        var _speed_x = parseInt(_distance_x / _allStepNum)
        var _speed_y = parseInt(_distance_y / _allStepNum)
        var _perDelay = _allTime / _allStepNum
        this.animateStepAction(0, _perDelay, _allStepNum, () => {
          this.graphSetting.canvasOffset.x += _speed_x
          this.graphSetting.canvasOffset.y += _speed_y
        }, () => {
          // console.log('分解完毕....')
          callback()
        })
      },
      animateToZoom(finalZoom, time, callback) {
        var _zoom_distance = finalZoom - this.graphSetting.canvasZoom
        var _allTime = time
        var _allStepNum = 5
        var _speed = parseInt(_zoom_distance / _allStepNum)
        var _perDelay = _allTime / _allStepNum
        this.animateStepAction(0, _perDelay, _allStepNum, () => {
          this.zoom(_speed)
        }, () => {
          // console.log('分解完毕....')
          callback()
        })
      },
      animateStepAction(stepIndex, delay, allStepNum, stepCallback, finalCallback) {
        // console.log(Date.now() + '步骤[' + stepIndex + ']')
        if (stepIndex < allStepNum) {
          stepCallback(stepIndex, allStepNum)
          setTimeout(() => {
            this.animateStepAction(stepIndex + 1, delay, allStepNum, stepCallback, finalCallback)
          }, delay)
        } else {
          finalCallback()
        }
      },
      getNodes() {
        return this.nodeViewList
      },
      getLines() {
        return this.lineViewList
      },
      onNodeExpandEvent(node, e) {
        console.log('onNodeExpand:', node)
        if (this.onNodeExpand) {
          this.onNodeExpand(node, e)
        }
      },
      onNodeCollapseEvent(node, e) {
        console.log('onNodeCollapse:', node)
        if (this.onNodeCollapse) {
          this.onNodeCollapse(node, e)
        }
      },
      getGraphJsonData() {
        var _nodes = []
        var _links = []
        this.graphData.nodes.forEach(thisNode => {
          SeeksRGUtils.transNodeToJson(thisNode, _nodes)
        })
        this.graphData.lines.forEach(thisLine => {
          SeeksRGUtils.transLineToJson(thisLine, _links)
        })
        return {
          rootId: this.graphData.rootNode ? this.graphData.rootNode.id : '',
          nodes: _nodes,
          links: _links
        }
      },
      getGraphJsonOptions() {
        return this.SeeksRGStore.getOptions()
      },
      printGraphJsonData() {
        console.log('graph options:', JSON.stringify(this.getGraphJsonOptions()))
        console.log('graph json data:', JSON.stringify(this.getGraphJsonData()))
      }
    }
  }
</script>
<style scoped>
  .rel-map{
    background-color: #ffffff;
    /*background-image: url("/static/images/graph-bg.png");*/
    overflow:hidden;
    cursor: default;
    user-select: none;
  }
  .rel-map-background-norepeat{
    background-repeat: no-repeat;
    background-position:right bottom;
  }
  .rel-nodediv-for-webkit{
    position: absolute;
    width:100%;
    top:0px;
    left:0px;
    z-index: 1000
  }
  .rel-map-canvas{
    position:relative;
    top:0px;
    left:0px;
    /*overflow:hidden;*/
    border: 0px;
    z-index: 3;
    /*background-color: #efefef;*/
    /*border-top: #efefef dashed 1px;*/
    /*border-left: #efefef dashed 1px;*/
    /*width:30px;*/
    /*height:30px;*/
  }
  svg {
    height: 100%;
    width: 100%;
  }
  .rel-linediv{
    --stroke:url('#lineStyle');
    --markerEnd:url('#arrow-default');
    --markerEndChecked:url('#arrow-checked');
  }
  .rel-linediv svg{
    overflow: visible
  }
  .rel-linediv /deep/ .c-rg-line-checked {
    /*stroke: var(--stroke);*/
    /*marker-end: var(--markerEndChecked) !important;*/
    stroke-width: 2px;
    stroke-dasharray: 5,5,5;
    stroke-dashoffset: 3px;
    stroke-linecap: butt;
    /*stroke: #FD8B37;*/
    stroke-linejoin: bevel;
    /* firefox bug fix - won't rotate at 90deg angles */
    -moz-transform: rotate(-89deg) translateX(-190px);
    animation-timing-function:linear;
    animation: ACTRGLineChecked 10s;
  }
  .rel-map /deep/ img{
    -webkit-user-drag: none;
    -webkit-user-select: none;
  }
</style>
