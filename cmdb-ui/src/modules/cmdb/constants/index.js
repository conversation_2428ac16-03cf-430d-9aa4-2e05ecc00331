export const LoadValueTypes = {
  INT: '0',
  FLOAT: '1',
  TEXT: '2',
  LIST: '3'
}

export const LoadValueTypeOptions = [
  { label: 'INT', value: LoadValueTypes.INT },
  { label: 'FLOAT', value: LoadValueTypes.FLOAT },
  { label: 'TEXT', value: LoadValueTypes.TEXT },
  { label: 'LIST', value: LoadValueTypes.LIST }
]

// 费用相关分组名称常量
export const COST_RELATED_GROUPS = ['计费', '费用', '成本', '价格', '资费']

// CI类型分组常量
export const CI_TYPE_GROUPS = {
  BASE: '基础',
  BILLING: '计费',
  CONFIG: '配置'
}

// 需要提取的CI类型分组列表
export const IMPORTANT_CI_TYPE_GROUPS = [CI_TYPE_GROUPS.BASE, CI_TYPE_GROUPS.BILLING, CI_TYPE_GROUPS.CONFIG]

// 其他CMDB相关的常量也可以放在这里
export const PeriodTypes = {
  DAILY: '0',
  MONTHLY: '1'
}

export const PeriodTypeOptions = [
  { label: '按天', value: PeriodTypes.DAILY },
  { label: '按月', value: PeriodTypes.MONTHLY }
]

// 添加历史记录状态枚举
export const HistoryStatusEnum = {
  BEGINSYNC: '0',
  PENDING: '1',
  PROCESSING: '2',
  COMPLETED: '3',
  FAILED: '4'
}

export const HistoryStatusOptions = [
  { label: 'beginSync', value: HistoryStatusEnum.BEGINSYNC },
  { label: 'pending', value: HistoryStatusEnum.PENDING },
  { label: 'processing', value: HistoryStatusEnum.PROCESSING },
  { label: 'completed', value: HistoryStatusEnum.COMPLETED },
  { label: 'failed', value: HistoryStatusEnum.FAILED }
]

// 时间段相关常量
export const TimeSlotEnum = {
  IDLE_TIME: '00', // 闲时 00:00-08:00
  BUSY_TIME: '08', // 忙时 08:00-18:00
  DEFAULT_SLOT: '08' // 默认为忙时
}

export const TimeSlotOptions = [
  { label: '闲时(00:00-08:00)', value: TimeSlotEnum.IDLE_TIME },
  { label: '忙时(08:00-18:00)', value: TimeSlotEnum.BUSY_TIME }
]
