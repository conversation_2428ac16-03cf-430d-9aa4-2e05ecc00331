import i18n from '@/lang'

export const valueTypeMap = () => {
  return {
    '0': i18n.t('cmdb.ciType.int'),
    '1': i18n.t('cmdb.ciType.float'),
    '2': i18n.t('cmdb.ciType.shortText'),
    '3': i18n.t('cmdb.ciType.datetime'),
    '4': i18n.t('cmdb.ciType.date'),
    '5': i18n.t('cmdb.ciType.time'),
    '6': 'JSON',
    '7': i18n.t('cmdb.ciType.password'),
    '8': i18n.t('cmdb.ciType.link'),
    '9': i18n.t('cmdb.ciType.longText'),
    '10': i18n.t('cmdb.ciType.bool'),
    '11': i18n.t('cmdb.ciType.reference'),
  }
}

// 与后端 ValueTypeEnum 对应的常量
export const ValueTypeEnum = {
  INT: '0',
  FLOAT: '1',
  TEXT: '2',
  DATETIME: '3',
  DATE: '4',
  TIME: '5',
  JSON: '6',
  PASSWORD: '7',
  LINK: '8',
  LONGTEXT: '9',
  BOOL: '10',
  REFERENCE: '11'
}

// 与后端 AggregationTypeEnum 对应的常量
export const AggregationTypeEnum = {
  COUNT: 'count',
  SUM: 'sum',
  AVG: 'avg',
  MAX: 'max',
  MIN: 'min'
}

// 可聚合的属性类型
export const AGGREGATABLE_VALUE_TYPES = [
  ValueTypeEnum.INT,
  ValueTypeEnum.FLOAT
]

// 判断属性是否可聚合
export const isAggregatableType = (valueType) => {
  return AGGREGATABLE_VALUE_TYPES.includes(valueType)
}

export const defautValueColor = [
  { value: '#d9d9d9' },
  { value: '#ffccc7' },
  { value: '#ffd8bf' },
  { value: '#ffe7ba' },
  { value: '#fff1b8' },
  { value: '#f4ffb8' },
  { value: '#d9f7be' },
  { value: '#b5f5ec' },
  { value: '#bae7ff' },
  { value: '#d6e4ff' },
  { value: '#efdbff' },
  { value: '#ffd6e7' },
]

export const defaultBGColors = ['#ffccc7', '#ffd8bf', '#ffe7ba', '#fff1b8', '#d9f7be', '#b5f5ec', '#bae7ff', '#d6e4ff', '#efdbff', '#ffd6e7']

export const CI_DEFAULT_ATTR = {
  UPDATE_USER: '_updated_by',
  UPDATE_TIME: '_updated_at'
}
