export function validateIndispensableFields(upLoadData, requiredFields, statusType = 'recycled') {
  const missingFields = new Set()
  for (const item of upLoadData) {
    const itemFields = Object.keys(item)

    if (itemFields.length === requiredFields.length &&
        itemFields.every(field => requiredFields.includes(field))) {
      throw new Error('上传的数据中只包含必填字段，请确保包含其他业务数据。')
    }

    if (item['状态']) {
      // 根据 statusType 参数检查状态值
      const validStatus = statusType === 'recycled' ? '已回收' : '运行中'
      if (item['状态'] !== validStatus) {
        throw new Error(`状态字段只允许填写"${validStatus}"`)
      }

      // 仅在 statusType 为 recycled 时，检查是否只包含必填字段
      if (statusType === 'recycled') {
        const hasNonRequiredFields = itemFields.some(field =>
          !requiredFields.includes(field) && field !== '状态'
        )
        if (hasNonRequiredFields) {
          throw new Error('当"状态"字段为"已回收"时，只允许包含必填字段，请删除其他业务字段。')
        }
      }
    }

    // 无论"状态"是否有值，都需要检查必填字段
    for (const field of requiredFields) {
      if (!(field in item)) {
        missingFields.add(field)
      }
    }
  }
  if (missingFields.size > 0) {
    const errorMessage = `上传的数据中缺少以下字段：${[...missingFields].join(', ')}。`
    throw new Error(errorMessage)
  }
}
