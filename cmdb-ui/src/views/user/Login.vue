<template>

  <div class="ops-login" >

    <div class="ops-login-left" v-once>
      <!-- 添加动态背景效果 -->
      <div class="background-overlay"></div>

      <!-- 优化引言展示 -->
      <div class="quote-container">
        <div class="quote-content">
          <div class="quote-text">
            <div class="text-wrapper">
              <span class="text-line first">我快得像风一样</span>
            </div>
            <div class="text-wrapper">
              <span class="text-line second">从那以后，我想去什么地方</span>
            </div>
            <div class="text-wrapper">
              <span class="text-line third">我就跑着去</span>
            </div>
          </div>
          <div class="quote-author">——阿甘</div>
        </div>
      </div>

      <!-- 添加装饰元素 -->
      <div class="decorative-elements">
        <div class="glow-effect"></div>
      </div>
    </div>
    <div class="ops-login-right">
      <div class="login-container">
        <div class="login-header">
          <img src="../../assets/OMP4.png" alt="logo" class="login-logo" />
          <!-- <div class="login-title">O M P</div> -->
        </div>
        <div class="login-divider">
          <span class="divider-text">账号登录</span>
        </div>

        <a-form
          id="formLogin"
          ref="formLogin"
          :form="form"
          @submit="handleSubmit"
          hideRequiredMark
          :colon="false"
          :class="{ shake: shake }"
          class="login-form">
          <a-form-item label=" ">
            <a-input
              size="large"
              type="text"
              class="ops-input"
              placeholder="请输入用户名或邮箱"
              v-decorator="[
                'username',
                {
                  rules: [{ required: true, message: '请输入用户名或邮箱' }, { validator: handleUsernameOrEmail }],
                  validateTrigger: 'change',
                },
              ]"
            >
              <template #prefix>
                <ops-icon
                  type="monitor-director"
                />
              </template>
            </a-input>
          </a-form-item>
          <a-form-item label="">
            <div class="password-input-wrapper">
              <a-input
                size="large"
                :type="passwordVisible ? 'text' : 'password'"
                autocomplete="false"
                placeholder="请输入密码"
                class="ops-input"
                v-decorator="['password', { rules: [{ required: true, message: '请输入密码' }], validateTrigger: 'blur' }]"
              >
                <template #prefix>
                  <ops-icon type="duose-password" />
                </template>
                <template #suffix>
                  <a-icon
                    :type="passwordVisible ? 'eye' : 'eye-invisible'"
                    class="password-eye"
                    @mousedown.prevent="showPassword"
                    @mouseup.prevent="hidePassword"
                    @mouseleave="hidePassword"
                  />
                </template>
              </a-input>
            </div>
          </a-form-item>

          <a-form-item v-if="enable_list.some(item => item.auth_type === 'OTP')">
            <a-input
              size="large"
              type="password"
              autocomplete="false"
              placeholder="请输入二次认证密码"
              class="ops-input"
              v-decorator="[
                'otp',
                { rules: [{ required: enable_list.some(item => item.auth_type === 'OTP'), message: '请输入二次认证密码' }], validateTrigger: 'blur' },
              ]"
            >
              <template #prefix>
                <ops-icon
                  type="cmdb-count"
                />
              </template>
            </a-input>
          </a-form-item>

          <a-form-item style="margin-top: 24px">
            <a-button
              size="large"
              type="primary"
              htmlType="submit"
              class="login-button"
              :loading="state.loginBtn"
              :disabled="state.loginBtn"
            >
              <span class="button-text">登 录</span>
              <span class="button-hover"></span>
            </a-button>

            <div class="auth-options">
              <a-checkbox
                v-if="enable_list.some(item => item.auth_type === 'LDAP')"
                v-model="auth_with_ldap"
                class="ldap-checkbox"
              >
                <span class="checkbox-text">LDAP</span>
              </a-checkbox>

              <a
                v-if="enable_list.some(item => item.auth_type === 'OTP')"
                href="/user/register-otp"
                class="otp-link"
              >
                获取 OTP
              </a>
            </div>
          </a-form-item>
        </a-form>
        <div class="version-info">
          <div class="version-content">
            <span class="version-item">数广云运营平台</span>
            <span class="dot">·</span>
            <span class="version-item">云平台运营组</span>
            <span class="dot">·</span>
            <span class="version-number">V1.4.5</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import { timeFix, welcome } from '@/utils/util'
import { ops_move_icon as OpsMoveIcon } from '@/core/icons'
import _ from 'lodash'
import { encryptPassword } from '@/utils/user'

export default {
  name: 'Login',
  components: { OpsMoveIcon },
  data() {
    return {
      customActiveKey: 'tab1',
      loginBtn: false,
      // login type: 0 email, 1 username, 2 telephone
      loginType: 0,
      requiredTwoStepCaptcha: false,
      stepCaptchaVisible: false,
      form: this.$form.createForm(this),
      state: {
        time: 60,
        loginBtn: false,
        // login type: 0 email, 1 username, 2 telephone
        loginType: 0,
        smsSendBtn: false,
      },
      auth_with_ldap: false,
      auth_with_otp: false,
      enable_list: [], // 初始化 enable_list 为一个空数组
      formRules: {
        username: [
          { required: true, message: '请输入用户名或邮箱' },
          { validator: this.handleUsernameOrEmail }
        ],
        password: [
          { required: true, message: '请输入密码' },
          { min: 6, message: '密码长度不能小于6位' }
        ],
        otp: [
          { required: true, message: '请输入二次认证密码' }
        ]
      },
      loading: {
        init: true,
        submit: false
      },
      shake: false,
      passwordVisible: false,
    }
  },
  computed: {
    ...mapState({ auth_enable: (state) => state?.user?.auth_enable ?? {} }),
    _enable_list() {
      return this.enable_list
    },
  },
  watch: {
    auth_enable: {
      immediate: true,
      handler(newVal) {
        this.enable_list = newVal.enable_list ?? []
        this.auth_with_ldap = this.enable_list.some(item => item.auth_type === 'LDAP')
        this.auth_with_otp = this.enable_list.some(item => item.auth_type === 'OTP')
        console.log('this.auth_with_ldap', this.auth_with_ldap)
      },
    },
  },
  created() {},
  async mounted() {
    await this.GetAuthDataEnable()
  },
  methods: {
    ...mapActions(['Login', 'GetAuthDataEnable', 'GetInfo']),
    // handler
    handleUsernameOrEmail(rule, value, callback) {
      const { state } = this
      const regex = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((\.[a-zA-Z0-9_-]{2,3}){1,2})$/
      if (regex.test(value)) {
        state.loginType = 0
      } else {
        state.loginType = 1
      }
      callback()
    },
    handleSubmit(e) {
      e.preventDefault()
      if (this.loading.submit) return
      this.loading.submit = true

      this.form.validateFields((err, values) => {
        if (err) {
          console.log('err', err)
          this.handleValidateError()
          return
        }

        // 加密密码
        const { password: encryptedPassword, timestamp } = encryptPassword(values.password)

        const loginParams = {
          username: values.username,
          password: encryptedPassword,
          timestamp: timestamp,
          otp: values.otp,
          auth_with_ldap: this.auth_with_ldap ? 1 : 0
        }

        // 保存用户名，用于欢迎消息
        const username = values.username

        this.Login({ userInfo: loginParams })
          .then(() => this.loginSuccess(username))
          .catch(error => {
            // 添加错误处理
            this.handleLoginError(error)
          })
          .finally(() => {
            this.loading.submit = false
          })
      })
    },
    // 添加登录按钮防抖
    debounceSubmit: _.debounce(function(e) {
      this.handleSubmit(e)
    }, 300),
    // 登录成功处理
    loginSuccess(username) {
      // 显示带用户名的欢迎信息
      this.$notification.success({
        message: '登录成功',
        description: welcome(username),
        duration: 3,
        class: 'custom-notification'
      })

      // 确保无论如何都会跳转
      setTimeout(() => {
        this.$router.push({
          path: this.$route.query?.redirect ?? '/',
          replace: true
        })
      }, 800)
    },
    // 处理表单验证错误
    handleValidateError() {
      this.shake = true
      this.$message.error('请填写完整的登录信息')
      setTimeout(() => {
        this.shake = false
        this.loading.submit = false
      }, 600)
    },
    // 处理登录错误
    handleLoginError(error) {
      this.shake = true
      const errorMsg = error?.response?.data?.message || '登录失败，请检查用户名和密码'
      this.$message.error(errorMsg)

      // 重置抖动状态
      setTimeout(() => {
        this.shake = false
        this.loading.submit = false
      }, 600)
    },
    showPassword() {
      this.passwordVisible = true
    },
    hidePassword() {
      this.passwordVisible = false
    },
  },
}
</script>

<style lang="less" scoped>
.ops-login {
  width: 100%;
  height: 100%;
  display: flex;
  min-width: 1000px;
  overflow-x: auto;
.footer-text {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  font-size: 14px;
  color: #888;
  padding: 10px;
  width: 100%;
  background: rgba(255, 255, 255, 0.9);
}
  .ops-login-left {
    position: relative;
    width: 50%;
    background: url('../../assets/20240914_233042.png') no-repeat;
    background-position: center;
    background-size: cover;
    overflow: hidden;

    .quote-container {
      position: absolute;
      top: 46%;
      left: 56%;
      transform: translate(-45%, -40%);
      width: 80%;
      z-index: 1;

      .quote-content {
        text-align: left;

        .quote-text {
          .text-wrapper {
            overflow: hidden;
            margin: 8px 0;

            .text-line {
              display: block;
              font-size: 1.6vw;
              color: white;
              text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
              transform: translateY(100%);
              opacity: 0;
              padding-left: 20px;

              &.first {
                animation: slideUp 0.8s ease-out 0.2s forwards;
              }

              &.second {
                animation: slideUp 0.8s ease-out 0.4s forwards;
              }

              &.third {
                animation: slideUp 0.8s ease-out 0.6s forwards;
              }
            }
          }
        }

        .quote-author {
          font-size: 1.2vw;
          color: rgba(255, 255, 255, 0.95);
          margin-top: 20px;
          padding-left: 20px;
          opacity: 0;
          transform: translateX(-20px);
          animation: slideInRight 0.6s ease-out 1s forwards;
          text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }
      }
    }

    .decorative-elements {
      position: absolute;
      inset: 0;
      pointer-events: none;

      .glow-effect {
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(
          circle at center,
          rgba(255, 255, 255, 0.05) 0%,
          transparent 70%
        );
        opacity: 0;
        animation: glowPulse 3s ease-out forwards;
      }
    }
  }
  .ops-login-right {
    width: 45%;
    position: relative;
    padding: 3% 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #f5f5f5;

    .login-container {
      width: 90%;
      max-width: 460px;
      background: white;
      border-radius: 8px;
      padding: 24px 32px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      .login-header {
        text-align: center;
        margin-bottom: 20px;
        .login-logo {
          width: 140px;
          height: auto;
          margin-bottom: 10px;
        }
        .login-title {
          font-size: 20px;
          margin-bottom: 4px;
        }
        .login-subtitle {
          font-size: 13px;
        }
      }
      .login-divider {
        position: relative;
        text-align: center;
        margin: 16px 0;
        &::before {
          content: '';
          position: absolute;
          top: 50%;
          left: 0;
          width: 100%;
          height: 1px;
          background: #e8e8e8;
        }
        .divider-text {
          position: relative;
          padding: 0 12px;
          background: white;
          color: #999;
          font-size: 13px;
        }
      }
      .login-form {
        .ant-form-item {
          margin-bottom: 14px;
        }
        .ops-input {
          height: 36px;
          border-radius: 4px;
          &:hover, &:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
          }
        }
        .login-button {
          width: 100%;
          height: 36px;
          position: relative;
          overflow: hidden;
          border: none;
          background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
          transition: all 0.3s ease;
          margin-top: 12px;

          .button-text {
            position: relative;
            z-index: 1;
            font-size: 15px;
            font-weight: 500;
            letter-spacing: 2px;
          }

          .button-hover {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 0;
          }

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);

            .button-hover {
              opacity: 1;
            }
          }

          &:active {
            transform: translateY(1px);
          }
        }
      }
      .login-extra {
        margin-top: 16px;
        padding-top: 10px;
        border-top: 1px solid #f0f0f0;
        .extra-links {
          display: flex;
          justify-content: space-between;
          a {
            color: #666;
            font-size: 13px;
            &:hover {
              color: #1890ff;
            }
          }
        }
      }

      .version-info {
        margin-top: 24px;
        padding-top: 16px;
        border-top: 1px solid #f0f0f0;

        .version-content {
          text-align: center;
          color: #999;
          font-size: 13px;

          .version-item {
            transition: color 0.3s;

            &:hover {
              color: #666;
            }
          }

          .dot {
            margin: 0 8px;
            color: #d9d9d9;
          }

          .version-number {
            color: #1890ff;
            font-weight: 500;
          }
        }
      }
    }
  }

  // 添加响应式支持
  @media screen and (max-width: 768px) {
    flex-direction: column;
    min-width: auto;
    .ops-login-left {
      width: 100%;
      height: 200px;
      > span {
        font-size: 16px;
        left: 50%;
      }
    }
    .ops-login-right {
      width: 100%;
      padding: 12px;
      .login-container {
        width: 100%;
        max-width: none;
        padding: 20px;
        margin: 0 12px;
        .login-header {
          .login-logo {
            width: 100px;
          }
          .login-title {
            font-size: 18px;
          }
        }
      }
    }
  }

  // 支持暗色主题
  &[data-theme='dark'] {
    background: #1f1f1f;
    .ops-login-right {
      background: #141414;
      .ops-input {
        background: #1f1f1f;
        border-color: #434343;
        &:hover {
          border-color: #177ddc;
        }
      }
      .login-button {
        background: #177ddc;
        border-color: #177ddc;
      }
    }
    .footer-text {
      color: #888;
      background: rgba(0, 0, 0, 0.8);
    }
  }
}
.otp-register-link {
  float: right;
  color: #1890ff;
  &:hover {
    color: #40a9ff;
    text-decoration: underline;
  }
}
.smallImage {
  width: 100px;
  height: auto;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shine {
  0% {
    left: -50%;
  }
  100% {
    left: 150%;
  }
}

// 添加抖动动画
.shake {
  animation: shake 0.6s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
}

@keyframes shake {
  10%, 90% {
    transform: translate3d(-1px, 0, 0);
  }
  20%, 80% {
    transform: translate3d(2px, 0, 0);
  }
  30%, 50%, 70% {
    transform: translate3d(-4px, 0, 0);
  }
  40%, 60% {
    transform: translate3d(4px, 0, 0);
  }
}

.login-container {
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.auth-options {
  margin-top: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .ldap-checkbox {
    .checkbox-text {
      color: #666;
      transition: color 0.3s;

      &:hover {
        color: #1890ff;
      }
    }
  }

  .otp-link {
    color: #1890ff;
    font-size: 13px;
    transition: all 0.3s;

    &:hover {
      color: #40a9ff;
      text-decoration: none;
      transform: translateX(2px);
    }
  }
}

.footer-wrapper {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  backdrop-filter: blur(5px);
  padding: 12px 0;
  z-index: 100;

  .footer-content {
    text-align: center;
    font-size: 14px;
    color: #666;

    .company, .team, .version {
      display: inline-block;
      transition: color 0.3s;

      &:hover {
        color: #1890ff;
      }
    }
    .divider {
      margin: 0 8px;
      color: #d9d9d9;
    }
  }
}

// 暗色主题支持
[data-theme='dark'] {
  .footer-wrapper {
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.6));
    .footer-content {
      color: #888;
      .divider {
        color: #434343;
      }
    }
  }
  .login-button {
    background: linear-gradient(135deg, #177ddc 0%, #135ca3 100%);
    .button-hover {
      background: linear-gradient(135deg, #1f90ff 0%, #177ddc 100%);
    }
  }
}

// 响应式适配
@media screen and (max-width: 768px) {
  .footer-wrapper {
    position: relative;
    margin-top: 40px;
    .footer-content {
      font-size: 12px;
      .divider {
        margin: 0 4px;
      }
    }
  }
}

.password-input-wrapper {
  position: relative;

  .password-eye {
    cursor: pointer;
    color: rgba(0, 0, 0, 0.45);
    font-size: 16px;
    transition: all 0.3s;

    &:hover {
      color: #1890ff;
    }

    &:active {
      transform: scale(0.95);
    }
  }
}

// 添加按下效果
.password-eye:active {
  .eye-icon {
    transform: scale(0.95);
  }
}

// 禁用文本选择
.password-eye {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

// 暗色主题适
[data-theme='dark'] {
  .password-eye {
    color: rgba(255, 255, 255, 0.45);

    &:hover {
      color: #177ddc;
    }
  }
}

// 调整表单项间距
.ant-form-item {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

// 添加新的动画
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideLine {
  from {
    transform: translateX(-100%) rotate(-45deg);
  }
  to {
    transform: translateX(100%) rotate(-45deg);
  }
}

@keyframes float {
  0%, 100% {
    transform: translate(0, 0);
  }
  50% {
    transform: translate(20px, 20px);
  }
}

// 响应式优化
@media screen and (max-width: 768px) {
  .ops-login-left {
    .quote-container {
      .quote-content {
        .quote-text {
          .text-wrapper {
            .text-line {
              font-size: 12px;
              text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);
            }
          }
        }
        .quote-author {
          font-size: 14px;
          text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);
        }
      }
    }
  }
}

// 暗色主题适配
[data-theme='dark'] {
  .ops-login-left {
    .background-overlay {
      background: linear-gradient(
        135deg,
        rgba(0, 0, 0, 0.8) 0%,
        rgba(0, 0, 0, 0.6) 100%
      );
    }
  }
}

// 新的动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes glowPulse {
  0% {
    opacity: 0;
    transform: scale(0.8) rotate(0deg);
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 0;
    transform: scale(1.2) rotate(360deg);
  }
}

// 响应式适配
@media screen and (max-width: 768px) {
  .ops-login-left {
    .quote-container {
      .quote-content {
        .quote-text {
          .text-wrapper {
            .text-line {
              font-size: 16px;
            }
          }
        }
        .quote-author {
          font-size: 14px;
        }
      }
    }
  }
}
</style>
