<template>
  <div class="main user-layout-register">
    <h3><span>OTP二维码生成(仅限首次登陆)</span></h3>
    <div class="ops-login-right">
      <a-form
        id="formLogin"
        ref="formLogin"
        :form="form"
        @submit="handleSubmit"
        hideRequiredMark
        :colon="false">
        <a-form-item label="用户名/邮箱">
          <a-input
            size="large"
            type="text"
            class="ops-input"
            v-decorator="[
              'username',
              {
                rules: [{ required: true, message: '请输入用户名或邮箱' }, { validator: handleUsernameOrEmail }],
                validateTrigger: 'change',
              },
            ]"
          >
          </a-input>
        </a-form-item>

        <a-form-item label="密码">
          <a-input
            size="large"
            type="password"
            autocomplete="false"
            class="ops-input"
            v-decorator="['password', { rules: [{ required: true, message: '请输入密码' }], validateTrigger: 'blur' }]"
          >
          </a-input>
        </a-form-item>
        <a-form-item style="margin-top: 24px">
          <a-button
            size="large"
            type="primary"
            htmlType="submit"
            class="login-button"
            :loading="state.loginBtn"
            :disabled="state.loginBtn"
          >登录</a-button
          >
          <a-checkbox
            v-if="enable_list.some(item => item.auth_type === 'LDAP')"
            v-model="auth_with_ldap"
          >LDAP</a-checkbox
          >
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import { timeFix } from '@/utils/util'
import { encryptPassword } from '@/utils/user'

export default {
  name: 'Login',
  data() {
    return {
      customActiveKey: 'tab1',
      loginBtn: false,
      // login type: 0 email, 1 username, 2 telephone
      loginType: 0,
      requiredTwoStepCaptcha: false,
      stepCaptchaVisible: false,
      form: this.$form.createForm(this),
      state: {
        time: 60,
        loginBtn: false,
        // login type: 0 email, 1 username, 2 telephone
        loginType: 0,
        smsSendBtn: false,
      },
      auth_with_ldap: false,
      auth_with_otp: false,
      enable_list: []
    }
  },
  computed: {
    ...mapState({ auth_enable: (state) => state?.user?.auth_enable ?? {} }),
    _enable_list() {
      return this.enable_list
    },
  },
  watch: {
    auth_enable: {
      immediate: true,
      handler(newVal) {
        this.enable_list = newVal.enable_list ?? []
        this.auth_with_ldap = this.enable_list.some(item => item.auth_type === 'LDAP')
        this.auth_with_otp = this.enable_list.some(item => item.auth_type === 'OTP')
        console.log('this.auth_with_ldap', this.auth_with_ldap)
      },
    },
  },
  created() {},
  async mounted() {
    await this.GetAuthDataEnable()
  },
  methods: {
    ...mapActions(['Login', 'GetAuthDataEnable', 'verifyOtp']),
    // handler
    handleUsernameOrEmail(rule, value, callback) {
      const { state } = this
      const regex = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((\.[a-zA-Z0-9_-]{2,3}){1,2})$/
      if (regex.test(value)) {
        state.loginType = 0
      } else {
        state.loginType = 1
      }
      callback()
    },
    handleSubmit(e) {
      e.preventDefault()
      const {
        form: { validateFields },
        state,
        customActiveKey,
        Login,
      } = this

      state.loginBtn = true

      const validateFieldsKey = customActiveKey === 'tab1' ? ['username', 'password'] : ['mobile', 'captcha']

      validateFields(validateFieldsKey, { force: true }, (err, values) => {
        if (!err) {
          const loginParams = { ...values }
          delete loginParams.username
          loginParams.username = values.username
          const { password, timestamp } = encryptPassword(values.password)
          loginParams.password = password
          loginParams.timestamp = timestamp
          loginParams.auth_with_ldap = this.auth_with_ldap
          localStorage.setItem('ops_auth_type', '')
          Login({ userInfo: loginParams })
            .then((res) => this.loginSuccess(res))
            .catch((error) => {
              const errorMsg = error.response?.data?.message || '用户名或密码错误'
              this.$message.error(errorMsg)
            })
            .finally(() => {
              state.loginBtn = false
            })
        } else {
          setTimeout(() => {
            state.loginBtn = false
          }, 600)
        }
      })
    },
    otherLogin(auth_type) {
      this.Login({ userInfo: {}, auth_type })
    },
    loginSuccess(res) {
      this.verifyOtp(true)
      this.$router.push('/user/register-otp-result')
      // 延迟 1 秒显示欢迎信息
      setTimeout(() => {
        this.$notification.success({
          message: '欢迎',
          description: `${timeFix()}，登录成功`,
        })
      }, 1000)
    },
  },
}
</script>

<style lang="less">
.user-register {
  &.error {
    color: #ff0000;
  }

  &.warning {
    color: #ff7e05;
  }

  &.success {
    color: #52c41a;
  }
}

.user-layout-register {
  .ant-input-group-addon:first-child {
    background-color: #fff;
  }
}

.login-button {
  width: 100%;
}

</style>
