 /* eslint-dsiable */
import Vue from 'vue'
import axios from 'axios'
import { VueAxios } from './axios'
import message from 'ant-design-vue/es/message'
import notification from 'ant-design-vue/es/notification'
import { ACCESS_TOKEN } from '@/store/global/mutation-types'
import router from '@/router'
import store from '@/store'

// 创建 axios 实例
const service = axios.create({
  baseURL: process.env.VUE_APP_API_BASE_URL, // api base_url
  timeout: 12000, // 请求超时时间
  withCredentials: true,
  crossDomain: true,
})

const err = (error) => {
  if (error.response) {
    const status = error.response.status
    const errorMsg = ((error.response || {}).data || {}).message || '服务端未知错误'

    switch (status) {
      case 401:
        // 对于登录页面的401错误，不拦截处理，让组件自己处理
        if (window.location.pathname === '/user/login' || window.location.pathname === '/user/register-otp') {
          return Promise.reject(error)
        }
        // 非登录页面的401，跳转到登出
        window.location.href = '/user/logout'
        break

      case 500:
      case 501:
      case 502:
      case 503:
      case 504:
        message.error(errorMsg || '服务端未知错误, 请联系管理员！')
        break

      case 412:
        let seconds = 5
        notification.warning({
          key: 'notification',
          message: 'WARNING',
          description:
            '修改已提交，请等待审核（5s）',
          duration: 5,
        })
        let interval = setInterval(() => {
          seconds -= 1
          if (seconds === 0) {
            clearInterval(interval)
            interval = null
            return
          }
          notification.warning({
            key: 'notification',
            message: 'WARNING',
            description:
              `修改已提交，请等待审核（${seconds}s）`,
            duration: seconds
          })
        }, 1000)
        break

      default:
        if (!error.config.isShowMessage) {
          message.error(errorMsg || '出现错误，请稍后再试')
        }
    }
  }
  return Promise.reject(error)
}

// request interceptor
service.interceptors.request.use(config => {
  const token = Vue.ls.get(ACCESS_TOKEN)
  if (token) {
    config.headers['Access-Token'] = token // 让每个请求携带自定义 token 请根据实际情况自行修改
  }
  config.headers['Accept-Language'] = store?.state?.locale ?? 'zh'
  return config
}, err)

// response interceptor
service.interceptors.response.use((response) => {
  return response.data
}, err)

const installer = {
  vm: {},
  install(Vue) {
    Vue.use(VueAxios, service)
  }
}

export {
  installer as VueAxios,
  service as axios
}
