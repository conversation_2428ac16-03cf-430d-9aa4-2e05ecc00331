{"name": "oneops", "version": "0.0.9", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "build:preview": "vue-cli-service build --mode preview", "lint": "vue-cli-service lint", "lint:nofix": "vue-cli-service lint --no-fix", "test:unit": "vue-cli-service test:unit"}, "dependencies": {"@antv/data-set": "^0.11.8", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.18.9", "@babel/plugin-syntax-import-meta": "^7.10.4", "@riophae/vue-treeselect": "^0.4.0", "@vue/composition-api": "^1.7.1", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^1.0.0", "ant-design-vue": "^1.6.5", "axios": "0.18.0", "babel-eslint": "^8.2.2", "butterfly-dag": "^4.3.26", "codemirror": "^5.65.13", "core-js": "^3.31.0", "echarts": "^5.3.2", "element-ui": "^2.15.10", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "html2canvas": "^1.0.0-rc.5", "is-buffer": "^2.0.5", "jquery": "^3.6.0", "js-cookie": "^2.2.0", "json2csv": "^4.5.2", "lodash.get": "^4.4.2", "lodash.pick": "^4.4.0", "md5": "^2.2.1", "moment": "^2.24.0", "nprogress": "^0.2.0", "relation-graph": "^2.1.42", "snabbdom": "^3.5.1", "sortablejs": "1.9.0", "style-resources-loader": "^1.5.0", "viser-vue": "^2.4.8", "vue": "2.6.11", "vue-cli-plugin-style-resources-loader": "^0.1.5", "vue-clipboard2": "^0.3.3", "vue-codemirror": "^4.0.6", "vue-cropper": "^0.6.2", "vue-grid-layout": "2.3.12", "vue-i18n": "8.28.2", "vue-infinite-scroll": "^2.0.2", "vue-json-editor": "^1.4.3", "vue-ls": "^3.2.1", "vue-router": "^3.1.2", "vue-svg-component-runtime": "^1.0.1", "vue-template-compiler": "2.6.11", "vuedraggable": "^2.23.0", "vuex": "^3.1.1", "vxe-table": "3.7.10", "vxe-table-plugin-export-xlsx": "2.0.0", "xe-utils": "3", "xlsx": "0.15.0", "xlsx-js-style": "^1.2.0", "crypto-js": "^4.2.0"}, "devDependencies": {"@ant-design/colors": "^3.2.2", "@babel/core": "^7.23.2", "@babel/polyfill": "^7.2.5", "@babel/preset-env": "^7.23.2", "@vue/cli-plugin-babel": "4.5.17", "@vue/cli-plugin-eslint": "^4.0.5", "@vue/cli-plugin-unit-jest": "^4.0.5", "@vue/cli-service": "^4.0.5", "@vue/eslint-config-standard": "^4.0.0", "@vue/test-utils": "^1.0.0-beta.30", "babel-jest": "^23.6.0", "babel-plugin-import": "^1.11.0", "babel-plugin-transform-remove-console": "^6.9.4", "eslint": "^5.8.0", "eslint-plugin-html": "^5.0.0", "eslint-plugin-vue": "^5.0.0", "less": "^3.8.1", "less-loader": "^4.1.0", "qrcode.vue": "1.7.0", "true-case-path": "^2.2.1", "vue-svg-icon-loader": "^2.1.1", "webpack-theme-color-replacer": "^1.2.17"}}