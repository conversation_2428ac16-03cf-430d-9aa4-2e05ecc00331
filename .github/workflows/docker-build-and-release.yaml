name: docker-images-build-and-release

on:
  push:
    branches:
      - master
    tags: ["v*"]
  pull_request:
    branches:
      - master

env:
  # Use docker.io for Docker Hub if empty
  REGISTRY_SERVER_ADDRESS: ghcr.io/veops
  TAG: ${{ github.sha }}

jobs:
  setup-environment:
    timeout-minutes: 30
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repo
        uses: actions/checkout@v4
  release-api-images:
    runs-on: ubuntu-latest
    needs: [setup-environment]
    permissions:
      contents: read
      packages: write
    timeout-minutes: 90
    steps:
      - name: Checkout Repo
        uses: actions/checkout@v4
      - name: Login to GitHub Package Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Build and push CMDB-API Docker image
        uses: docker/build-push-action@v6
        with:
          file: docker/Dockerfile-API
          context: .
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ env.REGISTRY_SERVER_ADDRESS }}/cmdb-api:${{ env.TAG }}
  release-ui-images:
    runs-on: ubuntu-latest
    needs: [setup-environment]
    permissions:
      contents: read
      packages: write
    timeout-minutes: 90
    steps:
      - name: Checkout Repo
        uses: actions/checkout@v4
      - name: Login to GitHub Package Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Build and push CMDB-UI Docker image
        uses: docker/build-push-action@v6
        with:
          file: docker/Dockerfile-UI
          context: .
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ env.REGISTRY_SERVER_ADDRESS }}/cmdb-ui:${{ env.TAG }}