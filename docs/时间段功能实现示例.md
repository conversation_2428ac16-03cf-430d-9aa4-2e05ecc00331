# OMP负载数据上报增加时间段功能实现方案

由于新版实施细则中，提供了按时间段统计数据的选项，本文档展示了在现有Load模块基础上正确添加时间段功能的实现方案。核心原则是**扩展现有方法而非重写**，确保向后兼容性和业务逻辑一致性。

## 核心设计原则

1. **向后兼容性**：所有新参数都是可选的，不影响现有功能
2. **业务逻辑一致性**：保持原有的数据验证、序列化等关键逻辑
3. **最小化变更**：只在必要的地方添加时间段相关代码
4. **渐进式实现**：分步骤实现，确保每步都稳定可靠

## 1. 常量定义扩展

**文件**: `cmdb-api/api/lib/cmdb/load/const.py`

```python
# 在现有枚举后添加时间段枚举
class TimeSlotEnum(BaseEnum):
    """时间段枚举 - 闲时和忙时"""
    IDLE_TIME = "00"        # 00:00-08:00 闲时
    BUSY_TIME = "08"        # 08:00-18:00 忙时
    
    # 默认时段设置为忙时
    DEFAULT_SLOT = BUSY_TIME
    
    @classmethod
    def get_slot_description(cls, slot_value):
        """获取时间段描述"""
        descriptions = {
            cls.IDLE_TIME: "闲时(00:00-08:00)",
            cls.BUSY_TIME: "忙时(08:00-18:00)"
        }
        return descriptions.get(slot_value, "未知时段")
    
    @classmethod
    def get_all_slots_info(cls):
        """获取所有时间段信息 - 便于前端展示"""
        return [
            {"value": cls.IDLE_TIME, "label": "闲时", "time_range": "00:00-08:00"},
            {"value": cls.BUSY_TIME, "label": "忙时", "time_range": "08:00-18:00"}
        ]
    
    @classmethod
    def validate_slot(cls, slot_value):
        """验证时间段值"""
        return slot_value in cls.all()
```

## 2. 数据库模型扩展

**文件**: `cmdb-api/api/models/cmdb.py`

为所有LoadValue表添加时间段字段，以LoadValueInt为例：

```python
class LoadValueInt(Model):
    # ... 保持所有现有字段不变 ...
    
    # 新增时间段字段
    time_slot = db.Column(
        db.String(2), 
        nullable=True, 
        comment='时间段标识：00-闲时(00:00-08:00), 08-忙时(08:00-18:00)'
    )

    # 更新索引定义以支持时间段
    __table_args__ = (
        # 每日数据的唯一约束(包含时间段)
        db.Index(
            "uk_daily_data_ints", 
            "ci_id", "load_attr_id", "collect_date", "time_slot", 
            unique=True, 
            mysql_where="collect_date IS NOT NULL"
        ),
        # 每月数据的唯一约束(不包含时间段)
        db.Index(
            "uk_monthly_data_ints", 
            "ci_id", "load_attr_id", "collect_month",
            unique=True, 
            mysql_where="collect_month IS NOT NULL"
        ),
        # 查询性能优化索引
        db.Index("idx_ci_date_slot_ints", "ci_id", "collect_date", "time_slot"),
    )

# 同样需要修改：LoadValueFloat, LoadValueText, LoadValueList
```

## 3. 时间段验证器

**文件**: `cmdb-api/api/lib/cmdb/load/utils.py`

```python
# 在现有utils.py文件末尾添加
from flask import abort
from api.lib.cmdb.load.const import TimeSlotEnum, PeriodTypeEnum

class TimeSlotValidator:
    """时间段验证器 - 统一验证逻辑"""
    
    @staticmethod
    def validate_and_normalize(time_slot: str = None) -> str:
        """验证并规范化时间段参数"""
        if not time_slot:
            return TimeSlotEnum.DEFAULT_SLOT
        
        if not TimeSlotEnum.validate_slot(time_slot):
            abort(400, f"无效的时间段: {time_slot}, 有效值: 00(闲时), 08(忙时)")
        
        return time_slot
    
    @staticmethod 
    def should_apply_time_slot(attr, period_type: str) -> bool:
        """判断是否为需要时间段的每日数据"""
        return (not attr.is_monthly and 
                period_type == PeriodTypeEnum.DAILY)
```

## 4. 核心业务逻辑扩展

**文件**: `cmdb-api/api/lib/cmdb/load/load_attr.py`

### 4.1 扩展数据验证方法

```python
# 导入时间段相关模块
from api.lib.cmdb.load.utils import TimeSlotValidator
from api.lib.cmdb.load.const import TimeSlotEnum

class LoadDataManager:
    """负载数据管理类"""

    @classmethod
    def validate_data(cls, type_id: int, data: List[Dict], time_slot: str = None) -> Tuple[List[Dict], List[Dict]]:
        """验证数据格式并转换为数据库记录格式 - 支持时间段
        
        Args:
            type_id: CI类型ID
            data: 原始数据列表
            time_slot: 时间段标识(可选，默认为忙时)
            
        Returns:
            Tuple[List[Dict], List[Dict]]: (有效记录列表, 无效记录列表)
        """
        # 验证时间段参数
        validated_time_slot = TimeSlotValidator.validate_and_normalize(time_slot) if time_slot else None
        
        # 保持原有的CI类型和属性获取逻辑
        from api.lib.cmdb.ci import CIManager, CITypeManager
        try:
            ci_type = CITypeManager.check_is_existed(type_id)
            unique_key = AttributeCache.get(ci_type.unique_id)
            attributes = CITypeLoadAttrCache.get_by_type(type_id)
            if not attributes:
                return abort(400, ErrFormat.ci_type_not_found2.format(type_id))
        except Exception as e:
            current_app.logger.error(f"获取CI类型信息失败: {str(e)}")
            return abort(400, ErrFormat.argument_value_invalid.format(str(e)))

        # 保持原有的属性映射逻辑
        type_attrs = LoadRelationsManager.get_type_attrs(type_id)
        attr_map = {}
        for attr in type_attrs:
            attr_map[attr.name] = attr
            if attr.alias:
                attr_map[attr.alias] = attr

        valid_records = []
        invalid_records = []

        for record in data:
            try:
                # 保持原有的基础验证逻辑
                unique_value = record.get('unique_value')
                if not unique_value:
                    raise ValueError("缺少unique_value字段")

                # 保持原有的CI存在性验证
                ci = CIManager.ci_is_exist(unique_key, unique_value, type_id)
                if not ci:
                    raise ValueError(f"CI不存在: {unique_value}")

                # 保持原有的周期数据处理逻辑
                for period, values in record.items():
                    if period == 'unique_value':
                        continue

                    # 保持原有的日期格式验证
                    is_monthly = len(period.split('-')) == 2
                    try:
                        if is_monthly:
                            datetime.datetime.strptime(period, '%Y-%m')
                        else:
                            datetime.datetime.strptime(period, '%Y-%m-%d')
                    except ValueError:
                        raise ValueError(f"无效的日期格式: {period}")

                    # 保持原有的字段值处理逻辑
                    for field_name, value in values.items():
                        attr = attr_map.get(field_name)
                        if not attr:
                            raise ValueError(f"未知的字段: {field_name}")

                        # 保持原有的月度属性验证
                        if attr.is_monthly and not is_monthly:
                            raise ValueError(f"属性 {field_name} 需要按月录入")

                        # ⭐ 关键：保持原有的值序列化逻辑
                        validated_value = LoadValueTypeMap.serialize_value(
                            attr.value_type, value)

                        # 构建数据库记录 - 在原有基础上扩展
                        record_data = {
                            'ci_id': ci.id,
                            'unique_value': unique_value,
                            'load_attr_id': attr.id,
                            'value': validated_value,
                            'collect_date': None if is_monthly else datetime.datetime.strptime(period, '%Y-%m-%d').date(),
                            'collect_month': period if is_monthly else None
                        }
                        
                        # 只在需要时添加时间段字段
                        if validated_time_slot and not is_monthly and not attr.is_monthly:
                            record_data['time_slot'] = validated_time_slot
                        else:
                            record_data['time_slot'] = None
                            
                        valid_records.append(record_data)

            except Exception as e:
                # 保持原有的错误处理格式
                invalid_records.append({
                    'record': str(record),
                    'error': str(e)
                })
                continue

        return valid_records, invalid_records
```

### 4.2 扩展批量写入方法

```python
    @classmethod
    def _batch_write_records(cls, records: List[Dict], batch_size: int, 
                            history: LoadDataImportHistory = None) -> Tuple[int, int]:
        """批量写入记录到数据库 - 支持时间段
        
        Args:
            records: 记录列表
            batch_size: 批次大小
            history: 导入历史记录对象
            
        Returns:
            Tuple[int, int]: (插入数量, 更新数量)
        """
        insert_count = 0
        update_count = 0
        processed_count = 0

        # 保持原有的数据分组逻辑
        grouped_data = defaultdict(list)
        for record in records:
            value_table = LoadValueTypeMap.get_table_by_attr_id(record['load_attr_id'])
            if value_table:
                grouped_data[value_table].append(record)

        # 保持原有的批量写入逻辑
        for value_table, table_records in grouped_data.items():
            current_app.logger.debug(f"正在向 {value_table.__tablename__} 写入 {len(table_records)} 条记录")

            for i in range(0, len(table_records), batch_size):
                batch = table_records[i:i + batch_size]
                current_batch = i // batch_size + 1

                try:
                    # 动态构建SQL字段列表以支持时间段
                    base_fields = "(ci_id, unique_value, load_attr_id, value, collect_date, collect_month, created_at, deleted"
                    base_values = "(:ci_id, :unique_value, :load_attr_id, :value, :collect_date, :collect_month, NOW(), 0"
                    
                    # 检查是否需要包含时间段字段
                    has_time_slot = any('time_slot' in record and record['time_slot'] is not None for record in batch)
                    if has_time_slot:
                        fields = base_fields + ", time_slot)"
                        values = base_values + ", :time_slot)"
                    else:
                        fields = base_fields + ")"
                        values = base_values + ")"

                    # 构建UPSERT语句
                    insert_stmt = f"""
                        INSERT INTO {value_table.__tablename__}
                        {fields}
                        VALUES
                        {values}
                        ON DUPLICATE KEY UPDATE
                        value = VALUES(value),
                        updated_at = NOW()
                    """

                    # 执行批量UPSERT
                    result = db.session.execute(insert_stmt, batch)
                    affected_rows = result.rowcount

                    # 保持原有的精确计数逻辑
                    if affected_rows > 0:
                        n = len(batch)
                        inserted = 2 * n - affected_rows
                        updated = affected_rows - n

                        insert_count += inserted
                        update_count += updated

                    processed_count += len(batch)

                    # 保持原有的历史记录更新逻辑
                    if history:
                        history.update(
                            success_count=insert_count + update_count,
                            current_batch=current_batch,
                            processed_count=processed_count
                        )

                    current_app.logger.debug(
                        f"批次 {current_batch} 处理完成: 新增 {inserted} 条, 更新 {updated} 条, "
                        f"总处理数 {processed_count}/{len(records)}"
                    )

                except Exception as e:
                    current_app.logger.error(f"批量写入失败: {str(e)}")
                    db.session.rollback()
                    raise

        db.session.commit()
        return insert_count, update_count
```

### 4.3 扩展导入入口方法

```python
    @staticmethod
    def batch_import(type_id: int, data: list, time_slot: str = None, 
                    batch_size: int = 5000, file_name: str = None) -> dict:
        """批量导入负载数据的入口方法 - 支持时间段
        
        Args:
            type_id: CI类型ID
            data: 要导入的数据列表
            time_slot: 时间段标识(可选)
            batch_size: 批次大小
            file_name: 文件名
            
        Returns:
            dict: 导入结果
        """
        from api.tasks.cmdb import batch_import_load_data

        # 验证时间段参数
        validated_time_slot = TimeSlotValidator.validate_and_normalize(time_slot) if time_slot else None

        # 保持原有的历史记录创建逻辑
        history = LoadHistoryManager.add(
            type_id=type_id,
            batch_size=batch_size,
            file_name=file_name,
            status=HistoryStatusEnum.BEGINSYNC
        )
        current_app.logger.debug(f"创建历史记录: id={history.id}")

        # 保持原有的提交和验证逻辑
        db.session.commit()

        test_history = LoadDataImportHistory.get_by_id(history.id)
        if not test_history:
            raise ValueError(f"历史记录创建失败: {history.id}")

        # 创建异步任务，传递时间段参数
        task = batch_import_load_data.apply_async(
            args=(type_id, data, batch_size, history.id, validated_time_slot),
            queue=CMDB_QUEUE
        )

        # 返回结果，包含时间段信息
        result = {
            'total': len(data),
            'batch_size': batch_size,
            'history_id': history.id,
            'status': 'submitted'
        }
        
        # 只在有时间段时添加相关信息
        if validated_time_slot:
            result.update({
                'time_slot': validated_time_slot,
                'time_slot_description': TimeSlotEnum.get_slot_description(validated_time_slot)
            })
            
        return result

    @staticmethod
    def batch_import_async(type_id: int, data: List[Dict], history_id: int, 
                          batch_size: int = 5000, time_slot: str = None) -> Dict[str, Any]:
        """异步执行批量导入负载数据 - 支持时间段"""
        
        try:
            history = LoadDataImportHistory.get_by_id(history_id)
            if not history:
                raise ValueError(f"导入历史记录不存在: {history_id}")
            
            # 更新状态为处理中
            history.update(status=HistoryStatusEnum.PROCESSING)

            # 使用扩展后的验证方法
            valid_records, invalid_records = LoadDataManager.validate_data(
                type_id, data, time_slot
            )
            current_app.logger.debug(f"验证和转换数据完成, 有效记录数: {len(valid_records)}, 无效记录数: {len(invalid_records)}")
            
            if invalid_records:
                current_app.logger.warning(f"数据验证失败: {len(invalid_records)} 条记录")

            # 使用扩展后的批量写入方法
            insert_count, update_count = LoadDataManager._batch_write_records(
                valid_records,
                batch_size,
                history=history
            )

            # 更新历史记录完成状态
            history.update(
                total_count=len(valid_records) + len(invalid_records),
                success_count=insert_count + update_count,
                error_count=len(invalid_records),
                status=HistoryStatusEnum.COMPLETED if len(invalid_records) == 0 else HistoryStatusEnum.FAILED,
                end_time=datetime.datetime.now()
            )

            # 返回结果
            result = {
                'total': len(data),
                'success': insert_count + update_count,
                'inserted': insert_count,
                'updated': update_count,
                'failed': len(invalid_records),
                'errors': invalid_records,
                'history_id': history.id
            }
            
            # 只在有时间段时添加相关信息
            if time_slot:
                result['time_slot'] = time_slot
                
            return result

        except Exception as e:
            current_app.logger.debug(f"异步导入负载数据失败: {str(e)}")
            if 'history' in locals():
                history.update(
                    status=HistoryStatusEnum.FAILED,
                    error_message=str(e),
                    end_time=datetime.datetime.now()
                )
            raise
```

### 4.4 扩展查询方法

```python
    @classmethod
    def query_data(cls, type_id: int = None, ci_ids: List[int] = None,
                  unique_values: List[str] = None, attribute_ids: List[int] = None,
                  start_period: str = None, end_period: str = None,
                  period_type: str = PeriodTypeEnum.DAILY,
                  time_slot: str = None,  # 新增参数
                  page: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """查询数据 - 支持时间段过滤
        
        Args:
            time_slot: 时间段标识(可选)，只对每日数据有效
            其他参数保持不变
            
        Returns:
            dict: 查询结果，包含时间段信息
        """
        
        # 验证时间段参数
        if time_slot:
            time_slot = TimeSlotValidator.validate_and_normalize(time_slot)
        
        # 保持原有的查询逻辑，只在必要时添加时间段过滤
        # ... 原有查询逻辑 ...
        
        # 在返回结果中包含时间段信息
        result = {
            "page": page,
            "page_size": page_size, 
            "total": total,
            "data": result_data
        }
        
        # 只在有时间段时添加相关信息
        if time_slot:
            result.update({
                "time_slot": time_slot,
                "time_slot_description": TimeSlotEnum.get_slot_description(time_slot)
            })
        
        return result

### 4.5 缓存系统调整

在实现时间段功能过程中，我们发现了一个重要的缓存问题：`LoadDataQueryCache` 类的缓存键生成逻辑没有包含 `time_slot` 参数，导致不同时间段的查询可能返回相同的缓存结果。

#### 4.5.1 问题分析

**原始问题**：
- 缓存键生成方法 `_gen_cache_key` 没有包含 `time_slot` 参数
- 不同时间段查询相同条件时会使用同一个缓存键
- 可能导致查询结果错误，影响数据准确性

**文件**: `cmdb-api/api/lib/cmdb/cache.py`

#### 4.5.2 缓存键修复

```python
class LoadDataQueryCache:
    """负载数据查询缓存管理，用于缓存_get_ci_period_pairs的结果"""

    PREFIX = "LoadDataQuery"
    CACHE_TIMEOUT = 180  # 缓存过期时间，3分钟

    @classmethod
    def _gen_cache_key(cls, type_id, ci_ids, attribute_ids, start_period, end_period, period_type, time_slot=None):
        """
        生成缓存键 - 修复：包含时间段参数
        Args:
            type_id: CI类型ID
            ci_ids: CI ID列表
            attribute_ids: 属性ID列表
            start_period: 开始时间
            end_period: 结束时间
            period_type: 时间类型
            time_slot: 时间段(可选) - 新增参数
        Returns:
            str: 缓存键
        """
        # 对列表参数进行排序，确保相同参数生成相同的键
        ci_ids_str = "_".join(map(str, sorted(ci_ids))) if ci_ids else "none"
        attr_ids_str = "_".join(map(str, sorted(attribute_ids))) if attribute_ids else "none"
        time_slot_str = time_slot if time_slot else "none"  # 关键修复：添加时间段处理

        # 构建缓存键，包含时间段参数
        key = f"{cls.PREFIX}::type_{type_id}::ci_{ci_ids_str}::attr_{attr_ids_str}::period_{start_period}_{end_period}_{period_type}::slot_{time_slot_str}"
        return key

    @classmethod
    def get(cls, type_id, ci_ids, attribute_ids, start_period, end_period, period_type, time_slot=None):
        """
        获取缓存的CI-时间组合 - 修复：支持时间段参数
        """
        key = cls._gen_cache_key(type_id, ci_ids, attribute_ids, start_period, end_period, period_type, time_slot)
        cached_data = cache.get(key)

        if cached_data is not None:
            current_app.logger.debug(f"命中负载数据查询缓存: {key}")

        return cached_data

    @classmethod
    def set(cls, type_id, ci_ids, attribute_ids, start_period, end_period, period_type, data, time_slot=None):
        """
        设置CI-时间组合缓存 - 修复：支持时间段参数
        """
        key = cls._gen_cache_key(type_id, ci_ids, attribute_ids, start_period, end_period, period_type, time_slot)
        cache.set(key, data, timeout=cls.CACHE_TIMEOUT)
        current_app.logger.debug(f"设置负载数据查询缓存: {key}")
```

#### 4.5.3 查询方法调用调整

在 `LoadDataManager._get_ci_period_pairs` 方法中，需要确保缓存调用传递了时间段参数：

```python
@classmethod
def _get_ci_period_pairs(cls, type_id, ci_ids, attributes, start_period, end_period, period_type, time_slot=None):
    """获取所有符合条件的CI-时间组合 - 修复：正确传递时间段给缓存"""
    # 从缓存中获取结果 - 关键修复：传递时间段参数
    attribute_ids = [attr.id for attr in attributes]
    cached_pairs = LoadDataQueryCache.get(
        type_id=type_id,
        ci_ids=ci_ids,
        attribute_ids=attribute_ids,
        start_period=start_period,
        end_period=end_period,
        period_type=period_type,
        time_slot=time_slot  # 确保传递时间段参数
    )

    # 如果缓存命中，直接返回缓存的结果
    if cached_pairs is not None:
        current_app.logger.debug(f"从缓存获取CI-时间组合，共 {len(cached_pairs)} 个")
        return cached_pairs

    # 缓存未命中，执行查询
    # ... 查询逻辑 ...

    # 将结果存入缓存 - 关键修复：传递时间段参数
    LoadDataQueryCache.set(
        type_id=type_id,
        ci_ids=ci_ids,
        attribute_ids=attribute_ids,
        start_period=start_period,
        end_period=end_period,
        period_type=period_type,
        data=ci_period_pairs,
        time_slot=time_slot  # 确保传递时间段参数
    )

    return ci_period_pairs
```

#### 4.5.4 修复效果

**修复前缓存键示例**：
```
LoadDataQuery::type_1::ci_123::attr_1::period_2024-01-01_2024-01-31_0
```

**修复后缓存键示例**：
```bash
# 忙时数据缓存键
LoadDataQuery::type_1::ci_123::attr_1::period_2024-01-01_2024-01-31_0::slot_08

# 闲时数据缓存键  
LoadDataQuery::type_1::ci_123::attr_1::period_2024-01-01_2024-01-31_0::slot_00

# 不指定时间段时的缓存键
LoadDataQuery::type_1::ci_123::attr_1::period_2024-01-01_2024-01-31_0::slot_none
```

#### 4.5.5 向后兼容性

- 新增的 `time_slot` 参数在所有缓存方法中都是可选的，默认值为 `None`
- 当 `time_slot` 为 `None` 时，缓存键中会使用 `"none"` 作为占位符
- 保持原有的缓存超时和清理逻辑不变
- 确保现有不传时间段的查询仍能正常工作

#### 4.5.6 测试验证

为了验证缓存修复的正确性，我们创建了专门的测试用例：

**文件**: `cmdb-api/tests/test_cases/lib/test_load_cache.py`

```python
class TestLoadDataQueryCache:
    """负载数据查询缓存测试类 - 专门测试缓存键的正确性"""

    def test_cache_key_generation_with_time_slot(self):
        """测试缓存键生成是否包含时间段参数"""
        # 测试包含时间段的缓存键
        key_with_slot = LoadDataQueryCache._gen_cache_key(
            type_id=1,
            ci_ids=[1, 2, 3],
            attribute_ids=[10, 20],
            start_period="2024-01-01",
            end_period="2024-01-31",
            period_type="0",
            time_slot="08"
        )
        # 验证时间段包含在缓存键中
        assert "slot_08" in key_with_slot
        
        # 测试不包含时间段的缓存键
        key_without_slot = LoadDataQueryCache._gen_cache_key(
            type_id=1,
            ci_ids=[1, 2, 3],
            attribute_ids=[10, 20],
            start_period="2024-01-01",
            end_period="2024-01-31",
            period_type="0"
        )
        # 验证使用默认占位符
        assert "slot_none" in key_without_slot

    def test_cache_isolation_different_time_slots(self):
        """测试不同时间段使用不同的缓存键"""
        # 相同查询条件，不同时间段应该生成不同的缓存键
        base_params = {
            'type_id': 1,
            'ci_ids': [1, 2],
            'attribute_ids': [10],
            'start_period': "2024-01-01", 
            'end_period': "2024-01-01",
            'period_type': "0"
        }
        
        key_busy = LoadDataQueryCache._gen_cache_key(**base_params, time_slot="08")
        key_idle = LoadDataQueryCache._gen_cache_key(**base_params, time_slot="00")
        
        # 验证不同时间段生成不同缓存键
        assert key_busy != key_idle
        assert "slot_08" in key_busy
        assert "slot_00" in key_idle
```

这个缓存修复确保了：
1. **数据准确性**：不同时间段的查询使用独立的缓存键
2. **性能优化**：相同时间段的重复查询能够正确命中缓存
3. **向后兼容**：现有不使用时间段的查询逻辑保持不变
4. **调试友好**：缓存键包含完整的查询上下文信息

## 5. API接口扩展

**文件**: `cmdb-api/api/views/cmdb/load/load_attr.py`

```python
class LoadDataView(APIView):
    """负载数据视图"""

    def get(self, type_id=None):
        """查询负载数据 - 支持时间段"""
        # 保持所有原有参数解析逻辑
        # ... 原有参数解析 ...
        
        # 添加时间段参数解析
        time_slot = request.values.get("time_slot")
        
        # 验证时间段参数 如果time_slot为空，则使用默认时间段
        if time_slot and not TimeSlotEnum.validate_slot(time_slot):
            return abort(400, f"无效的时间段: {time_slot}, 有效值: 00(闲时), 08(忙时)")
        elif not time_slot:
            time_slot = TimeSlotEnum.DEFAULT_SLOT
        
        # 调用扩展后的查询方法
        result = LoadDataManager.query_data(
            type_id=type_id,
            ci_ids=ci_ids,
            unique_values=unique_values,
            attribute_ids=attribute_ids,
            start_period=start_period,
            end_period=end_period,
            period_type=period_type,
            time_slot=time_slot,  # 传递时间段参数
            page=page,
            page_size=page_size
        )
        return self.jsonify(result)

    @args_required("upload_data")
    def post(self, type_id):
        """批量导入负载数据 - 支持时间段"""
        # 保持所有原有参数解析逻辑
        upload_data = request.values.get("upload_data")
        batch_size = request.values.get("batch_size", 5000, type=int)
        file_name = request.values.get("file_name")
        
        # 添加时间段参数解析
        time_slot = request.values.get("time_slot")

        # 验证时间段参数
        if time_slot and not TimeSlotEnum.validate_slot(time_slot):
            return abort(400, f"无效的时间段: {time_slot}")

        # 调用扩展后的导入方法
        result = LoadDataManager.batch_import(
            type_id=type_id,
            data=upload_data,
            time_slot=time_slot,  # 传递时间段参数
            batch_size=batch_size,
            file_name=file_name
        )
        return self.jsonify(result)


class TimeSlotInfoView(APIView):
    """时间段信息查询视图"""
    url_prefix = "/load/time_slots"

    def get(self):
        """获取所有时间段信息"""
        return self.jsonify({
            "time_slots": TimeSlotEnum.get_all_slots_info(),
            "default_slot": TimeSlotEnum.DEFAULT_SLOT,
            "description": "时间段用于区分每日负载数据的不同时间窗口"
        })
```

## 6. 异步任务扩展

**文件**: `cmdb-api/api/tasks/cmdb.py`

```python
@celery.task(name="cmdb.batch_import_load_data", queue=CMDB_QUEUE)
@flush_db
@reconnect_db
def batch_import_load_data(type_id: int, data: list, batch_size: int, history_id: int, time_slot: str = None):
    """批量导入负载数据异步任务 - 支持时间段"""
    try:
        current_app.logger.info(f"开始异步导入负载数据: type_id={type_id}, time_slot={time_slot}")
        
        # 调用扩展后的异步导入方法
        result = LoadDataManager.batch_import_async(
            type_id=type_id,
            data=data,
            batch_size=batch_size,
            history_id=history_id,
            time_slot=time_slot
        )
        
        current_app.logger.info("异步导入负载数据完成")
        return result
        
    except Exception as e:
        current_app.logger.error(f"异步导入负载数据失败: {str(e)}")
        raise
```

相比较之前的版本，本次如果数据导入错误，则状态为失败。

## 7. 数据迁移方案

### 7.1 数据库字段迁移

数据库字段会根据模型自动添加，无需手动处理。

### 7.2 现有数据迁移

**文件**: `cmdb-api/api/commands/load_migration.py`

```python
import click
import time
from flask import current_app
from flask.cli import with_appcontext
from sqlalchemy import text
from api.extensions import db
from api.lib.cmdb.load.const import TimeSlotEnum

@click.command()
@with_appcontext
@click.option('--dry-run', is_flag=True, help='只显示将要执行的操作，不实际执行')
@click.option('--batch-size', default=1000, help='批处理大小，默认1000')
def migrate_load_time_slot(dry_run, batch_size):
    """为现有每日负载数据设置默认时间段"""
    
    tables = [
        'c_load_value_ints',
        'c_load_value_floats', 
        'c_load_value_texts',
        'c_load_value_lists'
    ]
    
    default_time_slot = TimeSlotEnum.DEFAULT_SLOT
    
    click.echo("开始为现有每日数据设置默认时间段...")
    click.echo(f"默认时间段: {default_time_slot} ({TimeSlotEnum.get_slot_description(default_time_slot)})")
    
    total_processed = 0
    
    for table in tables:
        click.echo(f"\n处理表: {table}")
        
        # 统计需要更新的记录数
        count_sql = f"""
            SELECT COUNT(*) as count 
            FROM {table} 
            WHERE collect_date IS NOT NULL 
            AND (time_slot IS NULL OR time_slot = '')
        """
        
        if dry_run:
            click.echo(f"[试运行] 统计SQL: {count_sql}")
        else:
            result = db.session.execute(text(count_sql)).fetchone()
            total_count = result[0] if result else 0
            
            if total_count == 0:
                click.echo(f"表 {table} 无需要更新的记录")
                continue
            
            click.echo(f"表 {table} 需要更新 {total_count} 条记录")
            
            # 分批更新
            updated_count = 0
            batch_count = 0
            
            while updated_count < total_count:
                batch_count += 1
                start_time = time.time()
                
                update_sql = f"""
                    UPDATE {table} 
                    SET time_slot = '{default_time_slot}' 
                    WHERE collect_date IS NOT NULL 
                    AND (time_slot IS NULL OR time_slot = '') 
                    LIMIT {batch_size}
                """
                
                try:
                    result = db.session.execute(text(update_sql))
                    affected_rows = result.rowcount
                    db.session.commit()
                    
                    updated_count += affected_rows
                    total_processed += affected_rows
                    end_time = time.time()
                    
                    click.echo(f"  批次 {batch_count}: 更新 {affected_rows} 条记录 "
                              f"(总进度: {updated_count}/{total_count}) "
                              f"耗时: {end_time - start_time:.2f}s")
                    
                    if affected_rows == 0:
                        break
                        
                except Exception as e:
                    click.echo(f"✗ 批次 {batch_count} 更新失败: {str(e)}")
                    db.session.rollback()
                    break
    
    if not dry_run:
        click.echo(f"\n数据迁移完成! 总共处理 {total_processed} 条记录")
    else:
        click.echo("\n试运行完成，使用不带 --dry-run 参数执行实际迁移")


@click.command()
@with_appcontext
def verify_load_time_slot():
    """验证负载数据时间段迁移结果"""
    
    tables = [
        'c_load_value_ints',
        'c_load_value_floats', 
        'c_load_value_texts',
        'c_load_value_lists'
    ]
    
    click.echo("开始验证迁移结果...")
    
    all_success = True
    
    for table in tables:
        # 检查是否还有未设置时间段的每日数据
        check_sql = f"""
            SELECT COUNT(*) as count 
            FROM {table} 
            WHERE collect_date IS NOT NULL 
            AND (time_slot IS NULL OR time_slot = '')
        """
        
        result = db.session.execute(text(check_sql)).fetchone()
        remaining = result[0] if result else 0
        
        if remaining > 0:
            click.echo(f"❌ 表 {table}: 仍有 {remaining} 条每日数据未设置时间段")
            all_success = False
        else:
            click.echo(f"✅ 表 {table}: 验证通过，所有每日数据已设置时间段")
    
    if all_success:
        click.echo("\n🎉 所有表验证通过，迁移成功!")
    else:
        click.echo("\n⚠️ 部分表验证失败，请检查迁移过程")
```

**注册命令** (在 `cmdb-api/api/app.py` 中):

register_commands 将实现自动注册命令，无需手动注册。

## 8. 使用示例

### 8.1 命令行迁移

```bash
# 1. 试运行，查看将要迁移的数据量
cd cmdb-api
pipenv run flask migrate-load-time-slot --dry-run

# 2. 执行实际迁移
flask migrate-load-time-slot

# 3. 验证迁移结果
flask verify-load-time-slot
```

### 8.2 API调用示例

**导入数据时指定时间段**:
```python
# 导入忙时数据
data = [{
    "unique_value": "server001",
    "2024-01-01": {
        "cpu_usage": 85.5,
        "memory_usage": 92.3
    }
}]

result = LoadDataManager.batch_import(
    type_id=1,
    data=data,
    time_slot="08"  # 忙时
)
```

**查询特定时间段数据**:
```python
# 查询闲时数据
result = LoadDataManager.query_data(
    type_id=1,
    time_slot="00",  # 闲时
    start_period="2024-01-01",
    end_period="2024-01-31"
)
```

### 8.3 向后兼容性验证

```python
# 不传时间段参数，保持原有行为
result = LoadDataManager.batch_import(
    type_id=1,
    data=data
    # 不传 time_slot，使用默认值
)

# 原有查询方式继续有效
result = LoadDataManager.query_data(
    type_id=1,
    start_period="2024-01-01",
    end_period="2024-01-31"
    # 不传 time_slot，返回所有时间段数据
)
```

## 9. 测试用例

### 9.1 测试框架和最佳实践

#### 测试fixture设计原则

基于实际开发经验，我们总结了以下测试最佳实践：

**核心原则**：
1. **保持fixture一致性**：同一测试类中的测试应该使用相似级别的fixture
2. **避免混合模式**：不要在同一类中混合"无fixture"和"复杂fixture"的测试
3. **遵循现有模式**：参考项目中其他成功的测试类的fixture设计模式
4. **统一初始化级别**：确保所有测试都使用相同类型的fixture依赖

#### Flask应用状态管理

**问题背景**：
pytest在运行多个测试时可能遇到Flask应用状态冲突：
- 第一个测试处理HTTP请求后，Flask应用状态被"锁定"
- 后续测试的fixture试图重新初始化SQLAlchemy等扩展时发生冲突
- 表现为：`AssertionError: The setup method 'teardown_appcontext' can no longer be called`

**解决方案**：
1. 统一测试fixture的使用模式
2. 为简单测试添加基础fixture (`db_session`, `request_context`)
3. 避免在同一测试类中混合不同初始化级别的测试

### 9.2 业务逻辑层测试 (`test_load_data.py`)

**文件位置**: `cmdb-api/tests/test_cases/lib/test_load_data.py`

```python
class TestTimeSlotDataManager:
    """时间段功能业务逻辑测试类"""

    def test_time_slot_validation(self, db_session, app_context):
        """测试时间段验证功能"""
        from api.lib.cmdb.load.utils import TimeSlotValidator
        from api.lib.cmdb.load.const import TimeSlotEnum
        
        # 测试有效时间段
        assert TimeSlotValidator.validate_and_normalize("00") == "00"
        assert TimeSlotValidator.validate_and_normalize("08") == "08"
        
        # 测试默认值
        assert TimeSlotValidator.validate_and_normalize(None) == TimeSlotEnum.DEFAULT_SLOT
        assert TimeSlotValidator.validate_and_normalize("") == TimeSlotEnum.DEFAULT_SLOT
        
        # 测试无效时间段
        with pytest.raises(Exception):
            TimeSlotValidator.validate_and_normalize("99")

    def test_batch_import_with_time_slot(self, db_session, app_context, request_context, auth_user, celery_worker):
        """测试带时间段的批量导入"""
        ci = init_ci(1, auth_user)
        unique_value = ci[0][ci[0]['unique']]
        
        # 创建测试属性
        attr = LoadAttrManager.add_attr(
            name="cpu_usage_time_slot",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="CPU使用率(时段)"
        )
        LoadRelationsManager.add_type_attr(type_id=ci[0]['_type'], attr_id=attr.id)
        
        # 准备忙时数据
        test_data = [{
            "unique_value": unique_value,
            "2024-01-01": {
                "cpu_usage_time_slot": 85.5
            }
        }]
        
        # 执行导入
        result = LoadDataManager.batch_import(
            type_id=ci[0]['_type'],
            data=test_data,
            time_slot="08"
        )
        
        # 验证结果
        assert result["total"] == 1
        assert result.get("time_slot") == "08"
        assert result.get("time_slot_description") == "忙时(08:00-18:00)"

    def test_query_data_with_time_slot_filter(self, db_session, app_context, request_context, auth_user, celery_worker):
        """测试带时间段过滤的数据查询"""
        # 分别导入忙时和闲时数据，验证查询过滤功能
        # ... 详细实现见实际测试文件 ...
        
    def test_monthly_data_ignores_time_slot(self, db_session, app_context, request_context, auth_user, celery_worker):
        """测试月度数据忽略时间段"""
        # 验证月度数据不受时间段参数影响
        # ... 详细实现见实际测试文件 ...
```

### 9.3 API视图层测试 (`test_views_load.py`)

**文件位置**: `cmdb-api/tests/test_cases/views/load/test_views_load.py`

```python
class TestTimeSlotFeature:
    """时间段功能API测试类"""

    @pytest.fixture(scope='function')
    def prepare_time_slot_data(self, db_session, request_context, celery_worker, auth_user):
        """准备时间段测试数据"""
        # 初始化CI类型和实例
        ci = init_ci(2, auth_user)
        type_id = ci[0]['_type']
        
        # 创建测试属性
        cpu_attr = LoadAttrManager.add_attr(
            name="cpu_usage_time_slot",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="CPU使用率(时段)"
        )
        mem_attr = LoadAttrManager.add_attr(
            name="memory_usage_time_slot",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="内存使用率(时段)",
        )
        cost_attr = LoadAttrManager.add_attr(
            name="monthly_cost_time_slot",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="月度成本(时段)",
            is_monthly=True
        )
        
        # 添加属性到CI类型
        for attr in [cpu_attr, mem_attr, cost_attr]:
            LoadRelationsManager.add_type_attr(type_id=type_id, attr_id=attr.id)
        
        return {
            'type_id': type_id,
            'ci': ci,
            'attrs': {
                'cpu': cpu_attr,
                'memory': mem_attr,
                'cost': cost_attr
            }
        }

    def test_time_slot_info_endpoint(self, client: FlaskClient, db_session, request_context):
        """测试时间段信息查询接口"""
        response = client.get('/api/v0.1/load/time_slots')
        assert response.status_code == 200
        
        result = json.loads(response.data)
        assert 'time_slots' in result
        assert 'default_slot' in result
        
        # 验证时间段信息
        time_slots = result['time_slots']
        assert len(time_slots) == 2
        assert any(slot['value'] == '00' and slot['label'] == '闲时' for slot in time_slots)
        assert any(slot['value'] == '08' and slot['label'] == '忙时' for slot in time_slots)

    def test_import_data_with_time_slot(self, client: FlaskClient, prepare_time_slot_data):
        """测试带时间段的数据导入"""
        data = prepare_time_slot_data
        type_id = data['type_id']
        unique_value = data['ci'][0][data['ci'][0]['unique']]
        cpu_attr_name = data['attrs']['cpu'].name
        
        # 测试导入忙时数据
        import_data = [{
            "unique_value": unique_value,
            "2024-01-01": {
                cpu_attr_name: 85.5
            }
        }]
        
        response = client.post(
            f'/api/v0.1/load/data/{type_id}',
            data={
                'upload_data': json.dumps(import_data),
                'time_slot': '08',  # 忙时
                'file_name': 'test_busy_time.json'
            }
        )
        assert response.status_code == 200
        
        result = response.json
        assert result.get('time_slot') == '08'
        assert result.get('time_slot_description') == '忙时(08:00-18:00)'

    def test_query_data_with_time_slot(self, client: FlaskClient, prepare_time_slot_data):
        """测试带时间段的数据查询"""
        # 先导入数据，再查询验证时间段过滤功能
        # ... 详细实现见实际测试文件 ...

    def test_query_data_with_invalid_time_slot(self, client: FlaskClient, db_session, request_context, auth_user):
        """测试使用无效时间段查询数据"""
        # 初始化基础数据（注意：使用基础fixture而非复杂fixture）
        ci = init_ci(1, auth_user)
        type_id = ci[0]['_type']
        
        # 创建基础属性
        attr = LoadAttrManager.add_attr(
            name="test_invalid_slot",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="测试无效时间段"
        )
        LoadRelationsManager.add_type_attr(type_id=type_id, attr_id=attr.id)
        
        query_params = {
            'type_id': type_id,
            'attribute_ids': str(attr.id),
            'time_slot': 'invalid'
        }
        
        response = client.get('/api/v0.1/load/data', query_string=query_params)
        assert response.status_code == 400
        assert '无效的时间段' in response.json['message']

    def test_backward_compatibility(self, client: FlaskClient, prepare_time_slot_data):
        """测试向后兼容性 - 不传时间段参数"""
        data = prepare_time_slot_data
        type_id = data['type_id']
        unique_value = data['ci'][0][data['ci'][0]['unique']]
        cpu_attr_name = data['attrs']['cpu'].name
        
        # 不传时间段参数的导入（保持原有行为）
        import_data = [{
            "unique_value": unique_value,
            "2024-01-01": {
                cpu_attr_name: 75.5
            }
        }]
        
        response = client.post(
            f'/api/v0.1/load/data/{type_id}',
            data={
                'upload_data': json.dumps(import_data),
                # 不传 time_slot 参数
                'file_name': 'test_backward_compatibility.json'
            }
        )
        assert response.status_code == 200
        
        result = response.json
        # 不应该包含时间段信息或使用默认值
        assert 'time_slot' not in result or result.get('time_slot') is None
```

### 9.4 测试执行验证

#### 单独执行测试
```bash
# 业务逻辑层测试
python -m pytest cmdb-api/tests/test_cases/lib/test_load_data.py::TestTimeSlotDataManager -v

# API视图层测试  
python -m pytest cmdb-api/tests/test_cases/views/load/test_views_load.py::TestTimeSlotFeature -v
```

#### 测试覆盖范围

**业务逻辑层测试** (11个测试用例)：
- 时间段验证和枚举功能
- 带时间段的数据导入（忙时/闲时）
- 数据验证逻辑（有/无时间段参数）
- 时间段过滤查询功能
- 月度数据处理（忽略时间段）
- 混合数据类型处理
- 无效参数处理

**API视图层测试** (8个测试用例)：
- 时间段信息查询接口
- 带时间段的数据导入API
- 时间段查询API
- 无效参数处理
- 月度数据处理
- 向后兼容性验证

**总计19个测试用例**，全面覆盖时间段功能的各个方面。

## 11. 开发过程经验总结

### 11.1 技术挑战与解决方案

#### 数据库模型设计挑战
- **问题**：如何在现有表结构基础上添加时间段字段，同时保持向后兼容
- **解决方案**：使用可空字段和复合索引，区分日度数据（包含时间段）和月度数据（忽略时间段）

#### 业务逻辑扩展挑战  
- **问题**：保持原有业务逻辑完整性的同时添加新功能
- **解决方案**：采用"扩展而非重写"的策略，保留所有原有的验证、序列化逻辑

### 11.2 关键设计决策

1. **默认时间段策略**：选择忙时(08)作为默认值，符合业务常识
2. **参数设计**：所有新增参数都是可选的，确保完全向后兼容
3. **查询过滤逻辑**：只对日度数据应用时间段过滤，月度数据保持原有逻辑
4. **错误处理**：提供清晰的错误信息，便于调试和使用


### 11.3 性能优化考虑

1. **索引优化**：为时间段字段创建合适的复合索引
2. **查询优化**：使用动态SQL构建，避免全表扫描
3. **缓存策略**：保持原有缓存机制的有效性

## 总结

本实现方案遵循以下核心原则：

1. **向后兼容**：所有现有功能保持不变
2. **业务逻辑一致**：保持原有的验证、序列化等关键逻辑
3. **最小化变更**：只在必要的地方添加时间段支持
4. **渐进式实现**：分阶段实施，降低风险
5. **测试驱动**：完整的测试覆盖确保功能正确性

通过这种方式，既能成功添加时间段功能，又能确保系统的稳定性和可维护性。整个开发过程展示了从问题发现、根本原因分析到正确解决方案设计和实现的完整流程，为类似功能扩展提供了宝贵的经验教训。

