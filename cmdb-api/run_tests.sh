#!/bin/bash

# 确保脚本在项目根目录下运行
if [ ! -d "api" ] || [ ! -d "tests" ]; then
    echo "错误：请在项目根目录下运行此脚本"
    exit 1
fi

# 生成时间戳目录名
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
REPORT_DIR="reports/test_${TIMESTAMP}"

# 创建报告目录
mkdir -p "${REPORT_DIR}"

# 确保依赖已安装
# pip install -r requirements-dev.txt

# 设置环境变量
export PYTHONPATH="${PYTHONPATH}:${PWD}"
export FLASK_ENV=testing

# 运行测试并生成报告
pytest \
    -v \
    --html="${REPORT_DIR}/report.html" \
    --self-contained-html \
    --cov=api \
    --cov-report=html:"${REPORT_DIR}/coverage" \
    --cov-report=term \
    --junitxml="${REPORT_DIR}/junit.xml" \
    tests/

# 运行结果
TEST_EXIT_CODE=$?

# 输出测试完成信息
if [ $TEST_EXIT_CODE -eq 0 ]; then
    echo "测试成功完成！报告已生成在 ${REPORT_DIR} 目录下"
else
    echo "测试过程中出现错误，请查看详细日志"
fi

echo "- HTML报告：${REPORT_DIR}/report.html"
echo "- 覆盖率报告：${REPORT_DIR}/coverage/index.html"
echo "- JUnit报告：${REPORT_DIR}/junit.xml"

# 创建最新报告的软链接
rm -f reports/latest
ln -s "test_${TIMESTAMP}" reports/latest

echo "- 最新报告快捷方式：reports/latest/"

# 返回测试结果
exit $TEST_EXIT_CODE