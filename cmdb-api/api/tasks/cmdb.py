# -*- coding:utf-8 -*- 


import datetime
import json
import redis_lock
from flask import current_app
from flask_login import login_user
import re
from collections import defaultdict
import time

import api.lib.cmdb.ci
from api.extensions import celery
from api.extensions import db
from api.extensions import es
from api.extensions import rd
from api.lib.cmdb.cache import AttributeCache
from api.lib.cmdb.cache import CITypeAttributesCache
from api.lib.cmdb.const import CMDB_QUEUE
from api.lib.cmdb.const import REDIS_PREFIX_CI
from api.lib.cmdb.const import REDIS_PREFIX_CI_RELATION
from api.lib.cmdb.const import REDIS_PREFIX_CI_RELATION2
from api.lib.cmdb.const import CostTypeEnum
from api.lib.cmdb.const import RelationSourceEnum
from api.lib.cmdb.perms import CIFilterPermsCRUD
from api.lib.decorator import flush_db
from api.lib.decorator import reconnect_db
from api.lib.perm.acl.cache import UserCache
from api.lib.utils import handle_arg_list
from api.models.cmdb import AutoDiscoveryCI
from api.models.cmdb import AutoDiscoveryCIType
from api.models.cmdb import AutoDiscoveryCITypeRelation
from api.models.cmdb import CI
from api.models.cmdb import CIRelation
from api.models.cmdb import CITypeAttribute
from api.models.cmdb import CITypeCost
from api.models.cmdb import MeasurementUnit
from api.lib.cmdb.attribute import AttributeManager
from api.lib.cmdb.load.load_attr import LoadDataManager
from api.lib.cmdb.load.load_attr import LoadDataImportHistory


@celery.task(name="cmdb.ci_cache", queue=CMDB_QUEUE)
@flush_db
@reconnect_db
def ci_cache(ci_id, operate_type, record_id):
    from api.lib.cmdb.ci import CITriggerManager
    from api.lib.cmdb.ci import CIRelationManager

    m = api.lib.cmdb.ci.CIManager()
    ci_dict = m.get_ci_by_id_from_db(ci_id, need_children=False, use_master=False)

    if current_app.config.get("USE_ES"):
        es.create_or_update(ci_id, ci_dict)
    else:
        rd.create_or_update({ci_id: json.dumps(ci_dict)}, REDIS_PREFIX_CI)

    current_app.logger.info("{0} flush..........".format(ci_id))

    if operate_type:
        with current_app.test_request_context():
            login_user(UserCache.get('worker'))
            CITriggerManager.fire(operate_type, ci_dict, record_id)

    ci_dict and CIRelationManager.build_by_attribute(ci_dict)


@celery.task(name="cmdb.rebuild_relation_for_attribute_changed", queue=CMDB_QUEUE)
@reconnect_db
def rebuild_relation_for_attribute_changed(ci_type_relation, uid):
    from api.lib.cmdb.ci import CIRelationManager

    CIRelationManager.rebuild_all_by_attribute(ci_type_relation, uid)


@celery.task(name="cmdb.batch_ci_cache", queue=CMDB_QUEUE)
@flush_db
@reconnect_db
def batch_ci_cache(ci_ids, ):  # only for attribute change index
    for ci_id in ci_ids:
        m = api.lib.cmdb.ci.CIManager()
        ci_dict = m.get_ci_by_id_from_db(ci_id, need_children=False, use_master=False)

        if current_app.config.get("USE_ES"):
            es.create_or_update(ci_id, ci_dict)
        else:
            rd.create_or_update({ci_id: json.dumps(ci_dict)}, REDIS_PREFIX_CI)

        current_app.logger.info("{0} flush..........".format(ci_id))


@celery.task(name="cmdb.ci_delete", queue=CMDB_QUEUE)
@reconnect_db
def ci_delete(ci_id):
    current_app.logger.info(ci_id)

    if current_app.config.get("USE_ES"):
        es.delete(ci_id)
    else:
        rd.delete(ci_id, REDIS_PREFIX_CI)

    instance = AutoDiscoveryCI.get_by(ci_id=ci_id, to_dict=False, first=True)
    if instance is not None:
        adt = AutoDiscoveryCIType.get_by_id(instance.adt_id)
        if adt:
            adt.update(updated_at=datetime.datetime.now())
        instance.delete()

    current_app.logger.info("{0} delete..........".format(ci_id))


@celery.task(name="cmdb.delete_id_filter", queue=CMDB_QUEUE)
@reconnect_db
def delete_id_filter(ci_id):
    CIFilterPermsCRUD().delete_id_filter_by_ci_id(ci_id)


@celery.task(name="cmdb.ci_delete_trigger", queue=CMDB_QUEUE)
@reconnect_db
def ci_delete_trigger(trigger, operate_type, ci_dict):
    current_app.logger.info('delete ci {} trigger'.format(ci_dict['_id']))
    from api.lib.cmdb.ci import CITriggerManager

    current_app.test_request_context().push()
    login_user(UserCache.get('worker'))

    CITriggerManager.fire_by_trigger(trigger, operate_type, ci_dict)


@celery.task(name="cmdb.ci_relation_cache", queue=CMDB_QUEUE)
@flush_db
@reconnect_db
def ci_relation_cache(parent_id, child_id, ancestor_ids):
    with redis_lock.Lock(rd.r, "CIRelation_{}".format(parent_id)):
        children = rd.get([parent_id], REDIS_PREFIX_CI_RELATION)[0]
        children = json.loads(children) if children is not None else {}

        cr = CIRelation.get_by(first_ci_id=parent_id, second_ci_id=child_id, ancestor_ids=ancestor_ids,
                               first=True, to_dict=False)
        if str(child_id) not in children:
            children[str(child_id)] = cr.second_ci.type_id

        rd.create_or_update({parent_id: json.dumps(children)}, REDIS_PREFIX_CI_RELATION)

        if ancestor_ids is not None:
            key = "{},{}".format(ancestor_ids, parent_id)
            grandson = rd.get([key], REDIS_PREFIX_CI_RELATION2)[0]
            grandson = json.loads(grandson) if grandson is not None else {}

            cr = CIRelation.get_by(first_ci_id=parent_id, second_ci_id=child_id, ancestor_ids=ancestor_ids,
                                   first=True, to_dict=False)
            if cr and str(cr.second_ci_id) not in grandson:
                grandson[str(cr.second_ci_id)] = cr.second_ci.type_id

            rd.create_or_update({key: json.dumps(grandson)}, REDIS_PREFIX_CI_RELATION2)

    current_app.logger.info("ADD ci relation cache: {0} -> {1}".format(parent_id, child_id))


@celery.task(name="cmdb.ci_relation_add", queue=CMDB_QUEUE)
@flush_db
@reconnect_db
def ci_relation_add(parent_dict, child_id, uid):
    """
    :param parent_dict: key is '$parent_model.attr_name'
    :param child_id:
    :param uid:
    :return:
    """
    from api.lib.cmdb.ci import CIRelationManager
    from api.lib.cmdb.ci_type import CITypeAttributeManager
    from api.lib.cmdb.search import SearchError
    from api.lib.cmdb.search.ci import search

    current_app.test_request_context().push()
    login_user(UserCache.get(uid))

    for parent in parent_dict:
        parent_ci_type_name, _attr_name = parent.strip()[1:].split('.', 1)
        attr_name = CITypeAttributeManager.get_attr_name(parent_ci_type_name, _attr_name)
        if attr_name is None:
            current_app.logger.warning("attr name {} does not exist".format(_attr_name))
            continue

        parent_dict[parent] = handle_arg_list(parent_dict[parent])
        for v in parent_dict[parent]:
            query = "_type:{},{}:{}".format(parent_ci_type_name, attr_name, v)
            s = search(query)
            try:
                response, _, _, _, _, _ = s.search()
            except SearchError as e:
                current_app.logger.error('ci relation add failed: {}'.format(e))
                continue

            for ci in response:
                try:
                    CIRelationManager.add(ci['_id'], child_id)
                    ci_relation_cache(ci['_id'], child_id)
                except Exception as e:
                    current_app.logger.warning(e)
                finally:
                    try:
                        db.session.commit()
                    except:
                        db.session.rollback()


@celery.task(name="cmdb.ci_relation_delete", queue=CMDB_QUEUE)
@reconnect_db
def ci_relation_delete(parent_id, child_id, ancestor_ids):
    with redis_lock.Lock(rd.r, "CIRelation_{}".format(parent_id)):
        children = rd.get([parent_id], REDIS_PREFIX_CI_RELATION)[0]
        children = json.loads(children) if children is not None else {}

        if str(child_id) in children:
            children.pop(str(child_id))

        rd.create_or_update({parent_id: json.dumps(children)}, REDIS_PREFIX_CI_RELATION)

        if ancestor_ids is not None:
            key = "{},{}".format(ancestor_ids, parent_id)
            grandson = rd.get([key], REDIS_PREFIX_CI_RELATION2)[0]
            grandson = json.loads(grandson) if grandson is not None else {}

            if str(child_id) in grandson:
                grandson.pop(str(child_id))

            rd.create_or_update({key: json.dumps(grandson)}, REDIS_PREFIX_CI_RELATION2)

    current_app.logger.info("DELETE ci relation cache: {0} -> {1}".format(parent_id, child_id))


@celery.task(name="cmdb.ci_type_attribute_order_rebuild", queue=CMDB_QUEUE)
@flush_db
@reconnect_db
def ci_type_attribute_order_rebuild(type_id, uid):
    current_app.logger.info('rebuild attribute order')

    from api.lib.cmdb.ci_type import CITypeAttributeGroupManager

    attrs = CITypeAttributesCache.get(type_id)
    id2attr = {attr.attr_id: attr for attr in attrs}

    current_app.test_request_context().push()
    login_user(UserCache.get(uid))

    res = CITypeAttributeGroupManager.get_by_type_id(type_id, True)
    order = 0
    for group in res:
        for _attr in group.get('attributes'):
            if order != id2attr.get(_attr['id']) and id2attr.get(_attr['id']):
                id2attr.get(_attr['id']).update(order=order)

            order += 1


@celery.task(name="cmdb.calc_computed_attribute", queue=CMDB_QUEUE)
@flush_db
@reconnect_db
def calc_computed_attribute(attr_id, uid):
    from api.lib.cmdb.ci import CIManager

    current_app.test_request_context().push()
    login_user(UserCache.get(uid))

    cim = CIManager()
    for i in CITypeAttribute.get_by(attr_id=attr_id, to_dict=False):
        cis = CI.get_by(type_id=i.type_id, to_dict=False)
        for ci in cis:
            cim.update(ci.id, {})


@celery.task(name="cmdb.write_ad_rule_sync_history", queue=CMDB_QUEUE)
@reconnect_db
def write_ad_rule_sync_history(rules, oneagent_id, oneagent_name, sync_at):
    from api.lib.cmdb.auto_discovery.auto_discovery import AutoDiscoveryRuleSyncHistoryCRUD

    for rule in rules:
        AutoDiscoveryRuleSyncHistoryCRUD().upsert(adt_id=rule['id'],
                                                  oneagent_id=oneagent_id,
                                                  oneagent_name=oneagent_name,
                                                  sync_at=sync_at,
                                                  commit=False)
    try:
        db.session.commit()
    except Exception as e:
        current_app.logger.error("write auto discovery rule sync history failed: {}".format(e))
        db.session.rollback()


@celery.task(name="cmdb.build_relations_for_ad_accept", queue=CMDB_QUEUE)
@reconnect_db
def build_relations_for_ad_accept(adc, ci_id, ad_key2attr):
    from api.lib.cmdb.ci import CIRelationManager
    from api.lib.cmdb.search import SearchError
    from api.lib.cmdb.search.ci import search as ci_search

    current_app.test_request_context().push()
    login_user(UserCache.get('worker'))

    relation_ads = AutoDiscoveryCITypeRelation.get_by(ad_type_id=adc['type_id'], to_dict=False)
    for r_adt in relation_ads:
        ad_key = r_adt.ad_key
        if not adc['instance'].get(ad_key):
            continue

        ad_key_values = [adc['instance'].get(ad_key)] if not isinstance(
            adc['instance'].get(ad_key), list) else adc['instance'].get(ad_key)
        for ad_key_value in ad_key_values:
            query = "_type:{},{}:{}".format(r_adt.peer_type_id, r_adt.peer_attr_id, ad_key_value)
            s = ci_search(query, use_ci_filter=False, count=1000000)
            try:
                response, _, _, _, _, _ = s.search()
            except SearchError as e:
                current_app.logger.error("build_relations_for_ad_accept failed: {}".format(e))
                return

            for relation_ci in response:
                relation_ci_id = relation_ci['_id']
                try:
                    CIRelationManager.add(ci_id, relation_ci_id,
                                          valid=False,
                                          source=RelationSourceEnum.AUTO_DISCOVERY)

                except:
                    try:
                        CIRelationManager.add(relation_ci_id, ci_id,
                                              valid=False,
                                              source=RelationSourceEnum.AUTO_DISCOVERY)
                    except:
                        pass

    # build relations in reverse
    relation_ads = AutoDiscoveryCITypeRelation.get_by(peer_type_id=adc['type_id'], to_dict=False)
    attr2ad_key = {v: k for k, v in ad_key2attr.items()}
    for r_adt in relation_ads:
        attr = AttributeCache.get(r_adt.peer_attr_id)
        ad_key = attr2ad_key.get(attr and attr.name)
        if not ad_key:
            continue

        ad_value = adc['instance'].get(ad_key)
        peer_ad_key = r_adt.ad_key
        peer_instances = AutoDiscoveryCI.get_by(type_id=r_adt.ad_type_id, to_dict=False)
        for peer_instance in peer_instances:
            peer_ad_values = peer_instance.instance.get(peer_ad_key)
            peer_ad_values = [peer_ad_values] if not isinstance(peer_ad_values, list) else peer_ad_values
            if ad_value in peer_ad_values and peer_instance.ci_id:
                try:
                    CIRelationManager.add(peer_instance.ci_id, ci_id,
                                          valid=False,
                                          source=RelationSourceEnum.AUTO_DISCOVERY)

                except:
                    try:
                        CIRelationManager.add(ci_id, peer_instance.ci_id,
                                              valid=False,
                                              source=RelationSourceEnum.AUTO_DISCOVERY)
                    except:
                        pass


@celery.task(name="cmdb.cost_computed_attribute", queue=CMDB_QUEUE)
@flush_db
@reconnect_db
def cost_computed_attribute(type_id, uid):
    start_time = time.time()
    from api.lib.cmdb.ci import CIManager
    cim = CIManager()

    current_app.test_request_context().push()
    login_user(UserCache.get(uid))

    cis = CI.get_by(type_id=type_id, to_dict=True)
    for ci in cis:
        cim.update(ci['id'], {}, is_cost=True)
    
    end_time = time.time()
    execution_time = end_time - start_time
    current_app.logger.info(f"Task cost_computed_attribute completed in {execution_time:.2f} seconds for type_id: {type_id}")


@celery.task(name="cmdb.generate_billing_data", queue=CMDB_QUEUE)
@reconnect_db
def generate_billing_data(source, target, path, uid):
    """异步生成计费数据的任务"""
    from api.lib.cmdb.cloud_billing import CloudBillingManager
    from api.lib.cmdb.cache import BillingCache
    
    try:
        current_app.test_request_context().push()
        login_user(UserCache.get(uid))
        
        # 处理target中的特殊查询参数
        billing_adjust_date = None
        new_query_parts = []
        target_copy = target.copy()
        type_ids = target_copy.get('type_ids', [])
        
        if isinstance(target_copy, dict) and 'q' in target_copy:
            query_parts = target_copy['q'].split(',')
            
            for part in query_parts:
                if 'JiFeiTiaoZhengRiQI:' in part:
                    # 提取纯日期部分，去除操作符
                    value = part.split(':')[1]
                    billing_adjust_date = value.lstrip('<>=')  # 移除开头的操作符
                else:
                    new_query_parts.append(part)
            # 更新target中的q
            if new_query_parts:
                target_copy['q'] = ','.join(new_query_parts)
        
        # 获取搜索结果
        search_result = CloudBillingManager.get_target_nodes_and_search(source, target_copy, path)
        
        # 检查搜索结果
        if not search_result:
            raise ValueError(f"搜索结果为空: source={source}, target={target_copy}, path={path}")
            
        paths, counter, _, _, _, id2ci, relation_types, type2show_key, type2multishow_key = search_result
        # 检查paths是否为空
        if not paths:
            raise ValueError("未找到有效路径")

        target_nodes = {path[-1] for path_list in paths.values() for path in path_list}
        if not target_nodes:
            raise ValueError("未找到目标节点")

        history_records = CloudBillingManager.get_history_records(type_ids)
        from collections import defaultdict
        
        billing_adjust_date_obj = try_parse_date(billing_adjust_date)
            
        # 使用列表推导式优化过滤逻辑
        business_history = [rec[1] for rec in history_records if rec[1]]
        
        # 添加日志记录历史记录数量
        current_app.logger.info(f"处理计费数据: 共找到 {len(business_history)} 条业务历史记录")
        
        filtered_business_history = []
        for history_list in business_history:
            # TODO: 判断本次的history_list是否一个计算属性，如果是计算属性，则跳过
            # 检查每个history_list中的item是否都有attr_name属性
            for idx, item in enumerate(history_list):
                if 'attr_name' not in item:
                    current_app.logger.error(f"发现缺少attr_name的数据项: 位置索引={idx}, 数据项={item}")
                    raise ValueError(f"发现缺少attr_name的数据项: 位置索引={idx}, 数据项={item}")
            adjust_record = next(
                (item for item in history_list if item.get('attr_name') == 'JiFeiTiaoZhengRiQI'),
                None)
            
            if adjust_record:
                try_date = try_parse_date(adjust_record.get('new'))
                if try_date is not None and (not billing_adjust_date_obj or try_date > billing_adjust_date_obj):
                    filtered_business_history.append(history_list)
        
        current_app.logger.info(f"过滤后的业务历史记录: {len(filtered_business_history)} 条")
        
        # 使用过滤后的历史记录构建分组
        ci_history_grouped = defaultdict(list)
        for history_entry in filtered_business_history:
            ci_id = str(history_entry[0]['ci_id'])
            if ci_id in target_nodes:
                ci_history_grouped[ci_id].append(history_entry)

        all_processed_paths, id2ci_copy = CloudBillingManager.process_paths_and_history(
            paths, id2ci, ci_history_grouped)

        # 2. 保存结果到缓存
        result_data = {
            'all_processed_paths': all_processed_paths,
            'id2ci_copy': id2ci_copy,
            'paths': paths,
            'relation_types': relation_types,
            'type2show_key': type2show_key,
            'type2multishow_key': type2multishow_key
        }
        
        BillingCache.set_billing_data(source, target, path, result_data)
        return True
        
    except Exception as e:
        import traceback
        error_msg = f"生成计费数据失败: {str(e)}\n"
        error_msg += f"错误堆栈:\n{traceback.format_exc()}"
        current_app.logger.error(error_msg)
        
        empty_result = {
            'all_processed_paths': [],
            'id2ci_copy': {},
            'paths': {},
            'relation_types': {},
            'type2show_key': {},
            'type2multishow_key': {}
        }
        BillingCache.set_billing_data(source, target, path, empty_result)

def try_parse_date(date_str):
    """尝试解析日期字符串为datetime对象"""
    try:
        from datetime import datetime
        return datetime.strptime(date_str, '%Y-%m-%d')
    except (ValueError, TypeError):
        return None

@celery.task(name="cmdb.batch_import_load_data", queue=CMDB_QUEUE)
@flush_db
@reconnect_db
def batch_import_load_data(type_id: int, data: list, batch_size: int, history_id: int, time_slot: str = None):
    """批量导入负载数据异步任务 - 支持时间段"""
    try:
        current_app.logger.info(f"开始异步导入负载数据: type_id={type_id}, time_slot={time_slot}")
        
        # 调用扩展后的异步导入方法
        result = LoadDataManager.batch_import_async(
            type_id=type_id,
            data=data,
            batch_size=batch_size,
            history_id=history_id,
            time_slot=time_slot
        )
        
        current_app.logger.info("异步导入负载数据完成")
        return result
        
    except Exception as e:
        current_app.logger.error(f"异步导入负载数据失败: {str(e)}")
        raise
