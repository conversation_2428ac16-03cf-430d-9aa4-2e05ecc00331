# -*- coding:utf-8 -*-

import uuid

from flask import abort, current_app, session
from ldap3 import ALL, Connection, Server
from ldap3.core.exceptions import (
    LDAPBindError,
    LDAPCertificateError,
    LDAPSocketOpenError,
)
from api.lib.common_setting.common_data import AuthenticateDataCRUD
from api.lib.common_setting.const import AuthenticateType
from api.lib.perm.acl.audit import AuditCRUD
from api.lib.perm.acl.resp_format import ErrFormat
from api.models.acl import User


def authenticate_with_ldap(username, password):
    config = AuthenticateDataCRUD(AuthenticateType.LDAP).get()

    server = Server(config.get('ldap_server'), get_info=ALL, connect_timeout=3)
    if '@' in username:
        email = username
        who = config.get('ldap_user_dn').format(username.split('@')[0])
    else:
        who = config.get('ldap_user_dn').format(username)
        email = "{}@{}".format(who, config.get('ldap_domain'))

    username = username.split('@')[0]
    user = User.query.get_by_username(username)
    # password == hashlib.md5(password.encode("utf-8")).hexdigest()

    try:
        if not password:
            raise LDAPCertificateError

        try:
            conn = Connection(server, user=who, password=password)
        except LDAPBindError:
            conn = Connection(
                server,
                user=f"{username}@{config.get('ldap_domain')}",
                password=password,
            )
        if not conn.bind():
            AuditCRUD.add_login_log(username, False, ErrFormat.invalid_password)
            raise LDAPBindError
        else:
            _id = AuditCRUD.add_login_log(username, True, ErrFormat.login_succeed)
            session['LOGIN_ID'] = _id

        if not user:
            return abort(404, ErrFormat.user_not_found.format(username))

        return user, True

    except LDAPBindError as e:
        current_app.logger.info(e)
        return user, False

    except LDAPSocketOpenError as e:
        current_app.logger.info(e)
        return abort(403, ErrFormat.ldap_connection_failed)
