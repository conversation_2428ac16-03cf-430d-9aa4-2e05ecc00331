# -*- coding:utf-8 -*-

from flask import abort
import sys
from collections import defaultdict
import time
from flask import current_app

from api.extensions import db
from api.lib.cmdb.cache import AttributeCache
from api.lib.cmdb.perms import CIFilterPermsCRUD
from api.lib.cmdb.resp_format import <PERSON>rr<PERSON>ormat
from api.lib.perm.acl.cache import UserCache
from api.models.cmdb import (
    AttributeHistory,
    OperationRecord,
)
from api.lib.cmdb.cache import CITypeCache
from api.lib.cmdb.utils import TableMap
from api.lib.cmdb.const import BillingFieldEnum, BillingStatusEnum
from api.lib.cmdb.search import SearchError
from api.lib.cmdb.search.ci_relation.search import Search
from api.lib.cmdb.cloud_history import CloudAttributeHistoryManger
from api.lib.cmdb.cache import BillingCache
from api.lib.cmdb.value import AttributeValueManager
from api.lib.cmdb.ci_type import CITypeAttributeManager
from api.lib.cmdb.attribute import AttributeManager
from api.lib.cmdb.const import ValueTypeEnum

class CloudBillingManager(object):
    @staticmethod
    def process_single_record(response_item):
        """处理单条记录的字段转换"""
        process_item = response_item.copy()
        process_item[BillingFieldEnum.PREV_BILLING_ORDER] = process_item[BillingFieldEnum.CURR_BILLING_ORDER]
        process_item[BillingFieldEnum.PREV_BILLING_DATE] = process_item[BillingFieldEnum.CURR_BILLING_DATE]
        process_item[BillingFieldEnum.CURR_BILLING_DATE] = None
        process_item[BillingFieldEnum.CURR_BILLING_ORDER] = None
        return process_item

    @staticmethod
    def process_history_record(process_item, history_entry):
        """处理历史记录条目"""
        temp_JiFeiTiaoZheng = {
            'DanHao': process_item[BillingFieldEnum.CURR_BILLING_ORDER],
            'RiQi': process_item[BillingFieldEnum.CURR_BILLING_DATE],
            'ZhuangTai': BillingStatusEnum.ADJUSTING
        }
        
        # 将 AttributeManager 实例化移到循环外部以提高性能
        attr_manager = AttributeManager()
        
        for change_record in history_entry:
            old_value = change_record['old']
            
            # 根据属性的 value_type 来决定是否进行数值转换
            attr = attr_manager.get_attribute_by_id(change_record['attr_id'])
            attr_type = attr['value_type']
            if attr and isinstance(old_value, str) and old_value.strip():
                # 只有当 value_type 为 INT 或 FLOAT 时才进行转换
                try:
                    if attr_type == ValueTypeEnum.INT:
                        old_value = int(old_value)
                    elif attr_type == ValueTypeEnum.FLOAT:
                        old_value = float(old_value)
                except (ValueError, TypeError):
                    current_app.logger.error(f"转换属性值失败: {change_record['attr_name']} {old_value}，保持原值")
                
            process_item[change_record['attr_name']] = old_value
            if change_record['attr_name'] == 'ZhuangTai':
                temp_JiFeiTiaoZheng['ZhuangTai'] = change_record['new']

        process_item.update({
            BillingFieldEnum.PREV_BILLING_ORDER: process_item[BillingFieldEnum.CURR_BILLING_ORDER],
            BillingFieldEnum.PREV_BILLING_DATE: process_item[BillingFieldEnum.CURR_BILLING_DATE],
            BillingFieldEnum.CURR_BILLING_ORDER: temp_JiFeiTiaoZheng['DanHao'],
            BillingFieldEnum.CURR_BILLING_DATE: temp_JiFeiTiaoZheng['RiQi'],
            'ZhuangTai': temp_JiFeiTiaoZheng['ZhuangTai']
        })
        return process_item

    @staticmethod
    def get_target_nodes_and_search(source, target, path):
        """获取目标节点并执行搜索"""
        search_start = time.time()
        s = Search(page=1, count=sys.maxsize)
        try:
            search_result = s.search_by_path(source, target, path)
            current_app.logger.debug(f"search_by_path耗时: {time.time() - search_start:.3f}秒")
            return search_result
        except SearchError as e:
            return abort(400, str(e))

    @staticmethod
    def get_history_records(type_ids):
        """获取历史记录"""
        history_start = time.time()
        _, res = CloudAttributeHistoryManger.get_records_for_attributes(
            None, None, None, 1, sys.maxsize,
            '2', '1', type_ids
        )
        current_app.logger.debug(f"获取历史记录耗时: {time.time() - history_start:.3f}秒")
        return res

    @classmethod
    def process_paths_and_history(cls, paths, id2ci, ci_history_grouped):
        """处理路径和历史记录"""
        process_start = time.time()
        paths_copy = paths.copy()
        id2ci_copy = id2ci.copy()
        value_manager = AttributeValueManager()
        # 预估结果大小并预分配空间
        estimated_size = sum(
            len(path_list) * (1 + len(ci_history_grouped.get(path[-1], []))) 
            for path_list in paths_copy.values() 
            for path in path_list
        )
        all_processed_paths = [(None, None)] * estimated_size
        current_idx = 0

        for path_type, path_list in paths_copy.items():
            for path in path_list:
                target_node = path[-1]
                all_processed_paths[current_idx] = (path_type, path)
                current_idx += 1

                original_record = id2ci_copy[target_node]
                if original_record['ZhuangTai'] != BillingStatusEnum.RECYCLED:
                    processed_record = cls.process_single_record(original_record)
                    id2ci_copy[target_node] = processed_record

                if target_node not in ci_history_grouped:
                    continue

                history_entries = ci_history_grouped[target_node]
                for idx, history_entry in enumerate(history_entries, 1):
                    if any(change_record['attr_name'] == 'ZhuangTai' for change_record in history_entry):
                        cls.process_history_record(id2ci_copy[target_node], history_entry)
                        original_record = id2ci_copy[target_node].copy()
                        process_instance = id2ci_copy[target_node]
                    else:
                        history_node_id = f"{target_node}-{idx}"
                        history_record = cls.process_history_record(original_record.copy(), history_entry)
                        id2ci_copy[history_node_id] = history_record
                        new_path = path[:-1] + [history_node_id]
                        all_processed_paths[current_idx] = (path_type, new_path)
                        current_idx += 1
                        original_record = history_record.copy()
                        process_instance = id2ci_copy[history_node_id]

                    
                    # 获取该CITYPE中的所有计算属性
                    ci_type = original_record['_type']
                    computed_attrs = CITypeAttributeManager.get_calculated_attributes(ci_type)
                    if computed_attrs:
                        value_manager.handle_ci_compute_attributes(process_instance, computed_attrs, None)
                    # 可以开始调试以上的代码了。
                    original_record.update({
                        BillingFieldEnum.CURR_BILLING_ORDER: original_record[BillingFieldEnum.PREV_BILLING_ORDER],
                        BillingFieldEnum.CURR_BILLING_DATE: original_record[BillingFieldEnum.PREV_BILLING_DATE]
                    })

        all_processed_paths = all_processed_paths[:current_idx]
        current_app.logger.debug(f"数据处理耗时: {time.time() - process_start:.3f}秒")
        return all_processed_paths, id2ci_copy

    @staticmethod
    def handle_pagination(all_processed_paths, id2ci_copy, paths, page, page_size):
        """处理分页逻辑"""
        page_start = time.time()
        
        total_records = len(all_processed_paths)
        total_pages = (total_records + page_size - 1) // page_size
        page = min(page, total_pages) if total_pages > 0 else 1
        
        start_idx = (page - 1) * page_size
        current_page_paths = all_processed_paths[start_idx:start_idx + page_size]
        
        final_paths = defaultdict(list)
        used_nodes = set()
        
        for path_type, path in current_page_paths:
            final_paths[path_type].append(path)
            used_nodes.update(path)
        
        final_id2ci = {node: id2ci_copy[node] for node in used_nodes}
        final_counter = {path_type: len(paths) for path_type, paths in final_paths.items()}
        
        current_app.logger.debug(f"分页处理耗时: {time.time() - page_start:.3f}秒")
        
        # 返回字典而不是元组
        return {
            'paths': dict(final_paths),
            'counter': final_counter,
            'numfound': total_records,
            'page': page,
            'id2ci': final_id2ci
        }

    @staticmethod
    def transform_path_to_data(path, id2ci):
        """将路径转换为完整数据
        Args:
            path: 路径列表，如 ["1789", "487", "2517"]
            id2ci: ID到CI数据的映射
        Returns:
            dict: 转换后的数据字典，包含所有必要字段
        """
        # 获取最后一个节点的完整数据
        result = id2ci[path[-1]].copy()
        
        # 根据路径长度添加单位和系统信息
        if len(path) == 3:
            result.update({
                'units_name': id2ci[path[0]]['units_name'],
                'system_name': id2ci[path[1]]['system_name']
            })
        elif len(path) == 2:
            result.update({
                'units_name': id2ci[path[0]]['units_name']
            })
        
        return result
