# -*- coding:utf-8 -*-

from __future__ import unicode_literals

QUERY_CIS_BY_VALUE_TABLE = """
    SELECT attr.name AS attr_name,
          attr.alias AS attr_alias,
          attr.value_type,
          attr.is_list,
          attr.is_password,
          c_cis.type_id,
          {0}.ci_id,
          {0}.attr_id,
          {0}.value
   FROM {0}
   INNER JOIN c_cis ON {0}.ci_id=c_cis.id
   AND {0}.`ci_id` IN ({1})
   INNER JOIN c_attributes as attr ON attr.id = {0}.attr_id
"""

#  {2}: value_table
QUERY_CIS_BY_IDS = """
    SELECT A.ci_id,
           A.type_id,
           A.attr_id,
           A.attr_name,
           A.attr_alias,
           A.value,
           A.value_type,
           A.is_list,
           A.is_password
    FROM
      ({1}) AS A {0}
       ORDER BY A.ci_id;
"""

FACET_QUERY1 = """
    SELECT {0}.value,
           count({0}.ci_id)
    FROM {0}
    INNER JOIN c_attributes AS attr ON attr.id={0}.attr_id
    WHERE attr.name="{1}"
    GROUP BY {0}.ci_id;
"""

FACET_QUERY = """
    SELECT {0}.value,
           count(distinct {0}.ci_id) as count,
           attr_id
    FROM {0}
    INNER JOIN ({1}) as CI_RESULT ON CI_RESULT.ci_id = {0}.ci_id
    WHERE {0}.attr_id = {2}
    GROUP BY {0}.value
"""

# 新增单字段直接聚合的查询模板（不分组，直接聚合）
DIRECT_AGGREGATION_QUERY = """
    SELECT {3}({0}.value) as aggregation_value
    FROM {0} INNER JOIN ({1}) as CI_RESULT ON CI_RESULT.ci_id = {0}.ci_id
    WHERE {0}.attr_id = {2}
"""

# 新增一级分组聚合查询模板（按一个字段分组后对另一个字段聚合）
GROUP_AGGREGATION_QUERY_L1 = """
    SELECT group_table.value as group_value, {3}(value_table.value) as aggregation_value, group_table.attr_id as group_attr_id
    FROM {0} as group_table
    INNER JOIN ({1}) as CI_RESULT ON CI_RESULT.ci_id = group_table.ci_id
    INNER JOIN {4} as value_table ON value_table.ci_id = CI_RESULT.ci_id
    WHERE group_table.attr_id = {2} AND value_table.attr_id = {5}
    GROUP BY group_table.value
"""

# 新增二级分组聚合查询模板（按两个字段分组后对第三个字段聚合）
GROUP_AGGREGATION_QUERY_L2 = """
    SELECT g1.value as group1_value, g2.value as group2_value, {3}(value_table.value) as aggregation_value,
           g1.attr_id as group1_attr_id, g2.attr_id as group2_attr_id
    FROM {0} as g1
    INNER JOIN ({1}) as CI_RESULT ON CI_RESULT.ci_id = g1.ci_id
    INNER JOIN {4} as g2 ON g2.ci_id = CI_RESULT.ci_id
    INNER JOIN {6} as value_table ON value_table.ci_id = CI_RESULT.ci_id
    WHERE g1.attr_id = {2} AND g2.attr_id = {5} AND value_table.attr_id = {7}
    GROUP BY g1.value, g2.value
"""

QUERY_CI_BY_ATTR_NAME = """
    SELECT {0}.ci_id
    FROM {0}
    WHERE {0}.attr_id={1:d}
      AND ({0}.value {2})
"""

QUERY_CI_BY_ID = """
    SELECT c_cis.id as ci_id
    FROM c_cis
    WHERE c_cis.id {}
"""

QUERY_CI_BY_TYPE = """
    SELECT c_cis.id AS ci_id
    FROM c_cis
    WHERE c_cis.type_id in ({0})
"""

QUERY_UNION_CI_ATTRIBUTE_IS_NULL = """
    SELECT *
    FROM (
      SELECT c_cis.id AS ci_id
      FROM c_cis
      WHERE c_cis.type_id IN ({0})
    ) {3}
      LEFT JOIN (
        SELECT {1}.ci_id
        FROM {1}
        WHERE {1}.attr_id = {2}
          AND {1}.value LIKE "%"
      ) {4} USING (ci_id)
    WHERE {4}.ci_id IS NULL
"""

QUERY_CI_BY_NO_ATTR = """
SELECT *
FROM
    (SELECT c_value_index_texts.ci_id
    FROM c_value_index_texts
    WHERE c_value_index_texts.value LIKE "{0}"
    UNION
    SELECT c_value_index_integers.ci_id
    FROM c_value_index_integers
    WHERE c_value_index_integers.value LIKE "{0}"
    UNION
    SELECT c_value_index_floats.ci_id
    FROM c_value_index_floats
    WHERE c_value_index_floats.value LIKE "{0}"
    UNION
    SELECT c_value_index_datetime.ci_id
    FROM c_value_index_datetime
    WHERE c_value_index_datetime.value LIKE "{0}") AS {1}
GROUP BY  {1}.ci_id
"""

QUERY_CI_BY_NO_ATTR_IN = """
SELECT *
FROM
    (SELECT c_value_index_texts.ci_id
    FROM c_value_index_texts
    WHERE c_value_index_texts.value in ({0})) AS {1}
GROUP BY {1}.ci_id
"""

# QUERY_CI_BY_NO_ATTR_IN = """
# SELECT *
# FROM
#     (SELECT c_value_index_texts.ci_id
#     FROM c_value_index_texts
#     WHERE c_value_index_texts.value in ({0})
#     UNION
#     SELECT c_value_index_integers.ci_id
#     FROM c_value_index_integers
#     WHERE c_value_index_integers.value in ({0})
#     UNION
#     SELECT c_value_index_floats.ci_id
#     FROM c_value_index_floats
#     WHERE c_value_index_floats.value in ({0})
#     UNION
#     SELECT c_value_index_datetime.ci_id
#     FROM c_value_index_datetime
#     WHERE c_value_index_datetime.value in ({0})) AS {1}
# GROUP BY {1}.ci_id
# """
