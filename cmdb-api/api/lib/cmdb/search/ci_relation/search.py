# -*- coding:utf-8 -*-
from collections import Counter
from collections import defaultdict

import copy
import json
import networkx as nx
import sys
import time
from flask import abort
from flask import current_app
from flask_login import current_user

from api.extensions import rd
from api.lib.cmdb.cache import AttributeCache
from api.lib.cmdb.cache import CITypeCache
from api.lib.cmdb.ci import CIRelationManager
from api.lib.cmdb.ci_type import CITypeRelationManager
from api.lib.cmdb.const import ConstraintEnum
from api.lib.cmdb.const import PermEnum
from api.lib.cmdb.const import REDIS_PREFIX_CI_RELATION
from api.lib.cmdb.const import REDIS_PREFIX_CI_RELATION2
from api.lib.cmdb.const import ResourceTypeEnum
from api.lib.cmdb.perms import CIFilterPermsCRUD
from api.lib.cmdb.resp_format import ErrFormat
from api.lib.cmdb.search.ci.db.search import Search as SearchFromDB
from api.lib.cmdb.search.ci.es.search import Search as SearchFromES
from api.lib.cmdb.utils import TableMap
from api.lib.cmdb.utils import ValueTypeMap
from api.lib.perm.acl.acl import ACLManager
from api.lib.perm.acl.acl import is_app_admin
from api.models.cmdb import CI
from api.models.cmdb import CITypeRelation
from api.models.cmdb import RelationType


class Search(object):
    def __init__(self, root_id=None,
                 level=None,
                 query=None,
                 fl=None,
                 facet_field=None,
                 page=1,
                 count=None,
                 sort=None,
                 reverse=False,
                 ancestor_ids=None,
                 descendant_ids=None,
                 has_m2m=None,
                 root_parent_path=None):
        self.orig_query = query
        self.fl = fl
        self.facet_field = facet_field
        self.page = page
        self.count = count or current_app.config.get("DEFAULT_PAGE_COUNT")
        self.sort = sort or ("ci_id" if current_app.config.get("USE_ES") else None)

        self.root_id = root_id
        self.level = level or 0
        self.reverse = reverse

        self.level2constraint = CITypeRelationManager.get_level2constraint(
            root_id[0] if root_id and isinstance(root_id, list) else root_id,
            level[0] if isinstance(level, list) and level else level)

        self.ancestor_ids = ancestor_ids
        self.descendant_ids = descendant_ids
        self.root_parent_path = root_parent_path
        self.has_m2m = has_m2m or False
        if not self.has_m2m:
            if self.ancestor_ids:
                self.has_m2m = True
            else:
                level = level[0] if isinstance(level, list) and level else level
                for _l, c in self.level2constraint.items():
                    if _l < int(level) and c == ConstraintEnum.Many2Many:
                        self.has_m2m = True

        self.type2filter_perms = {}

        self.is_app_admin = is_app_admin('cmdb') or current_user.username == "worker"

    def _get_ids(self, ids):

        merge_ids = []
        key = []
        _tmp = []
        for level in range(1, sorted(self.level)[-1] + 1):
            if len(self.descendant_ids or []) >= level and self.type2filter_perms.get(self.descendant_ids[level - 1]):
                id_filter_limit, _ = self._get_ci_filter(self.type2filter_perms[self.descendant_ids[level - 1]])
            else:
                id_filter_limit = {}

            if not self.has_m2m:
                key, prefix = list(map(str, ids)), REDIS_PREFIX_CI_RELATION

            else:
                if not self.ancestor_ids:
                    if level == 1:
                        key, prefix = list(map(str, ids)), REDIS_PREFIX_CI_RELATION
                    else:
                        key = list(set(["{},{}".format(i, j) for idx, i in enumerate(key) for j in _tmp[idx]]))
                        prefix = REDIS_PREFIX_CI_RELATION2
                else:
                    if level == 1:
                        key, prefix = ["{},{}".format(self.ancestor_ids, i) for i in ids], REDIS_PREFIX_CI_RELATION2
                    else:
                        key = list(set(["{},{}".format(i, j) for idx, i in enumerate(key) for j in _tmp[idx]]))
                        prefix = REDIS_PREFIX_CI_RELATION2

            if not key or id_filter_limit is None:
                return []

            res = [json.loads(x).items() for x in [i or '{}' for i in rd.get(key, prefix) or []]]
            _tmp = [[i[0] for i in x if (not id_filter_limit or (
                    key[idx] not in id_filter_limit or int(i[0]) in id_filter_limit[key[idx]]) or
                                         int(i[0]) in id_filter_limit)] for idx, x in enumerate(res)]

            ids = [j for i in _tmp for j in i]

            if level in self.level:
                merge_ids.extend(ids)

        return merge_ids

    def _get_reverse_ids(self, ids):
        merge_ids = []
        level2ids = {}
        for level in range(1, sorted(self.level)[-1] + 1):
            ids, _level2ids = CIRelationManager.get_ancestor_ids(ids, 1)

            if _level2ids.get(2):
                level2ids[level + 1] = _level2ids[2]

            if level in self.level:
                if level in level2ids and level2ids[level]:
                    merge_ids.extend(set(ids) & set(level2ids[level]))
                else:
                    merge_ids.extend(ids)

        return merge_ids

    def _has_read_perm_from_parent_nodes(self):
        self.root_parent_path = list(map(str, self.root_parent_path))
        if str(self.root_id).isdigit() and str(self.root_id) not in self.root_parent_path:
            self.root_parent_path.append(str(self.root_id))
        self.root_parent_path = set(self.root_parent_path)

        if self.is_app_admin:
            self.type2filter_perms = {}
            return True

        res = ACLManager().get_resources(ResourceTypeEnum.CI_FILTER) or {}
        self.type2filter_perms = CIFilterPermsCRUD().get_by_ids(list(map(int, [i['name'] for i in res]))) or {}
        for _, filters in self.type2filter_perms.items():
            if set((filters.get('id_filter') or {}).keys()) & self.root_parent_path:
                return True

        return True

    def search(self, only_ids=False):
        use_ci_filter = len(self.descendant_ids or []) == self.level[0] - 1
        parent_node_perm_passed = not self.is_app_admin and self._has_read_perm_from_parent_nodes()

        ids = [self.root_id] if not isinstance(self.root_id, list) else self.root_id
        cis = [CI.get_by_id(_id) or abort(404, ErrFormat.ci_not_found.format("id={}".format(_id))) for _id in ids]

        merge_ids = self._get_ids(ids) if not self.reverse else self._get_reverse_ids(ids)

        if not self.orig_query or ("_type:" not in self.orig_query
                                   and "type_id:" not in self.orig_query
                                   and "ci_type:" not in self.orig_query):
            type_ids = []
            for level in self.level:
                for ci in cis:
                    if not self.reverse:
                        type_ids.extend(CITypeRelationManager.get_child_type_ids(ci.type_id, level))
                    else:
                        type_ids.extend(CITypeRelationManager.get_parent_type_ids(ci.type_id, level))
            type_ids = set(type_ids)
            if self.orig_query:
                self.orig_query = "_type:({0}),{1}".format(";".join(map(str, type_ids)), self.orig_query)
            else:
                self.orig_query = "_type:({0})".format(";".join(map(str, type_ids)))

        if not merge_ids:
            # cis, counter, total, self.page, numfound, facet_
            return [], {}, 0, self.page, 0, {}

        if current_app.config.get("USE_ES"):
            return SearchFromES(self.orig_query,
                                fl=self.fl,
                                facet_field=self.facet_field,
                                page=self.page,
                                count=self.count,
                                sort=self.sort,
                                ci_ids=merge_ids).search()
        else:
            return SearchFromDB(self.orig_query,
                                fl=self.fl,
                                facet_field=self.facet_field,
                                page=self.page,
                                count=self.count,
                                sort=self.sort,
                                ci_ids=merge_ids,
                                parent_node_perm_passed=parent_node_perm_passed,
                                use_ci_filter=use_ci_filter,
                                only_ids=only_ids).search()

    def _get_ci_filter(self, filter_perms, ci_filters=None):
        ci_filters = ci_filters or []
        if ci_filters:
            result = {}
            for item in ci_filters:
                res = SearchFromDB('_type:{},{}'.format(item['type_id'], item['ci_filter']),
                                   count=sys.maxsize, parent_node_perm_passed=True).get_ci_ids()
                if res:
                    result[item['type_id']] = set(res)

            return {}, result if result else None

        result = dict()
        if filter_perms.get('id_filter'):
            for k in filter_perms['id_filter']:
                node_path = k.split(',')
                if len(node_path) == 1:
                    result[int(node_path[0])] = 1
                elif not self.has_m2m:
                    result.setdefault(node_path[-2], set()).add(int(node_path[-1]))
                else:
                    result.setdefault(','.join(node_path[:-1]), set()).add(int(node_path[-1]))
            if result:
                return result, None
            else:
                return None, None

        return {}, None

    def statistics(self, type_ids, need_filter=True):
        self.level = int(self.level)

        acl = ACLManager('cmdb')

        type2filter_perms = dict()
        if not self.is_app_admin:
            res2 = acl.get_resources(ResourceTypeEnum.CI_FILTER)
            if res2:
                type2filter_perms = CIFilterPermsCRUD().get_by_ids(list(map(int, [i['name'] for i in res2])))

        ids = [self.root_id] if not isinstance(self.root_id, list) else self.root_id
        _tmp, tmp_res = [], []
        level2ids = {}
        for lv in range(1, self.level + 1):
            level2ids[lv] = []

            if need_filter:
                id_filter_limit, ci_filter_limit = None, None
                if len(self.descendant_ids or []) >= lv and type2filter_perms.get(self.descendant_ids[lv - 1]):
                    id_filter_limit, _ = self._get_ci_filter(type2filter_perms[self.descendant_ids[lv - 1]])
                elif type_ids and self.level == lv:
                    ci_filters = [type2filter_perms[type_id] for type_id in type_ids if type_id in type2filter_perms]
                    if ci_filters:
                        id_filter_limit, ci_filter_limit = self._get_ci_filter({}, ci_filters=ci_filters)
                    else:
                        id_filter_limit = {}
                else:
                    id_filter_limit = {}
            else:
                id_filter_limit, ci_filter_limit = {}, {}

            if lv == 1:
                if not self.has_m2m:
                    key, prefix = [str(i) for i in ids], REDIS_PREFIX_CI_RELATION
                else:
                    key = ["{},{}".format(self.ancestor_ids, _id) for _id in ids]
                    if not self.ancestor_ids:
                        key, prefix = [str(i) for i in ids], REDIS_PREFIX_CI_RELATION
                    else:
                        prefix = REDIS_PREFIX_CI_RELATION2

                    level2ids[lv] = [[i] for i in key]

                if not key or id_filter_limit is None:
                    _tmp = [[]] * len(ids)
                    continue

                res = [json.loads(x).items() for x in [i or '{}' for i in rd.get(key, prefix) or []]]
                _tmp = []
                if type_ids and lv == self.level:
                    _tmp = [[i for i in x if i[1] in type_ids and
                             (not id_filter_limit or (key[idx] not in id_filter_limit or
                                                      int(i[0]) in id_filter_limit[key[idx]]) or
                              int(i[0]) in id_filter_limit)] for idx, x in enumerate(res)]
                else:
                    _tmp = [[i for i in x if (not id_filter_limit or (key[idx] not in id_filter_limit or
                                                                      int(i[0]) in id_filter_limit[key[idx]]) or
                                              int(i[0]) in id_filter_limit)] for idx, x in enumerate(res)]

                if ci_filter_limit:
                    _tmp = [[j for j in i if j[1] not in ci_filter_limit or int(j[0]) in ci_filter_limit[j[1]]]
                            for i in _tmp]

            else:

                for idx, item in enumerate(_tmp):
                    if item:
                        if not self.has_m2m:
                            key, prefix = [i[0] for i in item], REDIS_PREFIX_CI_RELATION
                        else:
                            key = list(set(['{},{}'.format(j, i[0]) for i in item for j in level2ids[lv - 1][idx]]))
                            prefix = REDIS_PREFIX_CI_RELATION2

                            level2ids[lv].append(key)

                        if key:
                            res = [json.loads(x).items() for x in [i or '{}' for i in rd.get(key, prefix) or []]]
                            if type_ids and lv == self.level:
                                tmp_res = [[i for i in x if i[1] in type_ids and
                                            (not id_filter_limit or (
                                                    key[idx] not in id_filter_limit or
                                                    int(i[0]) in id_filter_limit[key[idx]]) or
                                             int(i[0]) in id_filter_limit)] for idx, x in enumerate(res)]
                            else:
                                tmp_res = [[i for i in x if (not id_filter_limit or (
                                        key[idx] not in id_filter_limit or
                                        int(i[0]) in id_filter_limit[key[idx]]) or
                                                             int(i[0]) in id_filter_limit)] for idx, x in
                                           enumerate(res)]

                            if ci_filter_limit:
                                tmp_res = [[j for j in i if j[1] not in ci_filter_limit or
                                            int(j[0]) in ci_filter_limit[j[1]]] for i in tmp_res]
                        else:
                            tmp_res = []

                        if tmp_res:
                            _tmp[idx] = [j for i in tmp_res for j in i]
                    else:
                        _tmp[idx] = []
                        level2ids[lv].append([])

        result = {str(_id): len(_tmp[idx]) for idx, _id in enumerate(ids)}

        result.update(
            detail={str(_id): dict(Counter([i[1] for i in _tmp[idx]]).items()) for idx, _id in enumerate(ids)})

        return result

    def search_full(self, type_ids):
        def _get_id2name(_type_id):
            ci_type = CITypeCache.get(_type_id)

            attr = AttributeCache.get(ci_type.unique_id)
            value_table = TableMap(attr=attr).table
            serializer = ValueTypeMap.serialize[attr.value_type]
            unique_value = {i.ci_id: serializer(i.value) for i in value_table.get_by(attr_id=attr.id, to_dict=False)}

            attr = AttributeCache.get(ci_type.show_id) # 疑似问题
            if attr:
                value_table = TableMap(attr=attr).table
                serializer = ValueTypeMap.serialize[attr.value_type]
                show_value = {i.ci_id: serializer(i.value) for i in value_table.get_by(attr_id=attr.id, to_dict=False)}
            else:
                show_value = unique_value

            return show_value, unique_value

        self.level = int(self.level)

        acl = ACLManager('cmdb')

        type2filter_perms = dict()
        if not self.is_app_admin:
            res2 = acl.get_resources(ResourceTypeEnum.CI_FILTER)
            if res2:
                type2filter_perms = CIFilterPermsCRUD().get_by_ids(list(map(int, [i['name'] for i in res2])))

        ids = [self.root_id] if not isinstance(self.root_id, list) else self.root_id

        level_ids = [str(i) for i in ids] # 疑似问题
        result = []
        id2children = {}
        id2name = _get_id2name(type_ids[0])
        for i in level_ids:
            item = dict(id=int(i),
                        type_id=type_ids[0],
                        isLeaf=False,
                        title=id2name[0].get(int(i)),
                        uniqueValue=id2name[1].get(int(i)),
                        children=[])
            result.append(item)
            id2children[str(i)] = item['children']

        for lv in range(1, self.level):
            type_id = type_ids[lv]
            
            if len(type_ids or []) >= lv and type2filter_perms.get(type_id):
                id_filter_limit, _ = self._get_ci_filter(type2filter_perms[type_id])
            else:
                id_filter_limit = {}

            if self.has_m2m and lv != 1:
                key, prefix = [i for i in level_ids], REDIS_PREFIX_CI_RELATION2
            else:
                key, prefix = [i.split(',')[-1] for i in level_ids], REDIS_PREFIX_CI_RELATION
            
            res = [json.loads(x).items() for x in [i or '{}' for i in rd.get(key, prefix) or []]]
            res = [[i for i in x if i[1] == type_id and (not id_filter_limit or (key[idx] not in id_filter_limit or
                                                             int(i[0]) in id_filter_limit[key[idx]]) or
                                     int(i[0]) in id_filter_limit)] for idx, x in enumerate(res)]
            _level_ids = []
            id2name = _get_id2name(type_id)
            for idx, node_path in enumerate(level_ids):
                for child_id, _ in (res[idx] or []):
                    item = dict(id=int(child_id),
                                type_id=type_id,
                                isLeaf=True if lv == self.level - 1 else False,
                                title=id2name[0].get(int(child_id)),
                                uniqueValue=id2name[1].get(int(child_id)),
                                children=[])
                    id2children[node_path].append(item)

                    _node_path = "{},{}".format(node_path, child_id)
                    _level_ids.append(_node_path)
                    id2children[_node_path] = item['children']

            level_ids = _level_ids

        return result

    @staticmethod
    def _get_src_ids(src):
        q = src.get('q') or ''
        if not q.startswith('_type:'):
            q = "_type:{},{}".format(src['type_id'], q)

        return SearchFromDB(q, use_ci_filter=True, only_ids=True, count=100000).search()

    @staticmethod
    def _filter_target_ids(target_ids, type_ids, q):
        if not q.startswith('_type:'):
            q = "_type:({}),{}".format(";".join(map(str, type_ids)), q)

        ci_ids = SearchFromDB(q, ci_ids=target_ids, use_ci_filter=True, only_ids=True, count=100000).search()
        cis = CI.get_by(fl=['id', 'type_id'], only_query=True).filter(CI.id.in_(ci_ids))

        return [(str(i.id), i.type_id) for i in cis]

    @staticmethod
    def _path2level(src_type_id, target_type_ids, path):
        if not src_type_id or not target_type_ids:
            return abort(400, ErrFormat.relation_path_search_src_target_required)

        graph = nx.DiGraph()
        graph.add_edges_from([(n, _path[idx + 1]) for _path in path for idx, n in enumerate(_path[:-1])])
        relation_types = defaultdict(dict)
        level2type = defaultdict(set)
        type2show_key = dict()
        type2multishow_key = dict()
        for _path in path:
            for idx, node in enumerate(_path[1:]):
                level2type[idx + 1].add(node)

                src = CITypeCache.get(_path[idx])
                target = CITypeCache.get(node)
                relation_type = RelationType.get_by(only_query=True).join(
                    CITypeRelation, CITypeRelation.relation_type_id == RelationType.id).filter(
                    CITypeRelation.parent_id == src.id).filter(CITypeRelation.child_id == target.id).first()
                relation_types[src.alias].update({target.alias: relation_type.name})

                if src.id not in type2show_key:
                    type2show_key[src.id] = AttributeCache.get(src.show_id or src.unique_id).name
                if target.id not in type2show_key:
                    type2show_key[target.id] = AttributeCache.get(target.show_id or target.unique_id).name
                    
                if src.id not in type2multishow_key:
                    if src.show_ids:
                        type2multishow_key[src.id] = [AttributeCache.get(i).name for i in src.show_ids]
                        
        nodes = graph.nodes()

        return level2type, list(nodes), relation_types, type2show_key, type2multishow_key

    def _build_graph(self, source_ids, source_type_id, level2type, target_type_ids, acl):
        type2filter_perms = dict()
        if not self.is_app_admin:
            res2 = acl.get_resources(ResourceTypeEnum.CI_FILTER)
            if res2:
                type2filter_perms = CIFilterPermsCRUD().get_by_ids(list(map(int, [i['name'] for i in res2])))

        target_type_ids = set(target_type_ids)
        graph = nx.DiGraph()
        target_ids = []
        
        # 预先构建id_filter_limit字典,避免重复计算
        id_filter_limits = {}
        for level in level2type:
            filter_type_ids = level2type[level]
            id_filter_limit = dict()
            for _type_id in filter_type_ids:
                if type2filter_perms.get(_type_id):
                    _id_filter_limit, _ = self._get_ci_filter(type2filter_perms[_type_id])
                    id_filter_limit.update(_id_filter_limit)
            id_filter_limits[level] = id_filter_limit

        # 批量获取Redis数据
        key = [(str(i), source_type_id) for i in source_ids]
        graph.add_nodes_from(key)

        for level in level2type:
            filter_type_ids = level2type[level]
            id_filter_limit = id_filter_limits[level]
            has_target = filter_type_ids & target_type_ids

            # 批量获取Redis数据
            redis_keys = [i[0] for i in key]
            redis_result = rd.get(redis_keys, REDIS_PREFIX_CI_RELATION)
            
            res = [json.loads(x).items() if x else {} for x in (redis_result or [])]
            _key = []
            
            # 优化内部循环
            for idx, (_id, items) in enumerate(zip(key, res)):
                # 使用列表推导式优化过滤逻辑
                valid_targets = [
                    (i[0], i[1]) for i in items 
                    if i[1] in filter_type_ids and (
                        not id_filter_limit or 
                        int(i[0]) in id_filter_limit.get(_id[0], set()) or
                        int(i[0]) in id_filter_limit
                    )
                ]
                
                if valid_targets:
                    _key.extend(valid_targets)
                    # 批量添加边
                    graph.add_edges_from(zip([_id] * len(valid_targets), valid_targets))

            if has_target:
                # 使用生成器表达式优化内存使用
                target_ids.extend(i[0] for i in _key if i[1] in target_type_ids)

            key = _key

        return graph, target_ids

    @staticmethod
    def _find_paths(graph, source_ids, source_type_id, target_ids, valid_path, max_depth=6):
        paths = []
        target_ids = set(target_ids)  # 转换为集合提高查找效率
        
        # 如果source_ids为空，直接返回空列表
        if not source_ids:
            current_app.logger.warning("No source_ids provided for path finding")
            return paths
        
        # 使用BFS而不是DFS来查找路径,可以更快找到最短路径
        def bfs_paths(source, targets):
            queue = [(source, [source])]
            while queue:
                (vertex, path) = queue.pop(0)
                for next_vertex in graph[vertex]:
                    if len(path) > max_depth:
                        continue
                        
                    if next_vertex in targets:
                        if tuple([i[1] for i in path + [next_vertex]]) in valid_path:
                            yield [i[0] for i in path + [next_vertex]]
                    else:
                        new_path = path + [next_vertex]
                        queue.append((next_vertex, new_path))

        # 确保max_workers至少为1
        max_workers = max(1, min(len(source_ids), 10))
        
        # 并行处理多个源节点
        from concurrent.futures import ThreadPoolExecutor
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = []
            for source_id in source_ids:
                source = (source_id, source_type_id)
                futures.append(executor.submit(lambda s: list(bfs_paths(s, target_ids)), source))
                
            for future in futures:
                paths.extend(future.result())

        return paths

    @staticmethod
    def _wrap_path_result(paths, types, valid_path, target_types, type2show_key):
        ci_ids = [j for i in paths for j in i]

        response, _, _, _, _, _ = SearchFromDB("_type:({})".format(";".join(map(str, types))),
                                               use_ci_filter=False,
                                               ci_ids=list(map(int, ci_ids)),
                                               count=1000000).search()
        
        id2ci = {str(i.get('_id')): i if i['_type'] in target_types else {
            type2show_key[i['_type']]: i[type2show_key[i['_type']]],
            "ci_type_alias": i["ci_type_alias"],
            "_type": i["_type"],
        } for i in response}
        id2ci = {str(i.get('_id')): i for i in response}

        result = defaultdict(list)
        counter = defaultdict(int)

        for path in paths:
            key = "-".join([id2ci.get(i, {}).get('ci_type_alias') or '' for i in path])
            if tuple([id2ci.get(i, {}).get('_type') for i in path]) in valid_path:
                counter[key] += 1
                result[key].append(path)

        return result, counter, id2ci

    def search_by_path(self, source, target, path):
        """

        :param source: {type_id: id, q: expr}
        :param target: {type_ids: [id], q: expr}
        :param path: [source_type_id, ..., target_type_id], use type id
        :return:
        """
        current_app.logger.debug(f"开始执行search_by_path, source={source}, target={target}, path={path}")
        start_time = time.time()

        acl = ACLManager('cmdb')
        if not self.is_app_admin:
            res = {i['name'] for i in acl.get_resources(ResourceTypeEnum.CI_TYPE)}
            for type_id in (source.get('type_id') and [source['type_id']] or []) + (target.get('type_ids') or []):
                _type = CITypeCache.get(type_id)
                if _type and _type.name not in res:
                    return abort(403, ErrFormat.no_permission.format(_type.alias, PermEnum.READ))

        target['type_ids'] = [i[-1] for i in path]
        level2type, types, relation_types, type2show_key, type2multishow_key = self._path2level(
            source.get('type_id'), target.get('type_ids'), path)
        if not level2type:
            return [], {}, 0, self.page, 0, {}, {}

        current_app.logger.debug(f"获取path2level耗时: {time.time() - start_time:.3f}秒")
        path_level_time = time.time()

        source_ids = self._get_src_ids(source)
        current_app.logger.debug(f"获取source_ids耗时: {time.time() - path_level_time:.3f}秒, source_ids数量: {len(source_ids)}")
        source_ids_time = time.time()

        if not source_ids:
            current_app.logger.warning(f"No source_ids found for source: {source}")
            return [], {}, 0, self.page, 0, {}, {}, {}, {}

        graph, target_ids = self._build_graph(source_ids, source['type_id'], level2type, target['type_ids'], acl)
        current_app.logger.debug(f"构建图耗时: {time.time() - source_ids_time:.3f}秒, 图节点数: {len(graph.nodes)}, 边数: {len(graph.edges)}")
        build_graph_time = time.time()

        target_ids = self._filter_target_ids(target_ids, target['type_ids'], target.get('q') or '')
        current_app.logger.debug(f"过滤target_ids耗时: {time.time() - build_graph_time:.3f}秒, 过滤后target_ids数量: {len(target_ids)}")
        filter_time = time.time()

        paths = self._find_paths(graph,
                                 source_ids,
                                 source['type_id'],
                                 set(target_ids),
                                 {tuple(i): 1 for i in path})

        current_app.logger.debug(f"查找路径耗时: {time.time() - filter_time:.3f}秒, 找到路径数量: {len(paths)}")
        find_paths_time = time.time()

        numfound = len(paths)
        paths = paths[(self.page - 1) * self.count:self.page * self.count]
        response, counter, id2ci = self._wrap_path_result(paths,
                                                          types,
                                                          {tuple(i): 1 for i in path},
                                                          set(target.get('type_ids') or []),
                                                          type2show_key)

        current_app.logger.debug(f"包装结果耗时: {time.time() - find_paths_time:.3f}秒")
        current_app.logger.debug(f"search_by_path总耗时: {time.time() - start_time:.3f}秒")

        return response, counter, len(paths), self.page, numfound, id2ci, relation_types, type2show_key, type2multishow_key

    def search_missing_path(self, source, target, path):
        """
        查找路径中缺失关系的CI数据
        
        :param source: {type_id: id, q: expr}
        :param target: {type_ids: [id], q: expr}
        :param path: [source_type_id, ..., target_type_id], use type id
        :return: 缺失关系的路径信息
        """
        current_app.logger.debug(f"开始执行search_missing_path, source={source}, target={target}, path={path}")
        start_time = time.time()

        acl = ACLManager('cmdb')
        if not self.is_app_admin:
            res = {i['name'] for i in acl.get_resources(ResourceTypeEnum.CI_TYPE)}
            for type_id in (source.get('type_id') and [source['type_id']] or []) + (target.get('type_ids') or []):
                _type = CITypeCache.get(type_id)
                if _type and _type.name not in res:
                    return abort(403, ErrFormat.no_permission.format(_type.alias, PermEnum.READ))

        # 对于缺失关系搜索，我们需要构建到倒数第二层的路径
        # 检查每个路径的长度，至少需要2层才能有缺失关系
        valid_paths = [p for p in path if len(p) >= 2]
        if not valid_paths:
            return [], {}, 0, self.page, 0, {}, {}, {}, {}
            
        # 对于两级路径的情况，直接查找缺失的直接关系
        if all(len(p) == 2 for p in valid_paths):
            # 所有路径都是两级的，直接查找缺失关系
            missing_paths = []
            for single_path in valid_paths:
                source_type_id, target_type_id = single_path[0], single_path[1]
                if source_type_id == source.get('type_id'):
                    paths = self._find_missing_direct_relations(
                        self._get_src_ids(source), 
                        source_type_id, 
                        target_type_id, 
                        target.get('q', '')
                    )
                    missing_paths.extend(paths)
            
            # 为两级缺失关系构建返回数据
            numfound = len(missing_paths)
            missing_paths = missing_paths[(self.page - 1) * self.count:self.page * self.count]
            
            # 创建基本的返回数据结构
            if missing_paths:
                # 获取源类型信息
                src_type = CITypeCache.get(source.get('type_id'))
                response = {src_type.alias: missing_paths}
                counter = {src_type.alias: len(missing_paths)}
                
                # 获取CI详细信息
                ci_ids = [int(path[0]) for path in missing_paths]
                if ci_ids:
                    ci_response, _, _, _, _, _ = SearchFromDB(
                        f"_type:{source.get('type_id')}", 
                        use_ci_filter=False, 
                        ci_ids=ci_ids, 
                        count=1000000
                    ).search()
                    id2ci = {str(i.get('_id')): i for i in ci_response}
                else:
                    id2ci = {}
                    
                # 获取类型显示字段
                type2show_key = {source.get('type_id'): AttributeCache.get(src_type.show_id or src_type.unique_id).name}
                type2multishow_key = {}
                if src_type.show_ids:
                    type2multishow_key[source.get('type_id')] = [AttributeCache.get(i).name for i in src_type.show_ids]
                
                relation_types = {}
            else:
                response, counter, id2ci, type2show_key, type2multishow_key, relation_types = {}, {}, {}, {}, {}, {}
            
            current_app.logger.debug(f"search_missing_path总耗时: {time.time() - start_time:.3f}秒")
            return response, counter, len(missing_paths), self.page, numfound, id2ci, relation_types, type2show_key, type2multishow_key
        
        # 对于多级路径，构建前N-1层的路径用于搜索
        partial_paths = [p[:-1] for p in valid_paths]
        target['type_ids'] = [p[-2] for p in valid_paths]  # 倒数第二层作为目标
        
        level2type, types, relation_types, type2show_key, type2multishow_key = self._path2level(
            source.get('type_id'), target.get('type_ids'), partial_paths)
        if not level2type:
            return [], {}, 0, self.page, 0, {}, {}, {}, {}

        current_app.logger.debug(f"获取partial_path2level耗时: {time.time() - start_time:.3f}秒")
        path_level_time = time.time()

        source_ids = self._get_src_ids(source)
        current_app.logger.debug(f"获取source_ids耗时: {time.time() - path_level_time:.3f}秒, source_ids数量: {len(source_ids)}")
        source_ids_time = time.time()

        if not source_ids:
            current_app.logger.warning(f"No source_ids found for source: {source}")
            return [], {}, 0, self.page, 0, {}, {}, {}, {}

        # 构建前N-1层的完整关系图
        partial_graph, penultimate_ids = self._build_graph(source_ids, source['type_id'], level2type, target['type_ids'], acl)
        current_app.logger.debug(f"构建部分图耗时: {time.time() - source_ids_time:.3f}秒, 图节点数: {len(partial_graph.nodes)}, 边数: {len(partial_graph.edges)}")
        build_graph_time = time.time()

        # 对于多级路径，查找前N-1层有关系，但与最后一层缺失关系的情况
        valid_path_patterns = {tuple(p): 1 for p in partial_paths}
        
        # 将penultimate_ids转换为(ci_id, type_id)元组格式
        penultimate_target_ids = []
        for p_id in penultimate_ids:
            for target_type_id in target['type_ids']:
                penultimate_target_ids.append((str(p_id), target_type_id))
        
        current_app.logger.debug(f"penultimate_target_ids: {penultimate_target_ids}")
        
        valid_paths = self._find_paths(partial_graph,
                                     source_ids,
                                     source['type_id'],
                                     set(penultimate_target_ids),
                                     valid_path_patterns)
        
        current_app.logger.debug(f"找到前N-1层有效路径数量: {len(valid_paths)}")
        for i, vp in enumerate(valid_paths[:5]):  # 只显示前5个路径
            current_app.logger.debug(f"有效路径 {i}: {vp}")
        
        # 检查每个有效路径与最后一层的关系缺失情况
        missing_paths = []
        for original_path in valid_paths:
            final_type_ids = [p[-1] for p in path if len(p) > len(original_path)]
            current_app.logger.debug(f"检查路径 {original_path} 与最终类型 {final_type_ids} 的缺失关系")
            for final_type_id in final_type_ids:
                is_missing = self._check_missing_final_relation(original_path[-1], final_type_id, target.get('q', ''))
                current_app.logger.debug(f"CI {original_path[-1]} 与类型 {final_type_id} 缺失关系: {is_missing}")
                if is_missing:
                    missing_paths.append(original_path)
                    break

        current_app.logger.debug(f"查找缺失关系耗时: {time.time() - build_graph_time:.3f}秒, 找到缺失路径数量: {len(missing_paths)}")
        find_missing_time = time.time()

        numfound = len(missing_paths)
        missing_paths = missing_paths[(self.page - 1) * self.count:self.page * self.count]
        
        # 包装结果，使用前N-1层的types信息
        response, counter, id2ci = self._wrap_path_result(missing_paths,
                                                          types,
                                                          valid_path_patterns,
                                                          set(target.get('type_ids') or []),
                                                          type2show_key)

        current_app.logger.debug(f"包装缺失结果耗时: {time.time() - find_missing_time:.3f}秒")
        current_app.logger.debug(f"search_missing_path总耗时: {time.time() - start_time:.3f}秒")

        return response, counter, len(missing_paths), self.page, numfound, id2ci, relation_types, type2show_key, type2multishow_key

    def _find_missing_direct_relations(self, source_ids, source_type_id, target_type_id, target_query):
        """
        查找源CI中没有与目标类型建立关系的CI（用于路径长度为2的情况）
        
        :param source_ids: 源CI ID列表
        :param source_type_id: 源CI类型ID
        :param target_type_id: 目标CI类型ID
        :param target_query: 目标查询条件
        :return: 缺失关系的源CI列表
        """
        missing_cis = []
        
        # 获取目标类型所有符合条件的CI
        if target_query and not target_query.startswith('_type:'):
            target_q = f"_type:{target_type_id},{target_query}"
        else:
            target_q = f"_type:{target_type_id}"
            
        target_ci_ids = SearchFromDB(target_q, use_ci_filter=True, only_ids=True, count=100000).search()
        if not target_ci_ids:
            # 如果没有符合条件的目标CI，则所有源CI都被视为缺失关系
            return [[str(ci_id)] for ci_id in source_ids]
        
        target_ci_set = set(map(str, target_ci_ids))
        
        # 检查每个源CI是否与目标CI有关系
        for source_id in source_ids:
            key = str(source_id)
            redis_result = rd.get([key], REDIS_PREFIX_CI_RELATION)
            
            if redis_result and redis_result[0]:
                relations = json.loads(redis_result[0])
                # 检查是否有与目标类型的关系
                has_target_relation = False
                for related_ci_id, related_type_id in relations.items():
                    if related_type_id == target_type_id and related_ci_id in target_ci_set:
                        has_target_relation = True
                        break
                
                if not has_target_relation:
                    missing_cis.append([str(source_id)])
            else:
                # 没有任何关系的CI也被视为缺失关系
                missing_cis.append([str(source_id)])
        
        return missing_cis

    def _check_missing_final_relation(self, ci_id, final_type_id, target_query):
        """
        检查单个CI是否缺失与最终类型的关系
        
        :param ci_id: CI ID
        :param final_type_id: 最终类型ID
        :param target_query: 目标查询条件
        :return: True表示缺失关系，False表示有关系
        """
        # 获取最后一层所有符合条件的CI
        if target_query and not target_query.startswith('_type:'):
            target_q = f"_type:{final_type_id},{target_query}"
        else:
            target_q = f"_type:{final_type_id}"
            
        target_ci_ids = SearchFromDB(target_q, use_ci_filter=True, only_ids=True, count=100000).search()
        if not target_ci_ids:
            # 如果没有符合条件的目标CI，则认为是缺失关系
            return True
        
        target_ci_set = set(map(str, target_ci_ids))
        
        # 检查CI是否与最终类型有关系
        key = str(ci_id)
        redis_result = rd.get([key], REDIS_PREFIX_CI_RELATION)
        
        if redis_result and redis_result[0]:
            relations = json.loads(redis_result[0])
            # 检查是否有与最终类型的关系
            for related_ci_id, related_type_id in relations.items():
                if related_type_id == final_type_id and related_ci_id in target_ci_set:
                    return False  # 有关系，不缺失
        
        return True  # 没有关系，缺失

    def _filter_missing_final_relations(self, valid_paths, final_type_id, target_query):
        """
        过滤出最后一层缺失关系的路径
        
        :param valid_paths: 前N-1层的有效路径列表
        :param final_type_id: 最后一层的CI类型ID
        :param target_query: 目标查询条件
        :return: 缺失最后一层关系的路径列表
        """
        if not valid_paths:
            return []
            
        missing_paths = []
        
        # 获取最后一层所有符合条件的CI
        if target_query and not target_query.startswith('_type:'):
            target_q = f"_type:{final_type_id},{target_query}"
        else:
            target_q = f"_type:{final_type_id}"
            
        target_ci_ids = SearchFromDB(target_q, use_ci_filter=True, only_ids=True, count=100000).search()
        if not target_ci_ids:
            # 如果没有符合条件的目标CI，则所有路径都被视为缺失关系
            return valid_paths
        
        target_ci_set = set(map(str, target_ci_ids))
        
        # 检查每个路径的最后一个CI是否与最终类型有关系
        for path in valid_paths:
            last_ci_id = path[-1]  # 路径的最后一个CI
            key = str(last_ci_id)
            redis_result = rd.get([key], REDIS_PREFIX_CI_RELATION)
            
            has_final_relation = False
            if redis_result and redis_result[0]:
                relations = json.loads(redis_result[0])
                # 检查是否有与最终类型的关系
                for related_ci_id, related_type_id in relations.items():
                    if related_type_id == final_type_id and related_ci_id in target_ci_set:
                        has_final_relation = True
                        break
            
            if not has_final_relation:
                missing_paths.append(path)
        
        return missing_paths
