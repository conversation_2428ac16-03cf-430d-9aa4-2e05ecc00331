# -*- coding:utf-8 -*-


from flask import abort

from api.lib.cmdb.resp_format import ErrFormat
from api.models.cmdb import MeasurementUnit


class MeasurementUnitManager(object):
    cls = MeasurementUnit

    @staticmethod
    def get_all():
        return MeasurementUnit.get_by(to_dict=False)


    @staticmethod
    def get_by_id(id):
        return MeasurementUnit.get_by(id=id, first=True, to_dict=True)


    @classmethod
    def get_names(cls):
        return [i.name for i in cls.get_all()]

    @classmethod
    def get_pairs(cls):
        return [(i.id, i.name) for i in cls.get_all()]

    @staticmethod
    def add(name, unit_num):
        MeasurementUnit.get_by(name=name, first=True, to_dict=False) and abort(
            400, ErrFormat.relation_type_exists.format(name))

        return MeasurementUnit.create(name=name, unit_num=unit_num)

    @staticmethod
    def update(mu_id, name, unit_num):
        existed = MeasurementUnit.get_by_id(mu_id) or abort(
            404, ErrFormat.relation_type_not_found.format("id={}".format(mu_id)))

        return existed.update(name=name, unit_num=unit_num)

    @staticmethod
    def delete(mu_id):
        existed = MeasurementUnit.get_by_id(mu_id) or abort(
            404, ErrFormat.relation_type_not_found.format("id={}".format(mu_id)))

        existed.soft_delete()
