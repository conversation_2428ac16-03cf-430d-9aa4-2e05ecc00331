# -*- coding:utf-8 -*-

from __future__ import unicode_literals

import datetime
import os
import yaml

from flask import current_app

from api.extensions import cache
from api.extensions import db
from api.lib.cmdb.custom_dashboard import CustomDashboardManager
from api.models.cmdb import Attribute, AutoDiscoveryExecHistory
from api.models.cmdb import AutoD<PERSON>veryCI
from api.models.cmdb import AutoDiscoveryCIType
from api.models.cmdb import AutoDiscoveryCITypeRelation
from api.models.cmdb import AutoDiscoveryCounter
from api.models.cmdb import AutoDiscoveryRuleSyncHistory
from api.models.cmdb import CI
from api.models.cmdb import CIType
from api.models.cmdb import CITypeAttribute
from api.models.cmdb import PreferenceShowAttributes
from api.models.cmdb import PreferenceTreeView
from api.models.cmdb import RelationType
from api.models.cmdb import LoadAttribute, LoadCITypeAttribute


class AttributeCache(object):
    PREFIX_ID = 'Field::ID::{0}'
    PREFIX_NAME = 'Field::Name::{0}'
    PREFIX_ALIAS = 'Field::Alias::{0}'

    @classmethod
    def get(cls, key):
        if key is None:
            return
        attr = cache.get(cls.PREFIX_NAME.format(key))
        attr = attr or cache.get(cls.PREFIX_ID.format(key))
        attr = attr or cache.get(cls.PREFIX_ALIAS.format(key))

        if attr is None:
            attr = Attribute.get_by(name=key, first=True, to_dict=False)
            attr = attr or Attribute.get_by_id(key)
            attr = attr or Attribute.get_by(alias=key, first=True, to_dict=False)
            if attr is not None:
                cls.set(attr)

        return attr

    @classmethod
    def set(cls, attr):
        cache.set(cls.PREFIX_ID.format(attr.id), attr)
        cache.set(cls.PREFIX_NAME.format(attr.name), attr)
        cache.set(cls.PREFIX_ALIAS.format(attr.alias), attr)

    @classmethod
    def clean(cls, attr):
        cache.delete(cls.PREFIX_ID.format(attr.id))
        cache.delete(cls.PREFIX_NAME.format(attr.name))
        cache.delete(cls.PREFIX_ALIAS.format(attr.alias))


class CITypeCache(object):
    PREFIX_ID = "CIType::ID::{0}"
    PREFIX_NAME = "CIType::Name::{0}"
    PREFIX_ALIAS = "CIType::Alias::{0}"

    @classmethod
    def get(cls, key):
        if key is None:
            return
        ct = cache.get(cls.PREFIX_NAME.format(key))
        ct = ct or cache.get(cls.PREFIX_ID.format(key))
        ct = ct or cache.get(cls.PREFIX_ALIAS.format(key))
        if ct is None:
            ct = CIType.get_by(name=key, first=True, to_dict=False)
            ct = ct or CIType.get_by_id(key)
            ct = ct or CIType.get_by(alias=key, first=True, to_dict=False)
            if ct is not None:
                cls.set(ct)

        return ct

    @classmethod
    def set(cls, ct):
        cache.set(cls.PREFIX_NAME.format(ct.name), ct)
        cache.set(cls.PREFIX_ID.format(ct.id), ct)
        cache.set(cls.PREFIX_ALIAS.format(ct.alias), ct)

    @classmethod
    def clean(cls, key):
        ct = cls.get(key)
        if ct is not None:
            cache.delete(cls.PREFIX_NAME.format(ct.name))
            cache.delete(cls.PREFIX_ID.format(ct.id))
            cache.delete(cls.PREFIX_ALIAS.format(ct.alias))


class RelationTypeCache(object):
    PREFIX_ID = "RelationType::ID::{0}"
    PREFIX_NAME = "RelationType::Name::{0}"

    @classmethod
    def get(cls, key):
        if key is None:
            return
        ct = cache.get(cls.PREFIX_NAME.format(key))
        ct = ct or cache.get(cls.PREFIX_ID.format(key))
        if ct is None:
            ct = RelationType.get_by(name=key, first=True, to_dict=False) or RelationType.get_by_id(key)
            if ct is not None:
                cls.set(ct)

        return ct

    @classmethod
    def set(cls, ct):
        cache.set(cls.PREFIX_NAME.format(ct.name), ct)
        cache.set(cls.PREFIX_ID.format(ct.id), ct)

    @classmethod
    def clean(cls, key):
        ct = cls.get(key)
        if ct is not None:
            cache.delete(cls.PREFIX_NAME.format(ct.name))
            cache.delete(cls.PREFIX_ID.format(ct.id))


class CITypeAttributesCache(object):
    """
    key is type_id or type_name
    """

    PREFIX_ID = "CITypeAttributes::TypeID::{0}"
    PREFIX_NAME = "CITypeAttributes::TypeName::{0}"

    PREFIX_ID2 = "CITypeAttributes2::TypeID::{0}"
    PREFIX_NAME2 = "CITypeAttributes2::TypeName::{0}"

    @classmethod
    def get(cls, key):
        if key is None:
            return

        attrs = cache.get(cls.PREFIX_NAME.format(key))
        attrs = attrs or cache.get(cls.PREFIX_ID.format(key))
        if not attrs:
            attrs = CITypeAttribute.get_by(type_id=key, to_dict=False)

            if not attrs:
                ci_type = CIType.get_by(name=key, first=True, to_dict=False)
                if ci_type is not None:
                    attrs = CITypeAttribute.get_by(type_id=ci_type.id, to_dict=False)

            if attrs is not None:
                cls.set(key, attrs)

        return attrs

    @classmethod
    def get2(cls, key):
        """
        return [(type_attr, attr), ]
        :param key:
        :return:
        """
        if key is None:
            return

        attrs = cache.get(cls.PREFIX_NAME2.format(key))
        attrs = attrs or cache.get(cls.PREFIX_ID2.format(key))
        if not attrs:
            attrs = CITypeAttribute.get_by(type_id=key, to_dict=False)

            if not attrs:
                ci_type = CIType.get_by(name=key, first=True, to_dict=False)
                if ci_type is not None:
                    attrs = CITypeAttribute.get_by(type_id=ci_type.id, to_dict=False)

            if attrs is not None:
                attrs = [(i, AttributeCache.get(i.attr_id)) for i in attrs]
                cls.set2(key, attrs)

        return attrs

    @classmethod
    def set(cls, key, values):
        ci_type = CITypeCache.get(key)
        if ci_type is not None:
            cache.set(cls.PREFIX_ID.format(ci_type.id), values)
            cache.set(cls.PREFIX_NAME.format(ci_type.name), values)

    @classmethod
    def set2(cls, key, values):
        ci_type = CITypeCache.get(key)
        if ci_type is not None:
            cache.set(cls.PREFIX_ID2.format(ci_type.id), values)
            cache.set(cls.PREFIX_NAME2.format(ci_type.name), values)

    @classmethod
    def clean(cls, key):
        ci_type = CITypeCache.get(key)
        attrs = cls.get(key)
        if attrs is not None and ci_type:
            cache.delete(cls.PREFIX_ID.format(ci_type.id))
            cache.delete(cls.PREFIX_NAME.format(ci_type.name))

        attrs2 = cls.get2(key)
        if attrs2 is not None and ci_type:
            cache.delete(cls.PREFIX_ID2.format(ci_type.id))
            cache.delete(cls.PREFIX_NAME2.format(ci_type.name))


class CITypeAttributeCache(object):
    """
    key is type_id  & attr_id
    """

    PREFIX_ID = "CITypeAttribute::TypeID::{0}::AttrID::{1}"

    @classmethod
    def get(cls, type_id, attr_id):
        attr = cache.get(cls.PREFIX_ID.format(type_id, attr_id))
        attr = attr or cache.get(cls.PREFIX_ID.format(type_id, attr_id))
        attr = attr or CITypeAttribute.get_by(type_id=type_id, attr_id=attr_id, first=True, to_dict=False)

        if attr is not None:
            cls.set(type_id, attr_id, attr)

        return attr

    @classmethod
    def set(cls, type_id, attr_id, attr):
        cache.set(cls.PREFIX_ID.format(type_id, attr_id), attr)

    @classmethod
    def clean(cls, type_id, attr_id):
        cache.delete(cls.PREFIX_ID.format(type_id, attr_id))


class CMDBCounterCache(object):
    KEY = 'CMDB::Counter::dashboard'
    KEY2 = 'CMDB::Counter::adc'
    KEY3 = 'CMDB::Counter::sub'

    @classmethod
    def get(cls):
        result = cache.get(cls.KEY) or {}

        if not result:
            result = cls.reset()

        return result

    @classmethod
    def set(cls, result):
        cache.set(cls.KEY, result, timeout=0)

    @classmethod
    def reset(cls):
        customs = CustomDashboardManager.get()
        result = {}
        for custom in customs:
            if custom['category'] == 0:
                res = cls.sum_counter(custom)
            elif custom['category'] == 1:
                res = cls.attribute_counter(custom)
            else:
                res = cls.relation_counter(custom.get('type_id'),
                                           custom.get('level'),
                                           custom.get('options', {}).get('filter', ''),
                                           custom.get('options', {}).get('type_ids', ''))

            if res:
                result[custom['id']] = res

        cls.set(result)

        return result

    @classmethod
    def update(cls, custom, flush=True):
        result = cache.get(cls.KEY) or {}
        if not result:
            result = cls.reset()

        if custom['category'] == 0:
            res = cls.sum_counter(custom)
        elif custom['category'] == 1:
            res = cls.attribute_counter(custom)
        else:
            res = cls.relation_counter(custom.get('type_id'),
                                       custom.get('level'),
                                       custom.get('options', {}).get('filter', ''),
                                       custom.get('options', {}).get('type_ids', ''))

        if res and flush:
            result[custom['id']] = res
            cls.set(result)

        return res

    @staticmethod
    def relation_counter(type_id, level, other_filer, type_ids):
        from api.lib.cmdb.search.ci_relation.search import Search as RelSearch
        from api.lib.cmdb.search import SearchError
        from api.lib.cmdb.search.ci import search

        query = "_type:{}".format(type_id)
        s = search(query, count=1000000)
        try:
            type_names, _, _, _, _, _ = s.search()
        except SearchError as e:
            current_app.logger.error(e)
            return

        type_id_names = [(str(i.get('_id')), i.get(i.get('unique'))) for i in type_names]

        s = RelSearch([i[0] for i in type_id_names], level, other_filer or '')
        try:
            stats = s.statistics(type_ids, need_filter=False)
        except SearchError as e:
            current_app.logger.error(e)
            return

        id2name = dict(type_id_names)
        type_ids = set()
        for i in (stats.get('detail') or []):
            for j in stats['detail'][i]:
                type_ids.add(j)
        for type_id in type_ids:
            _type = CITypeCache.get(type_id)
            id2name[type_id] = _type and _type.alias

        result = dict(summary={}, detail={})
        for i in stats:
            if i == "detail":
                for j in stats['detail']:
                    if id2name[j]:
                        result['detail'][id2name[j]] = stats['detail'][j]
                        result['detail'][id2name[j]] = dict()
                        for _j in stats['detail'][j]:
                            result['detail'][id2name[j]][id2name[_j]] = stats['detail'][j][_j]
            elif id2name.get(i):
                result['summary'][id2name[i]] = stats[i]

        return result

    @staticmethod
    def attribute_counter(custom):
        from api.lib.cmdb.search import SearchError
        from api.lib.cmdb.search.ci import search
        from api.lib.cmdb.utils import ValueTypeMap

        custom.setdefault('options', {})
        type_id = custom.get('type_id')
        attr_id = custom.get('attr_id')
        type_ids = custom['options'].get('type_ids') or (type_id and [type_id])
        attr_ids = list(map(str, custom['options'].get('attr_ids') or (attr_id and [attr_id])))
        try:
            attr2value_type = [AttributeCache.get(i).value_type for i in attr_ids]
        except AttributeError:
            return

        other_filter = custom['options'].get('filter')
        other_filter = "{}".format(other_filter) if other_filter else ''

        if custom['options'].get('ret') == 'cis':
            query = "_type:({}),{}".format(";".join(map(str, type_ids)), other_filter)
            s = search(query, fl=attr_ids, ret_key='alias', count=100)
            try:
                cis, _, _, _, _, _ = s.search()
            except SearchError as e:
                current_app.logger.error(e)
                return

            return cis

        result = dict()
        # level = 1
        query = "_type:({}),{}".format(";".join(map(str, type_ids)), other_filter)
        s = search(query, fl=attr_ids, facet=[attr_ids[0]], count=1)
        try:
            _, _, _, _, _, facet = s.search()
        except SearchError as e:
            current_app.logger.error(e)
            return
        for i in (list(facet.values()) or [[]])[0]:
            result[ValueTypeMap.serialize2[attr2value_type[0]](str(i[0]))] = i[1]
        if len(attr_ids) == 1:
            return result

        # level = 2
        for v in result:
            query = "_type:({}),{},{}:{}".format(";".join(map(str, type_ids)), other_filter, attr_ids[0], v)
            s = search(query, fl=attr_ids, facet=[attr_ids[1]], count=1)
            try:
                _, _, _, _, _, facet = s.search()
            except SearchError as e:
                current_app.logger.error(e)
                return
            result[v] = dict()
            for i in (list(facet.values()) or [[]])[0]:
                result[v][ValueTypeMap.serialize2[attr2value_type[1]](str(i[0]))] = i[1]

        if len(attr_ids) == 2:
            return result

        # level = 3
        for v1 in result:
            if not isinstance(result[v1], dict):
                continue
            for v2 in result[v1]:
                query = "_type:({}),{},{}:{},{}:{}".format(";".join(map(str, type_ids)), other_filter,
                                                           attr_ids[0], v1, attr_ids[1], v2)
                s = search(query, fl=attr_ids, facet=[attr_ids[2]], count=1)
                try:
                    _, _, _, _, _, facet = s.search()
                except SearchError as e:
                    current_app.logger.error(e)
                    return
                result[v1][v2] = dict()
                for i in (list(facet.values()) or [[]])[0]:
                    result[v1][v2][ValueTypeMap.serialize2[attr2value_type[2]](str(i[0]))] = i[1]

        return result

    @staticmethod
    def sum_counter(custom):
        from api.lib.cmdb.search import SearchError
        from api.lib.cmdb.search.ci import search

        custom.setdefault('options', {})
        type_id = custom.get('type_id')
        type_ids = custom['options'].get('type_ids') or (type_id and [type_id])
        other_filter = custom['options'].get('filter') or ''

        query = "_type:({}),{}".format(";".join(map(str, type_ids)), other_filter)
        s = search(query, count=1)
        try:
            _, _, _, _, numfound, _ = s.search()
        except SearchError as e:
            current_app.logger.error(e)
            return

        return numfound

    @classmethod
    def flush_adc_counter(cls):
        res = db.session.query(CI.type_id, CI.is_auto_discovery)
        result = dict()
        for i in res:
            result.setdefault(i.type_id, dict(total=0, auto_discovery=0))
            result[i.type_id]['total'] += 1
            if i.is_auto_discovery:
                result[i.type_id]['auto_discovery'] += 1

        cache.set(cls.KEY2, result, timeout=0)

        res = db.session.query(AutoDiscoveryCI.created_at,
                               AutoDiscoveryCI.updated_at,
                               AutoDiscoveryCI.adt_id,
                               AutoDiscoveryCI.type_id,
                               AutoDiscoveryCI.is_accept).filter(AutoDiscoveryCI.deleted.is_(False))

        today = datetime.datetime.today()
        this_month = datetime.datetime(today.year, today.month, 1)
        last_month = this_month - datetime.timedelta(days=1)
        last_month = datetime.datetime(last_month.year, last_month.month, 1)
        this_week = today - datetime.timedelta(days=datetime.date.weekday(today))
        this_week = datetime.datetime(this_week.year, this_week.month, this_week.day)
        last_week = this_week - datetime.timedelta(days=7)
        last_week = datetime.datetime(last_week.year, last_week.month, last_week.day)
        result = dict()
        for i in res:
            if i.type_id not in result:
                result[i.type_id] = dict(instance_count=0, accept_count=0,
                                         this_month_count=0, this_week_count=0, last_month_count=0, last_week_count=0)

                adts = AutoDiscoveryCIType.get_by(type_id=i.type_id, to_dict=False)
                result[i.type_id]['rule_count'] = len(adts) + AutoDiscoveryCITypeRelation.get_by(
                    ad_type_id=i.type_id, only_query=True).count()
                result[i.type_id]['exec_target_count'] = len(
                    set([i.oneagent_id for adt in adts for i in db.session.query(
                        AutoDiscoveryRuleSyncHistory.oneagent_id).filter(
                        AutoDiscoveryRuleSyncHistory.adt_id == adt.id)]))

            result[i.type_id]['instance_count'] += 1
            if i.is_accept:
                result[i.type_id]['accept_count'] += 1

            if last_month <= i.created_at < this_month:
                result[i.type_id]['last_month_count'] += 1
            elif i.created_at >= this_month:
                result[i.type_id]['this_month_count'] += 1

            if last_week <= i.created_at < this_week:
                result[i.type_id]['last_week_count'] += 1
            elif i.created_at >= this_week:
                result[i.type_id]['this_week_count'] += 1

        for type_id in result:
            existed = AutoDiscoveryCounter.get_by(type_id=type_id, first=True, to_dict=False)
            if existed is None:
                AutoDiscoveryCounter.create(type_id=type_id, **result[type_id])
            else:
                existed.update(**result[type_id])

        for i in AutoDiscoveryCounter.get_by(to_dict=False):
            if i.type_id not in result:
                i.delete()

    @classmethod
    def clear_ad_exec_history(cls):
        ci_types = CIType.get_by(to_dict=False)
        for ci_type in ci_types:
            for i in AutoDiscoveryExecHistory.get_by(type_id=ci_type.id, only_query=True).order_by(
                    AutoDiscoveryExecHistory.id.desc()).offset(50000):
                i.delete(commit=False)
            db.session.commit()

    @classmethod
    def get_adc_counter(cls):
        return cache.get(cls.KEY2) or cls.flush_adc_counter()

    @classmethod
    def flush_sub_counter(cls):
        result = dict(type_id2users=dict())

        types = db.session.query(PreferenceShowAttributes.type_id,
                                 PreferenceShowAttributes.uid, PreferenceShowAttributes.created_at).filter(
            PreferenceShowAttributes.deleted.is_(False)).group_by(
            PreferenceShowAttributes.uid, PreferenceShowAttributes.type_id)
        for i in types:
            result['type_id2users'].setdefault(i.type_id, []).append(i.uid)

        types = PreferenceTreeView.get_by(to_dict=False)
        for i in types:

            result['type_id2users'].setdefault(i.type_id, [])
            if i.uid not in result['type_id2users'][i.type_id]:
                result['type_id2users'][i.type_id].append(i.uid)

        cache.set(cls.KEY3, result, timeout=0)

        return result

    @classmethod
    def get_sub_counter(cls):
        return cache.get(cls.KEY3) or cls.flush_sub_counter()


class AutoDiscoveryMappingCache(object):
    PREFIX = 'CMDB::AutoDiscovery::Mapping::{}'

    @classmethod
    def get(cls, name):
        res = cache.get(cls.PREFIX.format(name)) or {}
        if not res:
            path = os.path.join(os.path.abspath(os.path.dirname(__file__)), "auto_discovery/mapping/{}.yaml".format(name))
            if os.path.exists(path):
                with open(path, 'r') as f:
                    mapping = yaml.safe_load(f)
                    res = mapping.get('mapping') or {}
                    res and cache.set(cls.PREFIX.format(name), res, timeout=0)

        return res


class BillingCache(object):
    """计费数据缓存"""
    PREFIX_BILLING = "CMDB::Billing::{key}"
    CACHE_TIMEOUT = 1200  # 默认缓存20分钟

    # 添加状态相关的常量
    STATUS_PROCESSING = "processing"  # 正在生成中
    STATUS_COMPLETED = "completed"    # 生成完成
    STATUS_FAILED = "failed"         # 生成失败

    @classmethod
    def get_cache_key(cls, source, target, path):
        """生成缓存key，规范化处理参数"""
        try:
            current_app.logger.debug(f"开始生成缓存key, 原始参数: source={source}, target={target}, path={path}")

            # 规范化处理source
            if isinstance(source, dict):
                query = source.get('q', '')
                source_key = f"type_{source.get('type_id', '')}_q_{query}"
            else:
                source_key = str(source)

            # 规范化处理target
            if isinstance(target, dict):
                type_ids = '_'.join(map(str, sorted(target.get('type_ids', []))))
                query = target.get('q', '')
                target_key = f"types_{type_ids}_q_{query}"
            else:
                target_key = str(target)

            # 规范化处理path
            if isinstance(path, list):
                path_key = '_'.join('_'.join(map(str, p)) for p in sorted(path))
            else:
                path_key = str(path)

            # 生成最终的key
            cache_key = f"{source_key}:{target_key}:{path_key}"
            current_app.logger.debug(f"生成的缓存key: {cache_key}")
            return cache_key
        except Exception as e:
            current_app.logger.error(f"生成缓存key失败: {str(e)}")
            return None

    @classmethod
    def get_full_key(cls, key):
        """获取完整的缓存key"""
        return cls.PREFIX_BILLING.format(key=key)

    @classmethod
    def clean_billing_data(cls, source, target, path):
        """清除缓存数据和状态"""
        try:
            # 获取缓存key
            cache_key = cls.get_cache_key(source, target, path)
            if not cache_key:
                current_app.logger.error("获取缓存key失败")
                return False

            # 清理数据缓存
            full_key = cls.get_full_key(cache_key)
            current_app.logger.debug(f"正在清除数据缓存: {full_key}")
            cache.delete(full_key)

            # 清理状态缓存
            status_key = cls.get_status_key(source, target, path)
            if status_key:
                current_app.logger.debug(f"正在清除状态缓存: {status_key}")
                cache.delete(status_key)

            current_app.logger.info("缓存清除成功")
            return True
        except Exception as e:
            current_app.logger.error(f"清除缓存失败: {str(e)}")
            return False

    @classmethod
    def get_billing_data(cls, source, target, path):
        try:
            cache_key = cls.get_cache_key(source, target, path)
            if not cache_key:
                return None

            full_key = cls.get_full_key(cache_key)
            cached_data = cache.get(full_key)
            if cached_data:
                current_app.logger.debug(f"命中缓存: {full_key}")
            return cached_data
        except Exception as e:
            current_app.logger.error(f"获取缓存数据失败: {str(e)}")
            return None

    @classmethod
    def set_billing_data(cls, source, target, path, data, timeout=None):
        try:
            cache_key = cls.get_cache_key(source, target, path)
            if not cache_key:
                return

            full_key = cls.get_full_key(cache_key)
            timeout = timeout or cls.CACHE_TIMEOUT
            cache.set(full_key, data, timeout=timeout)
            current_app.logger.debug(f"设置缓存成功: {full_key}")
        except Exception as e:
            current_app.logger.error(f"设置缓存数据失败: {str(e)}")

    @classmethod
    def get_status_key(cls, source, target, path):
        """获取状态缓存的key"""
        try:
            current_app.logger.debug(f"开始获取状态缓存key: source={source}, target={target}, path={path}")
            cache_key = cls.get_cache_key(source, target, path)
            if not cache_key:
                current_app.logger.error("获取基础缓存key失败")
                return None
            status_key = f"{cls.get_full_key(cache_key)}:status"
            current_app.logger.debug(f"生成状态缓存key: {status_key}")
            return status_key
        except Exception as e:
            current_app.logger.error(f"获取状态缓存key异常: {str(e)}")
            return None

    @classmethod
    def get_generation_status(cls, source, target, path):
        """获取数据生成状态"""
        try:
            current_app.logger.debug(f"开始获取生成状态: source={source}, target={target}, path={path}")
            status_key = cls.get_status_key(source, target, path)
            if not status_key:
                current_app.logger.error("获取状态缓存key失败")
                return None

            status_data = cache.get(status_key)
            if status_data:
                current_app.logger.info(f"获取到生成状态: {status_data}")
            else:
                current_app.logger.debug("未找到生成状态数据")
            return status_data
        except Exception as e:
            current_app.logger.error(f"获取生成状态异常: {str(e)}")
            return None

    @classmethod
    def set_generation_status(cls, source, target, path, status, message=None):
        """设置数据生成状态"""
        try:
            current_app.logger.debug(f"开始设置生成状态: source={source}, target={target}, path={path}, status={status}, message={message}")
            status_key = cls.get_status_key(source, target, path)
            if not status_key:
                current_app.logger.error("获取状态缓存key失败")
                return

            status_data = {
                'status': status,
                'message': message,
                'timestamp': datetime.datetime.now().isoformat()
            }
            cache.set(status_key, status_data, timeout=cls.CACHE_TIMEOUT)
            current_app.logger.info(f"成功设置生成状态: {status_data}")
        except Exception as e:
            current_app.logger.error(f"设置生成状态异常: {str(e)}")


class LoadAttrCache:
    """负载字段缓存管理"""

    PREFIX = "LoadAttr"
    ID_PREFIX = f"{PREFIX}::ID::"  # LoadAttr::ID::{id}
    NAME_PREFIX = f"{PREFIX}::Name::"  # LoadAttr::Name::{name}

    @classmethod
    def _gen_id_key(cls, attr_id: int) -> str:
        """生成ID缓存键"""
        return f"{cls.ID_PREFIX}{attr_id}"

    @classmethod
    def _gen_name_key(cls, name: str) -> str:
        """生成名称缓存键"""
        return f"{cls.NAME_PREFIX}{name}"

    @classmethod
    def get(cls, attr_id_or_name) -> LoadAttribute:
        """
        获取字段定义
        Args:
            attr_id_or_name: 字段ID或名称
        Returns:
            LoadAttribute对象
        """
        # 尝试从缓存获取
        if isinstance(attr_id_or_name, (int, str)) and str(attr_id_or_name).isdigit():
            key = cls._gen_id_key(int(attr_id_or_name))
            attr_dict = cache.get(key)
            if attr_dict:
                return LoadAttribute.from_dict(**attr_dict)

            # 缓存未命中，从数据库查询
            attr = LoadAttribute.query.filter_by(
                id=int(attr_id_or_name),
                deleted=False
            ).first()
        else:
            key = cls._gen_name_key(str(attr_id_or_name))
            attr_dict = cache.get(key)
            if attr_dict:
                return LoadAttribute.from_dict(**attr_dict)

            # 缓存未命中，从数据库查询
            attr = LoadAttribute.query.filter_by(
                name=str(attr_id_or_name),
                deleted=False
            ).first()

        if attr:
            # 同时缓存ID和名称
            attr_dict = attr.to_dict()
            cache.set(cls._gen_id_key(attr.id), attr_dict)
            cache.set(cls._gen_name_key(attr.name), attr_dict)
            return attr

        return None

    @classmethod
    def get_attrs_by_ids(cls, attr_ids: list) -> list:
        """
        批量获取字段定义
        Args:
            attr_ids: 字段ID列表
        Returns:
            LoadAttribute对象列表
        """
        if not attr_ids:
            return []

        attrs = []
        missing_ids = []

        # 先从缓存获取
        for attr_id in attr_ids:
            attr = cls.get(attr_id)
            if attr:
                attrs.append(attr)
            else:
                missing_ids.append(attr_id)

        # 查询未命中的字段
        if missing_ids:
            db_attrs = LoadAttribute.query.filter_by(
                deleted=False
            ).all()

            for attr in db_attrs:
                attr_dict = attr.to_dict()
                cache.set(cls._gen_id_key(attr.id), attr_dict)
                cache.set(cls._gen_name_key(attr.name), attr_dict)
                attrs.append(attr)

        return attrs

    @classmethod
    def clean(cls, attr_id: int = None, name: str = None):
        """
        清理缓存
        Args:
            attr_id: 字段ID
            name: 字段名称
        """
        if attr_id:
            cache.delete(cls._gen_id_key(attr_id))
        if name:
            cache.delete(cls._gen_name_key(name))


class CITypeLoadAttrCache:
    """CI类型与负载字段关联关系缓存管理"""

    PREFIX = "CITypeLoadAttrs"
    TYPE_PREFIX = f"{PREFIX}::TypeID::"  # CITypeLoadAttrs::TypeID::{type_id}
    FIELD_PREFIX = f"{PREFIX}::AttrID::"  # CITypeLoadAttrs::AttrID::{attr_id}

    @classmethod
    def _gen_type_key(cls, type_id: int) -> str:
        """生成类型缓存键"""
        return f"{cls.TYPE_PREFIX}{type_id}"

    @classmethod
    def _gen_attr_key(cls, attr_id: int) -> str:
        """生成字段缓存键"""
        return f"{cls.FIELD_PREFIX}{attr_id}"

    @classmethod
    def get_by_type(cls, type_id: int) -> list:
        """
        获取CI类型关联的所有字段
        Args:
            type_id: CI类型ID
        Returns:
            LoadAttribute对象列表
        """
        key = cls._gen_type_key(type_id)
        relation_dicts = cache.get(key)

        if relation_dicts is None:
            # 缓存未命中，从数据库查询
            relations = LoadCITypeAttribute.query.filter_by(
                type_id=type_id,
                deleted=False
            ).order_by(LoadCITypeAttribute.order).all()

            if relations:
                relation_dicts = [relation.to_dict() for relation in relations]
                cache.set(key, relation_dicts)
            else:
                relation_dicts = []

        # 获取字段详情
        attrs = []
        for relation_dict in relation_dicts:
            attr = LoadAttrCache.get(relation_dict['load_attr_id'])
            if attr:
                # 添加关联关系的额外属性
                attr.is_required = relation_dict.get('is_required', False)
                attr.order = relation_dict.get('order', 0)
                attrs.append(attr)

        return attrs

    @classmethod
    def get_by_attr(cls, attr_id: int) -> list:
        """
        获取字段关联的所有CI类型
        Args:
            attr_id: 字段ID
        Returns:
            LoadCITypeAttribute对象列表
        """
        key = cls._gen_attr_key(attr_id)
        relation_dicts = cache.get(key)

        if relation_dicts is None:
            # 缓存未命中，从数据库查询
            relations = LoadCITypeAttribute.query.filter_by(
                load_attr_id=attr_id,
                deleted=False
            ).all()

            if relations:
                relation_dicts = [relation.to_dict() for relation in relations]
                cache.set(key, relation_dicts)
            else:
                relation_dicts = []

        return [LoadCITypeAttribute.from_dict(**r) for r in relation_dicts]

    @classmethod
    def clean_relation(cls, type_id: int = None, attr_id: int = None):
        """
        清理关联关系缓存
        Args:
            type_id: CI类型ID
            attr_id: 字段ID
        """
        if type_id:
            cache.delete(cls._gen_type_key(type_id))
        if attr_id:
            cache.delete(cls._gen_attr_key(attr_id))

    @classmethod
    def clean_all_relations(cls):
        """清理所有关联关系缓存"""
        # 获取所有CI类型和字段的关联关系
        relations = LoadCITypeAttribute.query.filter_by(deleted=False).all()
        for relation in relations:
            cls.clean_relation(relation.type_id, relation.load_attr_id)


class LoadDataQueryCache:
    """负载数据查询缓存管理，用于缓存_get_ci_period_pairs的结果"""

    PREFIX = "LoadDataQuery"
    CACHE_TIMEOUT = 180  # 缓存过期时间，3分钟

    @classmethod
    def _gen_cache_key(cls, type_id, ci_ids, attribute_ids, start_period, end_period, period_type, time_slot=None):
        """
        生成缓存键
        Args:
            type_id: CI类型ID
            ci_ids: CI ID列表
            attribute_ids: 属性ID列表
            start_period: 开始时间
            end_period: 结束时间
            period_type: 时间类型
            time_slot: 时间段(可选)
        Returns:
            str: 缓存键
        """
        # 对列表参数进行排序，确保相同参数生成相同的键
        ci_ids_str = "_".join(map(str, sorted(ci_ids))) if ci_ids else "none"
        attr_ids_str = "_".join(map(str, sorted(attribute_ids))) if attribute_ids else "none"
        time_slot_str = time_slot if time_slot else "none"

        # 构建缓存键，包含时间段参数
        key = f"{cls.PREFIX}::type_{type_id}::ci_{ci_ids_str}::attr_{attr_ids_str}::period_{start_period}_{end_period}_{period_type}::slot_{time_slot_str}"
        return key

    @classmethod
    def get(cls, type_id, ci_ids, attribute_ids, start_period, end_period, period_type, time_slot=None):
        """
        获取缓存的CI-时间组合
        Args:
            type_id: CI类型ID
            ci_ids: CI ID列表
            attribute_ids: 属性ID列表
            start_period: 开始时间
            end_period: 结束时间
            period_type: 时间类型
            time_slot: 时间段(可选)
        Returns:
            list: 缓存的CI-时间组合，如果缓存不存在则返回None
        """
        key = cls._gen_cache_key(type_id, ci_ids, attribute_ids, start_period, end_period, period_type, time_slot)
        cached_data = cache.get(key)

        if cached_data is not None:
            current_app.logger.debug(f"命中负载数据查询缓存: {key}")

        return cached_data

    @classmethod
    def set(cls, type_id, ci_ids, attribute_ids, start_period, end_period, period_type, data, time_slot=None):
        """
        设置CI-时间组合缓存
        Args:
            type_id: CI类型ID
            ci_ids: CI ID列表
            attribute_ids: 属性ID列表
            start_period: 开始时间
            end_period: 结束时间
            period_type: 时间类型
            data: 要缓存的数据
            time_slot: 时间段(可选)
        """
        key = cls._gen_cache_key(type_id, ci_ids, attribute_ids, start_period, end_period, period_type, time_slot)
        cache.set(key, data, timeout=cls.CACHE_TIMEOUT)
        current_app.logger.debug(f"设置负载数据查询缓存: {key}")

    @classmethod
    def clean(cls, type_id=None, ci_ids=None, attribute_ids=None):
        """
        清理缓存，由于缓存键的复杂性，这里只实现简单的按类型清理
        Args:
            type_id: CI类型ID，如果提供则只清理该类型的缓存
            ci_ids: CI ID列表，如果提供则只清理包含这些CI的缓存
            attribute_ids: 属性ID列表，如果提供则只清理包含这些属性的缓存
        """
        # 由于Redis不支持按前缀删除，这里只能通过设置较短的过期时间来实现缓存自动失效
        # 实际应用中可以考虑使用Redis的SCAN命令来实现按前缀删除
        current_app.logger.debug(f"负载数据查询缓存将在 {cls.CACHE_TIMEOUT} 秒后自动失效")
