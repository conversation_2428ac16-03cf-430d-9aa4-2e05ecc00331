# -*- coding:utf-8 -*-

from flask import abort, current_app
from api.models.cmdb import LoadDataImportHistory
from api.lib.perm.acl.cache import UserCache
from api.lib.cmdb.resp_format import ErrFormat
from api.lib.cmdb.cache import CIType<PERSON>ache
from api.lib.cmdb.load.const import HistoryStatusEnum, OperateTypeEnum


class LoadHistoryManager(object):
    """负载数据导入历史记录管理类"""

    @staticmethod
    def get(page: int, page_size: int, username: str = None, type_id: int = None, 
            status: str = None, file_name: str = None) -> tuple:
        """获取导入历史记录
        
        Args:
            page: 页码
            page_size: 每页大小
            username: 用户名
            type_id: CI类型ID
            status: 状态
            file_name: 文件名
            
        Returns:
            tuple: (总数, 历史记录列表)
        """
        current_app.logger.debug(f"开始查询导入历史记录: page={page}, page_size={page_size}, "
                               f"username={username}, type_id={type_id}, status={status}, "
                               f"file_name={file_name}")
        
        query = LoadDataImportHistory.get_by(only_query=True)

        # 按CI类型过滤
        if type_id is not None:
            current_app.logger.debug(f"按CI类型过滤: type_id={type_id}")
            query = query.filter(LoadDataImportHistory.type_id == type_id)

        # 按用户名过滤
        if username:
            current_app.logger.debug(f"按用户名过滤: username={username}")
            user = UserCache.get(username)
            if user:
                query = query.filter(LoadDataImportHistory.uid == user.uid)
                current_app.logger.debug(f"找到用户: uid={user.uid}")
            else:
                current_app.logger.debug(f"未找到用户: username={username}")
                return abort(404, ErrFormat.user_not_found.format(username))

        # 按状态过滤
        if status is not None:
            current_app.logger.debug(f"按状态过滤: status={status}")
            if not HistoryStatusEnum.is_valid(status):
                current_app.logger.debug(f"状态值无效: status={status}")
                return abort(400, ErrFormat.argument_value_invalid.format("status"))
            query = query.filter(LoadDataImportHistory.status == status)

        # 按文件名过滤
        if file_name is not None and file_name != "":
            current_app.logger.debug(f"按文件名过滤: file_name={file_name}")
            query = query.filter(LoadDataImportHistory.file_name.like(f"%{file_name}%"))

        # 获取总数
        numfound = query.count()
        current_app.logger.debug(f"查询到总记录数: numfound={numfound}")

        # 分页查询
        query = query.order_by(LoadDataImportHistory.id.desc())
        result = query.offset((page - 1) * page_size).limit(page_size)
        result = [i.to_dict() for i in result]
        current_app.logger.debug(f"当前页记录数: count={len(result)}")

        # 补充用户和CI类型信息
        for res in result:
            # 添加用户信息
            res["user"] = UserCache.get(res.get("uid"))
            if res["user"]:
                res['user'] = res['user'].nickname
                current_app.logger.debug(f"补充用户信息: uid={res.get('uid')}, nickname={res['user']}")

            # 添加CI类型信息
            ci_type = CITypeCache.get(res.get('type_id'))
            if ci_type:
                res['type_name'] = ci_type.name
                res['type_alias'] = ci_type.alias
            # # 格式化时间
            # if res.get('start_time'):
            #     res['start_time'] = res['start_time'].strftime('%Y-%m-%d %H:%M:%S')
            # if res.get('end_time'):
            #     res['end_time'] = res['end_time'].strftime('%Y-%m-%d %H:%M:%S')

            # 计算进度百分比
            if res.get('total_count', 0) > 0:
                res['progress'] = round(res.get('processed_count', 0) / res['total_count'] * 100, 2)
            else:
                res['progress'] = 0

        current_app.logger.debug(f"查询完成, 返回结果: numfound={numfound}, result_count={len(result)}")
        return numfound, result

    @staticmethod
    def get_by_id(history_id: int) -> dict:
        """获取单条历史记录详情
        
        Args:
            history_id: 历史记录ID
            
        Returns:
            dict: 历史记录详情
        """
        history = LoadDataImportHistory.get_by_id(history_id)
        if not history:
            return abort(404, ErrFormat.load_history_not_found.format(history_id))

        result = history.to_dict()

        # 添加用户信息
        result["user"] = UserCache.get(result.get("uid"))
        if result["user"]:
            result['user'] = result['user'].nickname

        # 添加CI类型信息
        ci_type = CITypeCache.get(result.get('type_id'))
        if ci_type:
            result['type_name'] = ci_type.name
            result['type_alias'] = ci_type.alias

        # 计算进度百分比
        if result.get('total_count', 0) > 0:
            result['progress'] = round(result.get('processed_count', 0) / result['total_count'] * 100, 2)
        else:
            result['progress'] = 0

        return result

    @staticmethod
    def get_by_type_id(type_id: int, page: int = 1, page_size: int = 20) -> tuple:
        """获取指定CI类型的导入历史记录
        
        Args:
            type_id: CI类型ID
            page: 页码
            page_size: 每页大小
            
        Returns:
            tuple: (总数, 历史记录列表)
        """
        return LoadHistoryManager.get(page, page_size, type_id=type_id)

    @staticmethod
    def get_by_user(username: str, page: int = 1, page_size: int = 20) -> tuple:
        """获取指定用户的导入历史记录
        
        Args:
            username: 用户名
            page: 页码
            page_size: 每页大小
            
        Returns:
            tuple: (总数, 历史记录列表)
        """
        return LoadHistoryManager.get(page, page_size, username=username)

    @staticmethod
    def get_by_status(status: str, page: int = 1, page_size: int = 20) -> tuple:
        """获取指定状态的导入历史记录
        
        Args:
            status: 状态
            page: 页码
            page_size: 每页大小
            
        Returns:
            tuple: (总数, 历史记录列表)
        """
        return LoadHistoryManager.get(page, page_size, status=status)

    @staticmethod
    def get_by_file_name(file_name: str, page: int = 1, page_size: int = 20) -> tuple:
        """获取指定文件名的导入历史记录
        
        Args:
            file_name: 文件名
            page: 页码
            page_size: 每页大小
            
        Returns:
            tuple: (总数, 历史记录列表)
        """
        return LoadHistoryManager.get(page, page_size, file_name=file_name)

    @staticmethod
    def add(type_id: int, total_count: int = 0, batch_size: int = 5000, 
            file_name: str = None, status: str = HistoryStatusEnum.PENDING, 
            operate_type: str = OperateTypeEnum.ADD_DEVICE) -> LoadDataImportHistory:
        """新增导入历史记录
        
        Args:
            type_id: CI类型ID
            total_count: 总记录数
            batch_size: 批次大小
            file_name: 文件名
            operate_type: 操作类型
            
        Returns:
            LoadDataImportHistory: 创建的历史记录对象
        """
        from flask_login import current_user
        
        # 计算总批次数
        total_batches = (total_count + batch_size - 1) // batch_size if total_count > 0 else 0
        
        try:
            # 创建历史记录
            history = LoadDataImportHistory.create(
                type_id=type_id,
                uid=current_user.uid,
                total_count=total_count,
                batch_size=batch_size,
                total_batches=total_batches,
                status=status,
                operate_type=operate_type,
                file_name=file_name
            )
            
            current_app.logger.debug(
                f"创建导入历史记录成功: id={history.id}, type_id={type_id}, "
                f"total_count={total_count}, batch_size={batch_size}"
            )
            
            return history
            
        except Exception as e:
            current_app.logger.error(f"创建导入历史记录失败: {str(e)}")
            return abort(400, ErrFormat.load_history_create_failed.format(str(e)))
