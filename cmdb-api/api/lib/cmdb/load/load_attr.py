from flask import abort
from flask import current_app
from api.extensions import db
from api.lib.cmdb.cache import LoadAttrCache, CITypeLoadAttrCache, LoadDataQueryCache
from api.lib.cmdb.load.const import LoadValueTypeEnum, PeriodTypeEnum, HistoryStatusEnum, OperateTypeEnum, TimeSlotEnum
from api.lib.cmdb.resp_format import ErrFormat
from api.models.cmdb import LoadAttribute, LoadCITypeAttribute, CI
from collections import defaultdict
import datetime
from typing import List, Dict, Any, Tuple
from api.lib.cmdb.load.utils import LoadValueTypeMap, TimeSlotValidator
from api.lib.cmdb.cache import AttributeCache
import re
from api.lib.cmdb.ci_type import CITypeGroupManager
from api.models.cmdb import LoadDataImportHistory
from api.lib.cmdb.load.load_history import LoadHistoryManager
from api.lib.cmdb.const import CMDB_QUEUE



class LoadAttrManager:
    """负载字段管理类"""
    NAME_PATTERN = re.compile(r'^[a-zA-Z0-9_-]+$')  # 只允许英文、数字、下划线和连字符

    @classmethod
    def _validate_name(cls, name):
        """验证名称格式
        Args:
            name: 字段名称
        Returns:
            bool: 验证是否通过
        """
        if not cls.NAME_PATTERN.match(name):
            return abort(400, ErrFormat.load_attr_name_invalid.format(name))
        return True

    @staticmethod
    def get_attr(attr_id_or_name):
        """获取字段定义"""
        return LoadAttrCache.get(attr_id_or_name)

    @staticmethod
    def get_attrs_by_ids(attr_ids):
        """批量获取字段定义"""
        return LoadAttrCache.get_attrs_by_ids(attr_ids)

    @classmethod
    def add_attr(cls, name, value_type, alias=None, is_monthly=False):
        """添加字段"""
        # 验证name格式
        cls._validate_name(name)

        if cls.get_attr(name):
            return abort(400, ErrFormat.load_attr_name_duplicate.format(name))

        if not LoadValueTypeEnum.is_valid(value_type):
            return abort(400, ErrFormat.load_attr_value_type_invalid.format(value_type))

        try:
            attr = LoadAttribute.create(
                name=name,
                value_type=value_type,
                alias=alias,
                is_monthly=is_monthly
            )
            LoadAttrCache.clean(attr_id=attr.id, name=attr.name)
            return attr
        except Exception as e:
            current_app.logger.error(f"创建负载字段失败: {str(e)}")
            return abort(400, ErrFormat.load_attr_create_failed.format(name))

    @classmethod
    def update_attr(cls, attr_id, **kwargs):
        """更新字段"""
        attr = cls.get_attr(attr_id)
        if not attr:
            return abort(400, ErrFormat.load_attr_not_found.format(attr_id))

        value_type = kwargs.get('value_type')
        if value_type and not LoadValueTypeEnum.is_valid(value_type):
            return abort(400, ErrFormat.load_attr_value_type_invalid.format(value_type))

        new_name = kwargs.get('name')
        if new_name:
            # 验证新name格式
            cls._validate_name(new_name)

            if new_name != attr.name:
                if cls.get_attr(new_name):
                    return abort(400, ErrFormat.load_attr_name_duplicate.format(new_name))

        try:
            # 直接在已存在的对象上调用update
            attr = LoadAttribute.get_by_id(attr_id)
            attr.update(**kwargs, filter_none=False)

            LoadAttrCache.clean(attr_id=attr.id, name=attr.name)
            if new_name:
                LoadAttrCache.clean(name=new_name)
            return attr
        except Exception as e:
            current_app.logger.error(f"更新负载字段失败: {str(e)}")
            return abort(400, ErrFormat.load_attr_update_failed.format(attr_id))

    @classmethod
    def delete_attr(cls, attr_id):
        """删除字段"""
        attr = cls.get_attr(attr_id)
        attr = LoadAttribute.get_by_id(attr_id)
        if not attr:
            return abort(400, ErrFormat.load_attr_not_found.format(attr_id))

        relations = LoadRelationsManager.get_type_relations(attr_id)
        if relations:
            current_app.logger.debug(f"字段 {attr_id} 关联CI类型 {relations}")
            return abort(400, ErrFormat.load_attr_referenced.format(attr_id))

        try:
            attr.update(
                deleted=True,
                deleted_at=datetime.datetime.now()
            )
            LoadAttrCache.clean(attr_id=attr.id, name=attr.name)
            return True
        except Exception as e:
            current_app.logger.error(f"删除负载字段失败: {str(e)}")
            return abort(400, ErrFormat.load_attr_delete_failed.format(attr_id))

    @classmethod
    def get_grouped_ci_types_with_load_attrs(cls) -> List[Dict]:
        """
        获取分组形式的已配置负载字段关联的CI类型信息

        Returns:
            List[Dict]: 分组形式的CI类型列表，每个分组包含已配置负载字段的CI类型
        """
        try:
            # 获取所有分组及其CI类型
            all_groups = CITypeGroupManager.get(need_other=True, config_required=False)
            current_app.logger.debug(f"获取所有分组信息: {all_groups}")

            # 获取所有配置了负载字段关联的CI类型ID
            ci_types_with_attrs = LoadCITypeAttribute.query.filter_by(
                deleted=False
            ).with_entities(LoadCITypeAttribute.type_id).distinct().all()
            ci_type_ids_with_attrs = {item[0] for item in ci_types_with_attrs}
            current_app.logger.debug(f"获取所有配置了负载字段的CI类型ID: {ci_type_ids_with_attrs}")

            # 过滤分组，只保留有负载字段关联的CI类型
            filtered_groups = []
            for group in all_groups:
                # 跳过没有ci_types字段的分组
                if 'ci_types' not in group:
                    current_app.logger.debug(f"跳过分组 {group.get('name', 'Unknown')}，因为它没有 ci_types 字段")
                    continue

                # 过滤CI类型，只保留有负载字段关联的
                filtered_ci_types = [
                    ci_type for ci_type in group['ci_types']
                    if ci_type['id'] in ci_type_ids_with_attrs
                ]
                current_app.logger.debug(f"分组 {group.get('name', 'Unknown')} 过滤后的CI类型: {filtered_ci_types}")

                # 如果过滤后还有CI类型，则保留该分组
                if filtered_ci_types:
                    filtered_group = group.copy()
                    filtered_group['ci_types'] = filtered_ci_types
                    filtered_groups.append(filtered_group)
                    current_app.logger.debug(f"保留分组 {filtered_group.get('name', 'Unknown')}")

            current_app.logger.debug(f"成功获取分组形式的负载字段关联CI类型信息: {filtered_groups}")
            return filtered_groups

        except Exception as e:
            current_app.logger.error(f"获取分组形式的负载字段关联CI类型失败: {str(e)}")
            return abort(500, f"获取分组形式的负载字段关联CI类型失败: {str(e)}")

class LoadRelationsManager:
    """CI类型与负载字段关联关系管理类"""

    @staticmethod
    def get_type_attrs(type_id):
        """
        获取CI类型关联的所有字段
        Args:
            type_id: CI类型ID
        Returns:
            LoadAttribute对象列表
        """
        return CITypeLoadAttrCache.get_by_type(type_id)

    @staticmethod
    def get_type_relations(attr_id):
        """
        获取字段关联的所有CI类型
        Args:
            attr_id: 字段ID
        Returns:
            LoadCITypeAttribute对象列表
        """
        return CITypeLoadAttrCache.get_by_attr(attr_id)

    @classmethod
    def add_type_attr(cls, type_id, attr_id, is_required=False, order=0):
        """添加CI类型字段关联"""
        # 检查是否已存在关联
        existing = LoadCITypeAttribute.query.filter_by(
            type_id=type_id,
            load_attr_id=attr_id,
            deleted=False
        ).first()
        if existing:
            return abort(400, ErrFormat.load_attr_referenced.format(attr_id))

        # 检查字段是否存在
        attr = LoadAttrManager.get_attr(attr_id)
        if not attr:
            return abort(400, ErrFormat.load_attr_not_found.format(attr_id))

        try:
            relation = LoadCITypeAttribute.create(
                type_id=type_id,
                load_attr_id=attr_id,
                is_required=is_required,
                order=order
            )

            # 清理缓存
            CITypeLoadAttrCache.clean_relation(type_id=type_id, attr_id=attr_id)

            return relation
        except Exception as e:
            current_app.logger.error(f"添加CI类型字段关联失败: {str(e)}")
            return abort(400, ErrFormat.load_attr_create_failed.format(attr_id))

    @classmethod
    def update_type_attr(cls, type_id, attr_id, **kwargs):
        """更新CI类型字段关联"""
        relation = LoadCITypeAttribute.query.filter_by(
            type_id=type_id,
            load_attr_id=attr_id,
            deleted=False
        ).first()
        if not relation:
            return abort(400, ErrFormat.load_attr_not_found.format(attr_id))

        try:
            relation.update(**kwargs)
            CITypeLoadAttrCache.clean_relation(type_id=type_id, attr_id=attr_id)
            return relation
        except Exception as e:
            current_app.logger.error(f"更新CI类型字段关联失败: {str(e)}")
            return abort(400, ErrFormat.load_attr_update_failed.format(attr_id))

    @classmethod
    def delete_type_attr(cls, type_id, attr_id):
        """删除CI类型字段关联"""
        relation = LoadCITypeAttribute.query.filter_by(
            type_id=type_id,
            load_attr_id=attr_id,
            deleted=False
        ).first()
        if not relation:
            return abort(400, ErrFormat.load_attr_not_found.format(attr_id))

        try:
            relation.soft_delete()
            CITypeLoadAttrCache.clean_relation(type_id=type_id, attr_id=attr_id)
            return True
        except Exception as e:
            current_app.logger.error(f"删除CI类型字段关联失败: {str(e)}")
            return abort(400, ErrFormat.load_attr_delete_failed.format(attr_id))

    @classmethod
    def batch_update_type_attrs(cls, type_id, attr_configs):
        """
        批量更新CI类型字段关联
        Args:
            type_id: CI类型ID
            attr_configs: 字段配置列表，格式：[{attr_id: xxx, is_required: xxx, order: xxx}, ...]
        Returns:
            bool
        """
        # 获取现有关联
        existing_relations = {r.load_attr_id: r for r in cls.get_type_attrs(type_id)}

        # 更新或添加关联
        for config in attr_configs:
            attr_id = config.get('attr_id')
            if not attr_id:
                continue

            if attr_id in existing_relations:
                # 更新现有关联
                cls.update_type_attr(
                    type_id=type_id,
                    attr_id=attr_id,
                    is_required=config.get('is_required', False),
                    order=config.get('order', 0)
                )
            else:
                # 添加新关联
                cls.add_type_attr(
                    type_id=type_id,
                    attr_id=attr_id,
                    is_required=config.get('is_required', False),
                    order=config.get('order', 0)
                )

        # 删除未在配置中的关联
        config_attr_ids = {c.get('attr_id') for c in attr_configs if c.get('attr_id')}
        for attr_id in existing_relations:
            if attr_id not in config_attr_ids:
                cls.delete_type_attr(type_id=type_id, attr_id=attr_id)

        return True

class LoadDataManager:
    """负载数据管理类"""

    BATCH_SIZE = 5000  # 批量插入的大小

    @classmethod
    def query_data(cls,
                  type_id: int,
                  ci_ids: List[int] = None,
                  unique_values: List[str] = None,
                  attribute_ids: List[int] = None,
                  start_period: str = None,
                  end_period: str = None,
                  period_type: str = PeriodTypeEnum.DAILY,
                  time_slot: str = None,  # 新增参数
                  page: int = 1,
                  page_size: int = 20) -> Dict[str, Any]:
        """查询指定资源的负载数据 - 支持时间段过滤"""
        from api.lib.cmdb.ci import CIManager
        from api.lib.cmdb.ci_type import CITypeManager

        # 验证时间段参数
        if time_slot:
            time_slot = TimeSlotValidator.validate_and_normalize(time_slot)

        # 参数检查与处理
        if not attribute_ids:
            return cls._empty_result(page, page_size)

        # 如果提供unique_values，需要先转换为ci_ids
        if unique_values:
            ci_type = CITypeManager.check_is_existed(type_id)
            unique_key = AttributeCache.get(ci_type.unique_id)
            ci_ids = CIManager.get_cis_by_unique_values(unique_key, unique_values, type_id)
            if not ci_ids:
                return cls._empty_result(page, page_size)

        # 获取属性定义
        attributes = LoadAttrCache.get_attrs_by_ids(attribute_ids) if attribute_ids else []
        if not attributes:
            return cls._empty_result(page, page_size)

        # 优化：使用联合查询获取所有符合条件的CI-时间组合
        ci_period_pairs = cls._get_ci_period_pairs(
            type_id=type_id,
            ci_ids=ci_ids,
            attributes=attributes,
            start_period=start_period,
            end_period=end_period,
            period_type=period_type,
            time_slot=time_slot
        )

        # 如果没有找到符合条件的组合，直接返回空结果
        if not ci_period_pairs:
            return cls._empty_result(page, page_size)

        # 去重并排序
        ci_period_pairs = list(set(ci_period_pairs))
        ci_period_pairs.sort(key=lambda x: (x[1], x[2]))  # 按unique_value和period排序

        # 分页处理
        total = len(ci_period_pairs)
        start_idx = (page - 1) * page_size
        end_idx = min(start_idx + page_size, total)
        page_pairs = ci_period_pairs[start_idx:end_idx]

        # 提取当前页的CI ID和期间，用于后续查询
        page_ci_ids = [pair[0] for pair in page_pairs]
        page_unique_period_pairs = [(pair[1], pair[2]) for pair in page_pairs]

        # 优化：批量查询所有属性的值
        result = cls._get_attribute_values(
            attributes=attributes,
            page_ci_ids=page_ci_ids,
            page_unique_period_pairs=page_unique_period_pairs,
            period_type=period_type,
            time_slot=time_slot
        )

        # 确保结果包含所有页面的CI-period组合，即使某些属性没有值
        for unique_value, period in page_unique_period_pairs:
            unique_str = str(unique_value)
            if unique_str not in result or period not in result[unique_str]:
                result[unique_str][period] = {}

        # 在返回结果中包含时间段信息
        result_data = {
            "page": page,
            "page_size": page_size, 
            "total": total,
            "data": dict(result)
        }
        
        # 只在有时间段时添加相关信息
        if time_slot:
            result_data.update({
                "time_slot": time_slot,
                "time_slot_description": TimeSlotEnum.get_slot_description(time_slot)
            })
        
        return result_data

    @classmethod
    def _empty_result(cls, page, page_size):
        """返回空结果"""
        return {
            'total': 0,
            'page': page,
            'page_size': page_size,
            'data': {}
        }

    @classmethod
    def _get_ci_period_pairs(cls, type_id, ci_ids, attributes, start_period, end_period, period_type, time_slot=None):
        """获取所有符合条件的CI-时间组合"""
        # 从缓存中获取结果
        attribute_ids = [attr.id for attr in attributes]
        cached_pairs = LoadDataQueryCache.get(
            type_id=type_id,
            ci_ids=ci_ids,
            attribute_ids=attribute_ids,
            start_period=start_period,
            end_period=end_period,
            period_type=period_type,
            time_slot=time_slot
        )

        # 如果缓存命中，直接返回缓存的结果
        if cached_pairs is not None:
            current_app.logger.debug(f"从缓存获取CI-时间组合，共 {len(cached_pairs)} 个")
            return cached_pairs

        # 缓存未命中，执行查询
        current_app.logger.debug("缓存未命中，执行查询获取CI-时间组合")
        ci_period_pairs = []

        # 使用 UNION 查询减少数据库查询次数
        for attr in attributes:
            table = LoadValueTypeMap.get_table(attr.value_type)
            if not table:
                continue

            # 构建基础查询
            query = db.session.query(
                table.ci_id,
                table.unique_value,
                table.collect_date if not (attr.is_monthly or period_type == PeriodTypeEnum.MONTHLY) else table.collect_month
            ).filter(
                ~table.deleted,
                table.load_attr_id == attr.id,
                CI.type_id == type_id
            ).join(
                CI, CI.id == table.ci_id
            )

            # 添加CI过滤条件
            if ci_ids:
                query = query.filter(table.ci_id.in_(ci_ids))

            # 添加日期过滤条件
            date_field = table.collect_month if (attr.is_monthly or period_type == PeriodTypeEnum.MONTHLY) else table.collect_date
            if start_period:
                start_val = start_period[:7] if (attr.is_monthly or period_type == PeriodTypeEnum.MONTHLY) else start_period
                query = query.filter(date_field >= start_val)
            if end_period:
                end_val = end_period[:7] if (attr.is_monthly or period_type == PeriodTypeEnum.MONTHLY) else end_period
                query = query.filter(date_field <= end_val)

            # 添加时间段过滤条件（只对日度数据有效）
            if time_slot and not (attr.is_monthly or period_type == PeriodTypeEnum.MONTHLY):
                query = query.filter(table.time_slot == time_slot)

            # 执行查询并存储结果
            try:
                pairs = query.distinct().all()
                current_app.logger.debug(f"属性 {attr.name} 查询到 {len(pairs)} 个符合条件的CI-时间组合")

                # 标准化并存储结果
                for pair in pairs:
                    ci_id, unique_value, period_val = pair
                    # 统一日期格式
                    if isinstance(period_val, (datetime.date, datetime.datetime)):
                        period_str = period_val.strftime('%Y-%m-%d')
                    else:
                        period_str = period_val

                    ci_period_pairs.append((ci_id, unique_value, period_str))
            except Exception as e:
                current_app.logger.error(f"查询属性 {attr.name} 的CI-时间组合失败: {str(e)}")

        # 将结果存入缓存
        LoadDataQueryCache.set(
            type_id=type_id,
            ci_ids=ci_ids,
            attribute_ids=attribute_ids,
            start_period=start_period,
            end_period=end_period,
            period_type=period_type,
            data=ci_period_pairs,
            time_slot=time_slot
        )

        return ci_period_pairs

    @classmethod
    def _get_attribute_values(cls, attributes, page_ci_ids, page_unique_period_pairs, period_type, time_slot=None):
        """获取属性值"""
        result = defaultdict(lambda: defaultdict(dict))

        # 优化：按照值类型分组查询，减少数据库查询次数
        value_type_attrs = defaultdict(list)
        for attr in attributes:
            table = LoadValueTypeMap.get_table(attr.value_type)
            if table:
                value_type_attrs[table].append(attr)

        # 按值类型批量查询
        for value_table, attrs in value_type_attrs.items():
            attr_ids = [attr.id for attr in attrs]

            # 构建基本查询
            query = db.session.query(value_table).filter(
                ~value_table.deleted,
                value_table.load_attr_id.in_(attr_ids),
                value_table.ci_id.in_(page_ci_ids)
            )

            # 构建 OR 条件来精确定位当前页的CI-period组合
            monthly_attrs = [attr for attr in attrs if attr.is_monthly or period_type == PeriodTypeEnum.MONTHLY]
            daily_attrs = [attr for attr in attrs if not attr.is_monthly and period_type != PeriodTypeEnum.MONTHLY]

            or_conditions = []

            # 处理月度属性
            if monthly_attrs:
                monthly_attr_ids = [attr.id for attr in monthly_attrs]
                for unique_value, period in page_unique_period_pairs:
                    or_conditions.append(
                        db.and_(
                            value_table.load_attr_id.in_(monthly_attr_ids),
                            value_table.unique_value == unique_value,
                            value_table.collect_month == period
                        )
                    )

            # 处理日度属性
            if daily_attrs:
                daily_attr_ids = [attr.id for attr in daily_attrs]
                for unique_value, period in page_unique_period_pairs:
                    or_conditions.append(
                        db.and_(
                            value_table.load_attr_id.in_(daily_attr_ids),
                            value_table.unique_value == unique_value,
                            value_table.collect_date == period
                        )
                    )

            # 只有在有条件时才添加过滤器
            if or_conditions:
                query = query.filter(db.or_(*or_conditions))

            # 添加时间段过滤条件（只对日度数据有效）
            if time_slot and daily_attrs:
                query = query.filter(
                    db.or_(
                        value_table.load_attr_id.in_([attr.id for attr in monthly_attrs]) if monthly_attrs else db.false(),
                        db.and_(
                            value_table.load_attr_id.in_([attr.id for attr in daily_attrs]),
                            value_table.time_slot == time_slot
                        )
                    )
                )

            try:
                # 执行查询
                values = query.all()

                # 将查询结果填充到结果结构中
                attr_map = {attr.id: attr for attr in attrs}
                for value in values:
                    attr = attr_map.get(value.load_attr_id)
                    if not attr:
                        continue

                    if attr.is_monthly or period_type == PeriodTypeEnum.MONTHLY:
                        period_str = value.collect_month
                    else:
                        period_str = value.collect_date.strftime('%Y-%m-%d')

                    unique_str = str(value.unique_value)
                    result[unique_str][period_str][attr.name] = LoadValueTypeMap.deserialize_value(
                        attr.value_type,
                        value.value
                    )
            except Exception as e:
                current_app.logger.error(f"查询值类型 {value_table.__name__} 的属性值失败: {str(e)}")

        return result

    @classmethod
    def validate_data(cls, type_id: int, data: List[Dict], time_slot: str = None) -> Tuple[List[Dict], List[Dict]]:
        """验证数据格式并转换为数据库记录格式 - 支持时间段
        
        Args:
            type_id: CI类型ID
            data: 原始数据列表
            time_slot: 时间段标识(可选，默认为忙时)
            
        Returns:
            Tuple[List[Dict], List[Dict]]: (有效记录列表, 无效记录列表)
        """
        # 验证时间段参数 - 不传时也使用默认值
        validated_time_slot = TimeSlotValidator.validate_and_normalize(time_slot)
        
        from api.lib.cmdb.ci import CIManager, CITypeManager
        try:
            ci_type = CITypeManager.check_is_existed(type_id)
            unique_key = AttributeCache.get(ci_type.unique_id)
            attributes = CITypeLoadAttrCache.get_by_type(type_id)
            if not attributes:
                return abort(400, ErrFormat.ci_type_not_found2.format(type_id))
        except Exception as e:
            current_app.logger.error(f"获取CI类型信息失败: {str(e)}")
            return abort(400, ErrFormat.argument_value_invalid.format(str(e)))

        # 获取CI类型的属性定义
        type_attrs = LoadRelationsManager.get_type_attrs(type_id)
        # 同时使用name和alias作为键
        attr_map = {}
        for attr in type_attrs:
            attr_map[attr.name] = attr
            if attr.alias:
                attr_map[attr.alias] = attr

        valid_records = []
        invalid_records = []

        for record in data:
            try:
                unique_value = record.get('unique_value')
                if not unique_value:
                    raise ValueError("缺少unique_value字段")

                # 验证CI存在性
                ci = CIManager.ci_is_exist(unique_key, unique_value, type_id)
                if not ci:
                    raise ValueError(f"CI不存在: {unique_value}")

                # 处理每个时间点的数据
                for period, values in record.items():
                    if period == 'unique_value':
                        continue

                    # 验证日期格式
                    is_monthly = len(period.split('-')) == 2
                    try:
                        if is_monthly:
                            datetime.datetime.strptime(period, '%Y-%m')
                        else:
                            datetime.datetime.strptime(period, '%Y-%m-%d')
                    except ValueError:
                        raise ValueError(f"无效的日期格式: {period}")

                    # 处理每个字段值
                    for field_name, value in values.items():
                        attr = attr_map.get(field_name)
                        if not attr:
                            raise ValueError(f"未知的字段: {field_name}")

                        # 验证月度属性
                        if attr.is_monthly and not is_monthly:
                            raise ValueError(f"属性 {field_name} 需要按月录入")

                        # ⭐ 关键：保持原有的值序列化逻辑
                        validated_value = LoadValueTypeMap.serialize_value(
                            attr.value_type, value)

                        # 构建数据库记录 - 在原有基础上扩展
                        record_data = {
                            'ci_id': ci.id,
                            'unique_value': unique_value,
                            'load_attr_id': attr.id,
                            'value': validated_value,
                            'collect_date': None if is_monthly else datetime.datetime.strptime(period, '%Y-%m-%d').date(),
                            'collect_month': period if is_monthly else None
                        }
                        
                        # 设置时间段字段（对于日度数据）
                        if not is_monthly and not attr.is_monthly:
                            record_data['time_slot'] = validated_time_slot
                        else:
                            record_data['time_slot'] = None
                            
                        valid_records.append(record_data)

            except Exception as e:
                invalid_records.append({
                    'record': str(record),
                    'error': str(e)
                })
                continue

        return valid_records, invalid_records

    @staticmethod
    def batch_import(type_id: int, data: list, time_slot: str = None, 
                    batch_size: int = 5000, file_name: str = None) -> dict:
        """批量导入负载数据的入口方法 - 支持时间段
        
        Args:
            type_id: CI类型ID
            data: 要导入的数据列表
            time_slot: 时间段标识(可选)
            batch_size: 批次大小
            file_name: 文件名
            
        Returns:
            dict: 导入结果
        """
        from api.tasks.cmdb import batch_import_load_data

        # 验证时间段参数
        validated_time_slot = TimeSlotValidator.validate_and_normalize(time_slot) if time_slot else None

        # 保持原有的历史记录创建逻辑
        history = LoadHistoryManager.add(
            type_id=type_id,
            batch_size=batch_size,
            file_name=file_name,
            status=HistoryStatusEnum.BEGINSYNC
        )
        current_app.logger.debug(f"创建历史记录: id={history.id}")

        # 保持原有的提交和验证逻辑
        db.session.commit()

        test_history = LoadDataImportHistory.get_by_id(history.id)
        if not test_history:
            raise ValueError(f"历史记录创建失败: {history.id}")

        # 创建异步任务，传递时间段参数
        task = batch_import_load_data.apply_async(
            args=(type_id, data, batch_size, history.id, validated_time_slot),
            queue=CMDB_QUEUE
        )

        # 返回结果，包含时间段信息
        result = {
            'total': len(data),
            'batch_size': batch_size,
            'history_id': history.id,
            'status': 'submitted'
        }
        
        # 只在有时间段时添加相关信息
        if validated_time_slot:
            result.update({
                'time_slot': validated_time_slot,
                'time_slot_description': TimeSlotEnum.get_slot_description(validated_time_slot)
            })
            
        return result

    @staticmethod
    def batch_import_async(type_id: int, data: List[Dict], history_id: int, 
                          batch_size: int = 5000, time_slot: str = None) -> Dict[str, Any]:
        """异步执行批量导入负载数据 - 支持时间段

        Args:
            type_id: CI类型ID
            data: 要导入的数据列表
            history_id: 导入历史记录ID
            batch_size: 批次大小，默认5000
            time_slot: 时间段标识(可选)

        Returns:
            Dict[str, Any]: 导入结果统计
        """
        try:
            history = LoadDataImportHistory.get_by_id(history_id)
            if not history:
                raise ValueError(f"导入历史记录不存在: {history_id}")
            
            # 更新状态为处理中
            history.update(status=HistoryStatusEnum.PROCESSING)

            # 使用扩展后的验证方法
            valid_records, invalid_records = LoadDataManager.validate_data(
                type_id, data, time_slot
            )
            current_app.logger.debug(f"验证和转换数据完成, 有效记录数: {len(valid_records)}, 无效记录数: {len(invalid_records)}")
            
            if invalid_records:
                current_app.logger.warning(f"数据验证失败: {len(invalid_records)} 条记录")

            # 使用扩展后的批量写入方法
            insert_count, update_count = LoadDataManager._batch_write_records(
                valid_records,
                batch_size,
                history=history
            )

            # 更新历史记录完成状态
            history.update(
                total_count=len(valid_records) + len(invalid_records),
                success_count=insert_count + update_count,
                error_count=len(invalid_records),
                status=HistoryStatusEnum.COMPLETED if len(invalid_records) == 0 else HistoryStatusEnum.FAILED,
                end_time=datetime.datetime.now()
            )

            # 返回结果
            result = {
                'total': len(data),
                'success': insert_count + update_count,
                'inserted': insert_count,
                'updated': update_count,
                'failed': len(invalid_records),
                'errors': invalid_records,
                'history_id': history.id
            }
            
            # 只在有时间段时添加相关信息
            if time_slot:
                result['time_slot'] = time_slot
                
            return result

        except Exception as e:
            current_app.logger.debug(f"异步导入负载数据失败: {str(e)}")
            if history:
                history.update(
                    status=HistoryStatusEnum.FAILED,
                    error_message=str(e),
                    end_time=datetime.datetime.now()
                )
            raise

    @classmethod
    def _batch_write_records(cls, records: List[Dict], batch_size: int, history: LoadDataImportHistory = None) -> Tuple[int, int]:
        """批量写入记录到数据库

        Args:
            records: 记录列表
            batch_size: 批次大小
            history: 导入历史记录对象

        Returns:
            Tuple[int, int]: (插入数量, 更新数量)
        """
        insert_count = 0
        update_count = 0
        processed_count = 0

        # 按值类型分组数据
        grouped_data = defaultdict(list)
        for record in records:
            value_table = LoadValueTypeMap.get_table_by_attr_id(record['load_attr_id'])
            if value_table:
                grouped_data[value_table].append(record)

        # 批量写入每种类型的数据
        for value_table, table_records in grouped_data.items():
            current_app.logger.debug(f"正在向 {value_table.__tablename__} 写入 {len(table_records)} 条记录")

            for i in range(0, len(table_records), batch_size):
                batch = table_records[i:i + batch_size]
                current_batch = i // batch_size + 1

                try:
                    # 动态构建SQL字段列表以支持时间段
                    base_fields = "(ci_id, unique_value, load_attr_id, value, collect_date, collect_month, created_at, deleted"
                    base_values = "(:ci_id, :unique_value, :load_attr_id, :value, :collect_date, :collect_month, NOW(), 0"
                    
                    # 检查是否需要包含时间段字段
                    has_time_slot = any('time_slot' in record and record['time_slot'] is not None for record in batch)
                    if has_time_slot:
                        fields = base_fields + ", time_slot)"
                        values = base_values + ", :time_slot)"
                    else:
                        fields = base_fields + ")"
                        values = base_values + ")"

                    # 构建UPSERT语句
                    insert_stmt = f"""
                        INSERT INTO {value_table.__tablename__}
                        {fields}
                        VALUES
                        {values}
                        ON DUPLICATE KEY UPDATE
                        value = VALUES(value),
                        updated_at = NOW()
                    """

                    # 执行批量UPSERT
                    result = db.session.execute(insert_stmt, batch)
                    affected_rows = result.rowcount

                    # 计算插入和更新的数量
                    if affected_rows > 0:
                        n = len(batch)
                        inserted = 2 * n - affected_rows
                        updated = affected_rows - n

                        insert_count += inserted
                        update_count += updated

                    processed_count += len(batch)

                    # 更新历史记录进度
                    if history:
                        history.update(
                            success_count=insert_count + update_count,
                            current_batch=current_batch,
                            processed_count=processed_count
                        )

                    current_app.logger.debug(
                        f"批次 {current_batch} 处理完成: 新增 {inserted} 条, 更新 {updated} 条, "
                        f"总处理数 {processed_count}/{len(records)}"
                    )

                except Exception as e:
                    current_app.logger.error(f"批量写入失败: {str(e)}")
                    db.session.rollback()
                    raise

        db.session.commit()
        return insert_count, update_count
