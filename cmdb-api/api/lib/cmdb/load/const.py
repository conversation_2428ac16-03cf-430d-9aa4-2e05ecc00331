from api.lib.utils import BaseEnum

class LoadValueTypeEnum(BaseEnum):
    INT = "0"
    FLOAT = "1"
    TEXT = "2"
    LIST = "3"

    @classmethod
    def get_key(cls, value):
        """根据枚举值获取对应的键名
        Args:
            value: 枚举值
        Returns:
            str: 键名，如果未找到返回None
        """
        for k, v in cls.__dict__.items():
            if not k.startswith('_') and v == value:
                return k
        return None

class OperateTypeEnum(BaseEnum):
    ADD_DEVICE = "0"
    REMOVE_DEVICE = "1"
    MOVE_DEVICE = "2"

class PeriodTypeEnum(BaseEnum):
    DAILY = "0"
    MONTHLY = "1"
    
    @classmethod
    def validate_period(cls, period_type, period):
        """验证周期格式"""
        from datetime import datetime
        try:
            if period_type == cls.DAILY:
                datetime.strptime(period, '%Y-%m-%d')
            elif period_type == cls.MONTHLY:
                datetime.strptime(period, '%Y-%m')
            else:
                return False
            return True
        except ValueError:
            return False

class HistoryStatusEnum(BaseEnum):
    BEGINSYNC = "0"
    PENDING = "1"
    PROCESSING = "2"
    COMPLETED = "3"
    FAILED = "4"

class TimeSlotEnum(BaseEnum):
    """时间段枚举 - 闲时和忙时"""
    IDLE_TIME = "00"        # 00:00-08:00 闲时
    BUSY_TIME = "08"        # 08:00-18:00 忙时
    
    # 默认时段设置为忙时
    DEFAULT_SLOT = BUSY_TIME
    
    @classmethod
    def get_slot_description(cls, slot_value):
        """获取时间段描述"""
        descriptions = {
            cls.IDLE_TIME: "闲时(00:00-08:00)",
            cls.BUSY_TIME: "忙时(08:00-18:00)"
        }
        return descriptions.get(slot_value, "未知时段")
    
    @classmethod
    def get_all_slots_info(cls):
        """获取所有时间段信息 - 便于前端展示"""
        return [
            {"value": cls.IDLE_TIME, "label": "闲时", "time_range": "00:00-08:00"},
            {"value": cls.BUSY_TIME, "label": "忙时", "time_range": "08:00-18:00"}
        ]
    
    @classmethod
    def validate_slot(cls, slot_value):
        """验证时间段值"""
        return slot_value in cls.all()