# -*- coding:utf-8 -*-

from api.lib.cmdb.load.const import LoadValueTypeEnum, TimeSlotEnum, PeriodTypeEnum
from api.lib.cmdb.cache import LoadAttrCache
from api.models.cmdb import LoadValueInt, LoadValueFloat, LoadValueText, LoadValueList
from api.lib.cmdb.resp_format import ErrFormat
import json  # 添加json导入


class LoadValueTypeMap:
    """负载数据字段类型与存储表的映射关系管理"""

    # 字段类型到反序列化函数的映射
    deserialize = {
        LoadValueTypeEnum.INT: int,
        LoadValueTypeEnum.FLOAT: float,
        LoadValueTypeEnum.TEXT: str,
        LoadValueTypeEnum.LIST: lambda x: x,  # 直接返回列表，因为SQLAlchemy已经处理了JSON转换
    }
    
    # 字段类型到序列化函数的映射
    serialize = {
        LoadValueTypeEnum.INT: int,
        LoadValueTypeEnum.FLOAT: float,
        LoadValueTypeEnum.TEXT: str,
        LoadValueTypeEnum.LIST: lambda x: json.dumps(
            [x.strip() for x in x.split(',')] if isinstance(x, str) 
            else ([x] if isinstance(x, (int, float)) else x)
        ),  # 转换为JSON字符串
    }
    
    # 字段类型到存储表的映射
    table = {
        LoadValueTypeEnum.INT: LoadValueInt,
        LoadValueTypeEnum.FLOAT: LoadValueFloat,
        LoadValueTypeEnum.TEXT: LoadValueText,
        LoadValueTypeEnum.LIST: LoadValueList,  # 添加LIST类型对应的表
    }

    @classmethod
    def get_table(cls, value_type: str):
        """
        获取字段类型对应的存储表
        Args:
            value_type: 字段类型
        Returns:
            对应的Model类
        """
        return cls.table.get(value_type)

    @classmethod
    def serialize_value(cls, value_type: str, value):
        """序列化值"""
        if value == '-':
            return None
            
        # 获取类型名称用于错误信息
        type_name = LoadValueTypeEnum.get_key(value_type) or value_type
            
        # 对所有类型进行空值校验
        if value is None:
            raise ValueError(f"{type_name}类型不允许空值")
        if isinstance(value, str):
            # 检查是否为空字符串
            if not value.strip():
                raise ValueError(f"{type_name}类型不允许空字符串")
            # 对于LIST类型，检查是否包含空值
            if value_type == LoadValueTypeEnum.LIST:
                items = [x.strip() for x in value.split(',')]
                if any(not item for item in items):  # 如果有任何一项为空
                    raise ValueError(f"{type_name}类型不允许包含空值")
            
        serialize_func = cls.serialize.get(value_type)
        if serialize_func and value is not None:
            try:
                return serialize_func(value)
            except (ValueError, TypeError) as e:
                raise ValueError(f"值{value}无法转换为{type_name}类型: {str(e)}")
        return value

    @classmethod
    def deserialize_value(cls, value_type: str, value):
        """反序列化值"""
        if value_type == LoadValueTypeEnum.LIST:
            return value if value else []  # 直接返回，因为SQLAlchemy已经处理了JSON转换

        # 获取类型名称用于错误信息
        type_name = LoadValueTypeEnum.get_key(value_type) or value_type

        deserialize_func = cls.deserialize.get(value_type)
        if deserialize_func and value is not None:
            try:
                return deserialize_func(value)
            except (ValueError, TypeError) as e:
                raise ValueError(f"值{value}无法从{type_name}类型转换: {str(e)}")
        return value
    
    @classmethod
    def get_table_by_attr_id(cls, attr_id: int):
        """根据属性ID获取对应的值表"""
        attr = LoadAttrCache.get(attr_id)
        if not attr:
            return None
        return cls.get_table(attr.value_type)

class LoadTableMap:
    """根据字段类型动态选择存储表"""
    
    def __init__(self, field_name=None, field=None):
        """
        初始化
        Args:
            field_name: 字段名称
            field: 字段对象
        """
        self.field_name = field_name
        self.field = field
    
    @property
    def table(self):
        """
        获取存储表
        Returns:
            对应的Model类
        """
        if not self.field:
            self.field = LoadAttrCache.get(self.field_name)
            if not self.field:
                raise ValueError(f"字段{self.field_name}不存在")
        
        table = LoadValueTypeMap.get_table(self.field.value_type)
        if not table:
            raise ValueError(f"字段类型{self.field.value_type}没有对应的存储表")
        
        return table

    def serialize_value(self, value):
        """
        序列化值
        Args:
            value: 原始值
        Returns:
            序列化后的值
        """
        if not self.field:
            self.field = LoadAttrCache.get(self.field_name)
            if not self.field:
                raise ValueError(f"字段{self.field_name}不存在")
        
        return LoadValueTypeMap.serialize_value(self.field.value_type, value)

    def deserialize_value(self, value):
        """
        反序列化值
        Args:
            value: 数据库中的值
        Returns:
            反序列化后的值
        """
        if not self.field:
            self.field = LoadAttrCache.get(self.field_name)
            if not self.field:
                raise ValueError(f"字段{self.field_name}不存在")
        
        return LoadValueTypeMap.deserialize_value(self.field.value_type, value)

class TimeSlotValidator:
    """时间段验证器 - 统一验证逻辑"""
    
    @staticmethod
    def validate_and_normalize(time_slot: str = None) -> str:
        """验证并规范化时间段参数"""
        from flask import abort
        
        if not time_slot:
            return TimeSlotEnum.DEFAULT_SLOT
        
        if not TimeSlotEnum.validate_slot(time_slot):
            abort(400, ErrFormat.load_attr_time_slot_invalid.format(time_slot))
        
        return time_slot
    
    @staticmethod 
    def should_apply_time_slot(attr, period_type: str) -> bool:
        """判断是否为需要时间段的每日数据"""
        return (not attr.is_monthly and 
                period_type == PeriodTypeEnum.DAILY)