# -*- coding:utf-8 -*-

import time
from flask import abort, current_app

from api.extensions import db
from api.lib.cmdb.cache import AttributeCache
from api.lib.cmdb.perms import CIFilterPermsCRUD
from api.lib.cmdb.resp_format import ErrFormat
from api.lib.perm.acl.cache import UserCache
from api.models.cmdb import (
    AttributeHistory,
    OperationRecord,
)
from api.lib.cmdb.cache import CITypeCache
from api.lib.cmdb.utils import TableMap


class CloudAttributeHistoryManger(object):
    @staticmethod
    def get_records_for_attributes(
        start,
        end,
        username,
        page,
        page_size,
        operate_type,
        business_type,
        type_id,
        record_id=None,
        ci_id=None,
        attr_id=None,
        ci_ids=None,
        more=False,
    ):
        start_time = time.time()
        current_app.logger.debug("开始获取属性历史记录")

        records = db.session.query(OperationRecord, AttributeHistory).join(
            AttributeHistory, OperationRecord.id == AttributeHistory.record_id
        )
        query_time = time.time()
        current_app.logger.debug(f"初始查询构建耗时: {query_time - start_time:.3f}秒")

        if start:
            records = records.filter(OperationRecord.created_at >= start)
        if end:
            records = records.filter(OperationRecord.created_at <= end)
        if type_id:
            if not isinstance(type_id, (list, tuple)):
                type_id = [type_id]
            records = records.filter(OperationRecord.type_id.in_(type_id))
        if username:
            user = UserCache.get(username)
            if user:
                records = records.filter(OperationRecord.uid == user.uid)
            else:
                return abort(404, ErrFormat.user_not_found.format(username))

        filter_time = time.time()
        current_app.logger.debug(f"添加基本过滤条件耗时: {filter_time - query_time:.3f}秒")

        if operate_type:
            records = records.filter(AttributeHistory.operate_type == operate_type)
        if business_type:
            records = records.filter(AttributeHistory.business_type == business_type)
            records = records.filter(AttributeHistory.business_is_active.is_(True))
        if record_id:
            records = records.filter(AttributeHistory.record_id == record_id)
        if ci_id is not None:
            records = records.filter(AttributeHistory.ci_id == ci_id)
        if ci_ids and isinstance(ci_ids, list):
            records = records.filter(AttributeHistory.ci_id.in_(ci_ids))
        if attr_id is not None:
            records = records.filter(AttributeHistory.attr_id == attr_id)

        business_filter_time = time.time()
        current_app.logger.debug(f"添加业务过滤条件耗时: {business_filter_time - filter_time:.3f}秒")

        records = (
            records.order_by(AttributeHistory.id.desc())
            .offset(page_size * (page - 1))
            .limit(page_size)
            .all()
        )
        query_exec_time = time.time()
        current_app.logger.debug(f"执行查询耗时: {query_exec_time - business_filter_time:.3f}秒")
        
        total = len(records)
        current_app.logger.debug(f"获取到记录数: {total}")

        res = {}
        show_attr_set = {}
        show_attr_cache = {}
        for record in records:
            record_id = record.OperationRecord.id
            type_id = record.OperationRecord.type_id
            ci_id = record.AttributeHistory.ci_id
            show_attr_set[ci_id] = None
            show_attr = show_attr_cache.setdefault(
                type_id,
                AttributeCache.get(
                    CITypeCache.get(type_id).show_id
                    or CITypeCache.get(type_id).unique_id
                )
                if CITypeCache.get(type_id)
                else None,
            )
            if show_attr:
                attr_table = TableMap(attr=show_attr).table
                attr_record = attr_table.get_by(
                    attr_id=show_attr.id, ci_id=ci_id, first=True, to_dict=False
                )
                show_attr_set[ci_id] = attr_record.value if attr_record else None

            attr_hist = record.AttributeHistory.to_dict()
            attr_hist["attr"] = AttributeCache.get(attr_hist["attr_id"])
            if attr_hist["attr"]:
                attr_hist["attr_name"] = attr_hist["attr"].name
                attr_hist["attr_alias"] = attr_hist["attr"].alias
                if more:
                    attr_hist["is_list"] = attr_hist["attr"].is_list
                    attr_hist["is_computed"] = attr_hist["attr"].is_computed
                    attr_hist["is_password"] = attr_hist["attr"].is_password
                    attr_hist["default"] = attr_hist["attr"].default
                attr_hist["value_type"] = attr_hist["attr"].value_type
                attr_hist.pop("attr")

            if record_id not in res:
                record_dict = record.OperationRecord.to_dict()
                record_dict["show_attr_value"] = show_attr_set.get(ci_id)
                record_dict["user"] = UserCache.get(record_dict.get("uid"))
                if record_dict["user"]:
                    record_dict["user"] = record_dict["user"].nickname

                res[record_id] = [record_dict, [attr_hist]]
            else:
                res[record_id][1].append(attr_hist)

        process_time = time.time()
        current_app.logger.debug(f"处理查询结果耗时: {process_time - query_exec_time:.3f}秒")

        attr_filter = CIFilterPermsCRUD.get_attr_filter(type_id)
        if attr_filter:
            res = [i for i in res if i.get("attr_name") in attr_filter]

        res = [res[i] for i in sorted(res.keys(), reverse=True)]

        end_time = time.time()
        current_app.logger.debug(f"权限过滤和排序耗时: {end_time - process_time:.3f}秒")
        current_app.logger.debug(f"总耗时: {end_time - start_time:.3f}秒")

        return total, res
    
    @staticmethod
    def update_business_type(record_id, business_type, type_id=None, ticket_id=None, flush=False, commit=True):
        
        AttributeHistory.update_business_type(record_id, business_type)
    
    @staticmethod
    def update_business_active(record_id, type_id=None, ticket_id=None, flush=False, commit=True):
        
        return AttributeHistory.update_business_active(record_id)