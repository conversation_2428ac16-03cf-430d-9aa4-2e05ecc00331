# -*- coding:utf-8 -*-
import copy
import datetime
import json
import jsonpath
import os
from flask import abort
from flask import current_app
from flask_login import current_user
from sqlalchemy import func
from typing import Dict, List, Tuple, Any
import time

from api.extensions import db
from api.lib.cmdb.auto_discovery.const import CLOUD_MAP
from api.lib.cmdb.auto_discovery.const import DEFAULT_INNER
from api.lib.cmdb.auto_discovery.const import PRIVILEGED_USERS
from api.lib.cmdb.cache import AttributeCache
from api.lib.cmdb.cache import AutoDiscoveryMappingCache
from api.lib.cmdb.cache import CITypeAttributeCache
from api.lib.cmdb.cache import CITypeCache
from api.lib.cmdb.ci import CIManager
from api.lib.cmdb.ci_type import CITypeGroupManager
from api.lib.cmdb.const import AutoDiscoveryType
from api.lib.cmdb.const import CMDB_QUEUE
from api.lib.cmdb.const import PermEnum
from api.lib.cmdb.const import ResourceTypeEnum
from api.lib.cmdb.const import PrefectNameEnum
from api.lib.cmdb.custom_dashboard import SystemConfigManager
from api.lib.cmdb.resp_format import ErrFormat
from api.lib.cmdb.search import SearchError
from api.lib.cmdb.search.ci import search as ci_search
from api.lib.common_setting.role_perm_base import CMDBApp
from api.lib.mixin import DBMixin
from api.lib.perm.acl.acl import ACLManager
from api.lib.perm.acl.acl import is_app_admin
from api.lib.perm.acl.acl import validate_permission
from api.lib.utils import AESCrypto
from api.models.cmdb import AutoDiscoveryAccount
from api.models.cmdb import AutoDiscoveryCI
from api.models.cmdb import AutoDiscoveryCIType
from api.models.cmdb import AutoDiscoveryCITypeRelation
from api.models.cmdb import AutoDiscoveryCounter
from api.models.cmdb import AutoDiscoveryExecHistory
from api.models.cmdb import AutoDiscoveryRule
from api.models.cmdb import AutoDiscoveryRuleSyncHistory
from api.tasks.cmdb import build_relations_for_ad_accept
from api.tasks.cmdb import write_ad_rule_sync_history
from api.lib.cmdb.auto_discovery.prefect_manager import PrefectManager
from api.lib.cmdb.const import ExistPolicy
PWD = os.path.abspath(os.path.dirname(__file__))
app_cli = CMDBApp()


def parse_plugin_script(script):
    attributes = []
    try:
        x = compile(script, '', "exec")
        local_ns = {}
        exec(x, {}, local_ns)
        unique_key = local_ns['AutoDiscovery']().unique_key
        attrs = local_ns['AutoDiscovery']().attributes() or []
    except Exception as e:
        return abort(400, str(e))

    if not isinstance(attrs, list):
        return abort(400, ErrFormat.adr_plugin_attributes_list_required)

    for i in attrs:
        if len(i) == 3:
            name, _type, desc = i
        elif len(i) == 2:
            name, _type = i
            desc = ""
        else:
            continue
        attributes.append(dict(name=name, type=_type, desc=desc))

    return unique_key, attributes


def check_plugin_script(**kwargs):
    kwargs['unique_key'], kwargs['attributes'] = parse_plugin_script(kwargs['plugin_script'])

    if not kwargs.get('unique_key'):
        return abort(400, ErrFormat.adr_unique_key_required)

    if not kwargs.get('attributes'):
        return abort(400, ErrFormat.adr_plugin_attributes_list_no_empty)

    return kwargs


def parse_prefect_metadata(metadata: Dict[str, Any]) -> Tuple[str, List[Dict[str, str]]]:
    """
    解析Prefect Flow返回的元数据
    
    Args:
        metadata: Flow返回的元数据字典，包含状态和元数据信息
        
    Returns:
        Tuple[str, List[Dict]]: (unique_key, attributes列表)
    """
    if not isinstance(metadata, dict) or metadata.get('status') != "COMPLETED":
        return abort(400, ErrFormat.prefect_flow_invalid)
    
    metadata_content = metadata.get('metadata', {})
    unique_key = metadata_content.get('unique_key')
    if not unique_key:
        return abort(400, ErrFormat.prefect_unique_key_required)
        
    attrs = metadata_content.get('attributes', [])
    if not isinstance(attrs, list):
        return abort(400, ErrFormat.prefect_attributes_required)
        
    attributes = []
    for attr in attrs:
        if not isinstance(attr, (list, tuple)) or len(attr) < 2:
            continue
            
        name, _type, *desc = attr
        attributes.append(dict(name=name, type=_type, desc=desc[0] if desc else ""))
        
    return unique_key, attributes


def check_prefect_metadata(**kwargs) -> Dict[str, Any]:
    """
    检查Prefect Flow的元数据，并验证创建部署所需的参数。

    Args:
        kwargs: 包含plugin_script的参数字典或JSON字符串。

    Returns:
        Dict: 包含 unique_key 和 attributes 的字典。

    Raises:
        HTTPException: 如果验证失败。
    """
    plugin_script_input = kwargs.get('plugin_script')
    if not plugin_script_input:
        return abort(400, ErrFormat.prefect_plugin_script_required)

    try:
        # 解析plugin_script
        plugin_script = json.loads(plugin_script_input) if isinstance(plugin_script_input, str) else plugin_script_input
        if not isinstance(plugin_script, dict):
            return abort(400, ErrFormat.prefect_plugin_script_invalid)

        # 创建部署并获取元数据
        deployment_id = PrefectManager.create_deployment(
            flow_name=plugin_script.get('flow_name'),
            deployment_name=f"{plugin_script.get('flow_name')}_deployment",
            entrypoint=plugin_script.get('entrypoint'),
            path_to_flow=plugin_script.get('path_to_flow'),
            work_pool_name=plugin_script.get('work_pool_name', 'default-agent-pool'),
            tags=plugin_script.get('tags', []),
            parameters=plugin_script.get('parameters', {})
        )

        if not deployment_id:
            return abort(500, ErrFormat.prefect_deployment_failed)

        state, metadata = PrefectManager.get_flow_metadata(deployment_id)
        if state != "COMPLETED" or not metadata:
            return abort(400, ErrFormat.prefect_metadata_failed.format(state))

        # 解析元数据
        kwargs['unique_key'], kwargs['attributes'] = parse_prefect_metadata({
            "status": state,
            "metadata": metadata
        })

        return kwargs

    except Exception as e:
        current_app.logger.error(f"检查Prefect元数据时出错: {e}", exc_info=True)
        return abort(500, ErrFormat.prefect_check_error.format(str(e)))


class AutoDiscoveryRuleCRUD(DBMixin):
    cls = AutoDiscoveryRule

    @classmethod
    def get_by_name(cls, name):
        return cls.cls.get_by(name=name, first=True, to_dict=False)

    @classmethod
    def get_by_id(cls, _id):
        return cls.cls.get_by_id(_id)

    def get_by_inner(self):
        return self.cls.get_by(is_inner=True, to_dict=True)

    def import_template(self, rules):
        for rule in rules:
            rule.pop("id", None)
            rule.pop("created_at", None)
            rule.pop("updated_at", None)

            existed = self.cls.get_by(name=rule['name'], first=True, to_dict=False)
            if existed is not None:
                existed.update(**rule)
            else:
                self.cls.create(**rule)

    def _can_add(self, **kwargs):
        self.cls.get_by(name=kwargs['name']) and abort(400, ErrFormat.adr_duplicate.format(kwargs['name']))
        if kwargs.get('is_plugin') and kwargs.get('plugin_script'):
            if kwargs.get('type') == AutoDiscoveryType.PREFECT:
                kwargs = check_prefect_metadata(**kwargs)
            else:
                kwargs = check_plugin_script(**kwargs)
            
            acl = ACLManager(app_cli.app_name)
            has_perm = True
            try:
                if not acl.has_permission(app_cli.op.Auto_Discovery,
                                          app_cli.resource_type_name,
                                          app_cli.op.create_plugin) and not is_app_admin(app_cli.app_name):
                    has_perm = False
            except Exception:
                if not is_app_admin(app_cli.app_name):
                    return abort(403, ErrFormat.role_required.format(app_cli.admin_name))

            if not has_perm:
                return abort(403, ErrFormat.no_permission.format(
                    app_cli.op.Auto_Discovery, app_cli.op.create_plugin))

        kwargs['owner'] = current_user.uid

        return kwargs

    def _can_update(self, **kwargs):
        existed = self.cls.get_by_id(kwargs['_id']) or abort(
            404, ErrFormat.adr_not_found.format("id={}".format(kwargs['_id'])))

        if 'name' in kwargs and not kwargs['name']:
            return abort(400, ErrFormat.argument_value_required.format('name'))

        if kwargs.get('name'):
            other = self.cls.get_by(name=kwargs['name'], first=True, to_dict=False)
            if other and other.id != existed.id:
                return abort(400, ErrFormat.adr_duplicate.format(kwargs['name']))

        if existed.is_plugin:
            acl = ACLManager(app_cli.app_name)
            has_perm = True
            try:
                if not acl.has_permission(app_cli.op.Auto_Discovery,
                                          app_cli.resource_type_name,
                                          app_cli.op.update_plugin) and not is_app_admin(app_cli.app_name):
                    has_perm = False
            except Exception:
                if not is_app_admin(app_cli.app_name):
                    return abort(403, ErrFormat.role_required.format(app_cli.admin_name))

            if not has_perm:
                return abort(403, ErrFormat.no_permission.format(
                    app_cli.op.Auto_Discovery, app_cli.op.update_plugin))

        return existed

    def update(self, _id, **kwargs):

        if kwargs.get('is_plugin') and kwargs.get('plugin_script'):
            if kwargs.get('type') == AutoDiscoveryType.PREFECT:
                kwargs = check_prefect_metadata(**kwargs)
            else:
                kwargs = check_plugin_script(**kwargs)

            for item in AutoDiscoveryCIType.get_by(adr_id=_id, to_dict=False):
                item.update(updated_at=datetime.datetime.now())

            return super(AutoDiscoveryRuleCRUD, self).update(_id, filter_none=False, **kwargs)

    def _can_delete(self, **kwargs):
        if AutoDiscoveryCIType.get_by(adr_id=kwargs['_id'], first=True):
            return abort(400, ErrFormat.adr_referenced)

        existed = self.cls.get_by_id(kwargs['_id']) or abort(
            404, ErrFormat.adr_not_found.format("id={}".format(kwargs['_id'])))

        if existed.is_plugin:
            acl = ACLManager(app_cli.app_name)
            has_perm = True
            try:
                if not acl.has_permission(app_cli.op.Auto_Discovery,
                                          app_cli.resource_type_name,
                                          app_cli.op.delete_plugin) and not is_app_admin(app_cli.app_name):
                    has_perm = False
            except Exception:
                if not is_app_admin(app_cli.app_name):
                    return abort(403, ErrFormat.role_required.format(app_cli.admin_name))

            if not has_perm:
                return abort(403, ErrFormat.no_permission.format(
                    app_cli.op.Auto_Discovery, app_cli.op.delete_plugin))

        return existed


class AutoDiscoveryCITypeCRUD(DBMixin):
    cls = AutoDiscoveryCIType

    @classmethod
    def get_all(cls, type_ids=None):
        res = cls.cls.get_by(to_dict=False)
        return [i for i in res if type_ids is None or i.type_id in type_ids]

    @classmethod
    def get_by_id(cls, _id):
        return cls.cls.get_by_id(_id)

    @classmethod
    def get_by_type_id(cls, type_id):
        return cls.cls.get_by(type_id=type_id, to_dict=False)

    @classmethod
    def get_ad_attributes(cls, type_id):
        result = []
        adts = cls.get_by_type_id(type_id)
        for adt in adts:
            adr = AutoDiscoveryRuleCRUD.get_by_id(adt.adr_id)
            if not adr:
                continue
            if adr.type == AutoDiscoveryType.HTTP:
                for i in DEFAULT_INNER:
                    if adr.name == i['name']:
                        attrs = AutoDiscoveryHTTPManager.get_attributes(
                            i['en'], (adt.extra_option or {}).get('category')) or []
                        result.extend([i.get('name') for i in attrs])
                        break
            elif adr.type == AutoDiscoveryType.SNMP:
                attributes = AutoDiscoverySNMPManager.get_attributes()
                result.extend([i.get('name') for i in (attributes or [])])
            else:
                result.extend([i.get('name') for i in (adr.attributes or [])])

        return sorted(list(set(result)))

    @classmethod
    def get(cls, ci_id, oneagent_id, oneagent_name, last_update_at=None):
        """
        OneAgent sync rules
        :param ci_id:
        :param oneagent_id:
        :param oneagent_name:
        :param last_update_at:
        :return:
        """
        result = []
        rules = cls.cls.get_by(to_dict=True)

        for rule in rules:
            if not rule['enabled']:
                continue

            if isinstance(rule.get("extra_option"), dict):
                decrypt_account(rule['extra_option'], rule['uid'])

                if rule['extra_option'].get('_reference'):
                    rule['extra_option'].pop('password', None)
                    rule['extra_option'].pop('secret', None)
                    rule['extra_option'].update(
                        AutoDiscoveryAccountCRUD().get_config_by_id(rule['extra_option']['_reference']))

            if oneagent_id and rule['agent_id'] == oneagent_id:
                result.append(rule)
            elif rule['query_expr']:
                query = rule['query_expr'].lstrip('q').lstrip('=')
                s = ci_search(query, fl=['_id'], count=1000000)
                try:
                    response, _, _, _, _, _ = s.search()
                except SearchError as e:
                    return abort(400, str(e))

                for i in (response or []):
                    if i.get('_id') == ci_id:
                        result.append(rule)
                        break
            elif not rule['agent_id'] and not rule['query_expr'] and rule['adr_id']:
                try:
                    if not int(oneagent_id, 16):  # excludes master
                        continue
                except Exception:
                    pass

                adr = AutoDiscoveryRuleCRUD.get_by_id(rule['adr_id'])
                if not adr:
                    continue
                if adr.type in (AutoDiscoveryType.SNMP, AutoDiscoveryType.HTTP):
                    continue

                result.append(rule)

        ad_rules_updated_at = (SystemConfigManager.get('ad_rules_updated_at') or {}).get('option', {}).get('v') or ""
        new_last_update_at = ""
        for i in result:
            i['adr'] = AutoDiscoveryRule.get_by_id(i['adr_id']).to_dict()
            i['adr'].pop("attributes", None)
            __last_update_at = max([i['updated_at'] or "", i['created_at'] or "",
                                    i['adr']['created_at'] or "", i['adr']['updated_at'] or "", ad_rules_updated_at])
            if new_last_update_at < __last_update_at:
                new_last_update_at = __last_update_at

        write_ad_rule_sync_history.apply_async(args=(result, oneagent_id, oneagent_name, datetime.datetime.now()),
                                               queue=CMDB_QUEUE)
        if not last_update_at or new_last_update_at > last_update_at:
            return result, new_last_update_at
        else:
            return [], new_last_update_at

    @classmethod
    def get_by_adt_id(cls, adt_id):
        """
        获取特定ADT的配置信息（适用于Prefect模式）
        
        Args:
            adt_id: 要获取的ADT ID
            
        Returns:
            Dict: 包含ADT和ADR信息的字典
        """
        # 1. 获取ADT记录
        adt = cls.get_by_id(adt_id)
        if not adt:
            return abort(404, ErrFormat.ad_not_found.format(f"id={adt_id}"))
            
        if not adt.enabled:
            return abort(400, ErrFormat.adt_disabled.format(f"id={adt_id}"))
        
        # 2. 获取关联的ADR记录
        adr = AutoDiscoveryRuleCRUD.get_by_id(adt.adr_id)
        if not adr:
            return abort(404, ErrFormat.adr_not_found.format(f"id={adt.adr_id}"))
        
        # 3. 构建响应对象
        result = {
            "adt": adt.to_dict(),
            "adr": adr.to_dict()
        }
        
        return result

    @staticmethod
    def __valid_exec_target(agent_id, query_expr):
        _is_app_admin = is_app_admin("cmdb")
        if not agent_id and not query_expr and not _is_app_admin:
            return abort(403, ErrFormat.adt_target_all_no_permission)

        if _is_app_admin:
            return

        if agent_id and isinstance(agent_id, str) and agent_id.startswith("0x"):
            agent_id = agent_id.strip()
            q = "op_duty:{0},-rd_duty:{0},oneagent_id:{1}"

            s = ci_search(q.format(current_user.username, agent_id.strip()))
            try:
                response, _, _, _, _, _ = s.search()
                if response:
                    return
            except SearchError as e:
                current_app.logger.warning(e)
                return abort(400, str(e))

            s = ci_search(q.format(current_user.nickname, agent_id.strip()))
            try:
                response, _, _, _, _, _ = s.search()
                if response:
                    return
            except SearchError as e:
                current_app.logger.warning(e)
                return abort(400, str(e))

        if query_expr.strip():
            query_expr = query_expr.strip()
            if query_expr.startswith('q='):
                query_expr = query_expr[2:]

            s = ci_search(query_expr, count=1000000)
            try:
                response, _, _, _, _, _ = s.search()
                for i in response:
                    if (current_user.username not in (i.get('rd_duty') or []) and
                            current_user.username not in (i.get('op_duty') or []) and
                            current_user.nickname not in (i.get('rd_duty') or []) and
                            current_user.nickname not in (i.get('op_duty') or [])):
                        return abort(403, ErrFormat.adt_target_expr_no_permission.format(
                            i.get("{}_name".format(i.get('ci_type')))))
            except SearchError as e:
                current_app.logger.warning(e)
                return abort(400, str(e))

    @staticmethod
    def _process_prefect_type(adr, kwargs):
        """处理Prefect类型的ADR"""
        try:
            # 解析 plugin_script
            plugin_script = json.loads(adr.plugin_script) if isinstance(adr.plugin_script, str) else adr.plugin_script
            if not isinstance(plugin_script, dict):
                return abort(400, ErrFormat.prefect_plugin_script_invalid)
            
            # 生成唯一的部署名称
            deployment_name = PrefectNameEnum.PREFECT_DEPLOYMENT_NAME.format(
                type_id=kwargs['type_id'],
                adr_id=kwargs['adr_id'],
                timestamp=int(time.time()))
            tags = PrefectNameEnum.PREFECT_TAGS
            # 创建 Prefect 部署
            deployment_id = PrefectManager.create_deployment(
                flow_name=plugin_script.get('flow_name'),
                deployment_name=deployment_name,
                entrypoint=plugin_script.get('entrypoint'),
                path_to_flow=plugin_script.get('path_to_flow'),
                work_pool_name=plugin_script.get('work_pool_name', 'default-agent-pool'),
                tags=[tags],
                parameters=plugin_script.get('parameters', {}),
                schedule=kwargs.get('cron', None)
            )
            
            if not deployment_id:
                return abort(500, ErrFormat.prefect_deployment_failed)
            # 保存部署ID到extra_option
            kwargs.setdefault('extra_option', {})
            kwargs['extra_option']['prefect_deployment_id'] = deployment_id
            kwargs['extra_option']['type'] = AutoDiscoveryType.PREFECT
            
        except Exception as e:
            current_app.logger.error(f"创建Prefect部署失败: {e}", exc_info=True)
            return abort(500, ErrFormat.prefect_check_error.format(str(e)))
        
        return kwargs

    @staticmethod
    def _process_http_type(adr, kwargs):
        """处理HTTP类型的ADR（云资源）"""
        kwargs.setdefault('extra_option', dict())
        en_name = None
        for i in DEFAULT_INNER:
            if i['name'] == adr.name:
                en_name = i['en']
                break
                
        if en_name and kwargs['extra_option'].get('category'):
            for item in CLOUD_MAP[en_name]:
                if item["collect_key_map"].get(kwargs['extra_option']['category']):
                    kwargs["extra_option"]["collect_key"] = item["collect_key_map"][
                        kwargs['extra_option']['category']]
                    kwargs["extra_option"]["provider"] = en_name
                    break
        
        return kwargs
        
    @staticmethod
    def _process_components_type(adr, kwargs):
        """处理Components类型的ADR"""
        if kwargs.get('extra_option'):
            for i in DEFAULT_INNER:
                if i['name'] == adr.name:
                    kwargs['extra_option']['collect_key'] = i['option'].get('collect_key')
                    break
        
        return kwargs
        
    @staticmethod
    def _process_snmp_type(adr, kwargs):
        """处理SNMP类型的ADR"""
        # SNMP类型目前没有特殊处理逻辑
        return kwargs
        
    @staticmethod
    def _process_agent_type(adr, kwargs):
        """处理Agent类型的ADR"""
        # Agent类型目前没有特殊处理逻辑
        return kwargs
        
    @staticmethod
    def _process_adr_type(adr, kwargs):
        """根据ADR类型调用对应的处理方法
        
        Args:
            adr: AutoDiscoveryRule实例
            kwargs: ADT创建/更新参数
            
        Returns:
            处理后的kwargs
        """
        type_handlers = {
            AutoDiscoveryType.PREFECT: AutoDiscoveryCITypeCRUD._process_prefect_type,
            AutoDiscoveryType.HTTP: AutoDiscoveryCITypeCRUD._process_http_type,
            AutoDiscoveryType.COMPONENTS: AutoDiscoveryCITypeCRUD._process_components_type,
            AutoDiscoveryType.SNMP: AutoDiscoveryCITypeCRUD._process_snmp_type,
            AutoDiscoveryType.AGENT: AutoDiscoveryCITypeCRUD._process_agent_type
        }
        
        handler = type_handlers.get(adr.type)
        if handler:
            return handler(adr, kwargs)
        return kwargs

    @staticmethod
    def _can_add(**kwargs):
        """添加ADT前的验证和预处理
        
        Args:
            kwargs: ADT创建参数
            
        Returns:
            处理后的kwargs，用于创建ADT
            
        Raises:
            HTTPException: 如果验证失败
        """
        if kwargs.get('adr_id'):
            adr = AutoDiscoveryRule.get_by_id(kwargs['adr_id']) or abort(
                404, ErrFormat.adr_not_found.format("id={}".format(kwargs['adr_id'])))
            
            # 根据ADR类型调用相应的处理方法
            kwargs = AutoDiscoveryCITypeCRUD._process_adr_type(adr, kwargs)

        if kwargs.get('is_plugin') and kwargs.get('plugin_script'):
            kwargs = check_plugin_script(**kwargs) # 这个分支在此处没有执行的可能

        encrypt_account(kwargs.get('extra_option'))

        ci_type = CITypeCache.get(kwargs['type_id'])
        unique = AttributeCache.get(ci_type.unique_id)
        if unique and unique.name not in (kwargs.get('attributes') or {}).values():
            current_app.logger.warning((unique.name, kwargs.get('attributes'), ci_type.alias))
            return abort(400, ErrFormat.ad_not_unique_key.format(unique.name))

        kwargs['uid'] = current_user.uid

        return kwargs

    @staticmethod
    def _after_add(obj, **kwargs):
        type = (obj.extra_option or {}).get('type')
        deployment_id = (obj.extra_option or {}).get('prefect_deployment_id')
        if type == AutoDiscoveryType.PREFECT and deployment_id:
            PrefectManager.update_deployment(deployment_id, {'parameters': {'adt_id': kwargs['_id'],
                                                                            'type_id': kwargs['type_id']}})
            

    @staticmethod
    def _update_prefect_deployment(adr, kwargs, existed):
        """处理 Prefect 类型部署的更新"""
        try:
            # 检查是否有相关字段变更
            deployment_id = (existed.extra_option or {}).get('prefect_deployment_id')
            if not deployment_id:
                current_app.logger.warning(f"ADT {existed.id} 缺少 prefect_deployment_id")
                return existed
                
            # 检查是否需要更新调度
            update_params = {}
            update_params['is_active'] = kwargs.get('enabled', None)
            update_params['cron'] = kwargs.get('cron', None)
            
            if update_params:
                success = PrefectManager.update_deployment(deployment_id, update_params)
                if not success:
                    return abort(500, ErrFormat.prefect_deployment_update_failed.format(deployment_id))
                    
        except Exception as e:
            current_app.logger.error(f"更新Prefect部署失败: {e}", exc_info=True)
            # 记录错误但不阻止更新
            current_app.logger.warning(f"ADT {existed.id} Prefect部署更新失败，继续更新CMDB状态")
        current_app.logger.info(f"ADT {existed.id} Prefect部署更新成功")
        return existed

    @staticmethod
    def _update_http_type(adr, kwargs):
        """处理HTTP类型的ADR更新"""
        kwargs.setdefault('extra_option', dict())
        en_name = None
        for i in DEFAULT_INNER:
            if i['name'] == adr.name:
                en_name = i['en']
                break
        if en_name and kwargs['extra_option'].get('category'):
            for item in CLOUD_MAP[en_name]:
                if item["collect_key_map"].get(kwargs['extra_option']['category']):
                    kwargs["extra_option"]["collect_key"] = item["collect_key_map"][
                        kwargs['extra_option']['category']]
                    kwargs["extra_option"]["provider"] = en_name
                    break
        return kwargs
        
    @staticmethod
    def _update_components_type(adr, kwargs):
        """处理Components类型的ADR更新"""
        if kwargs.get('extra_option'):
            for i in DEFAULT_INNER:
                if i['name'] == adr.name:
                    kwargs['extra_option']['collect_key'] = i['option'].get('collect_key')
                    break
        return kwargs
        
    def _process_update_type(self, adr, kwargs, existed):
        """根据ADR类型处理更新逻辑
        
        Args:
            adr: AutoDiscoveryRule实例
            kwargs: 更新参数
            existed: 现有ADT实例
            
        Returns:
            处理后的对象和参数
        """
        type_handlers = {
            AutoDiscoveryType.PREFECT: lambda: self._update_prefect_deployment(adr, kwargs, existed),
            AutoDiscoveryType.HTTP: lambda: self._update_http_type(adr, kwargs),
            AutoDiscoveryType.COMPONENTS: lambda: self._update_components_type(adr, kwargs),
        }
        
        handler = type_handlers.get(adr.type)
        if handler:
            result = handler()
            if adr.type == AutoDiscoveryType.PREFECT:
                return result  # 返回处理后的existed对象
            else:
                return existed  # 返回原始existed对象，kwargs已被修改
        return existed

    def _can_update(self, **kwargs):
        """更新ADT前的验证和预处理
        
        Args:
            kwargs: ADT更新参数
            
        Returns:
            处理后的ADT实例对象
            
        Raises:
            HTTPException: 如果验证失败
        """
        existed = self.cls.get_by_id(kwargs['_id']) or abort(
            404, ErrFormat.ad_not_found.format("id={}".format(kwargs['_id'])))

        adr = AutoDiscoveryRule.get_by_id(existed.adr_id) or abort(
            404, ErrFormat.adr_not_found.format("id={}".format(existed.adr_id)))
            
        # 根据ADR类型处理更新
        existed = self._process_update_type(adr, kwargs, existed)

        if 'attributes' in kwargs:
            self.__valid_exec_target(kwargs.get('agent_id'), kwargs.get('query_expr'))

            ci_type = CITypeCache.get(existed.type_id)
            unique = AttributeCache.get(ci_type.unique_id)
            if unique and unique.name not in (kwargs.get('attributes') or {}).values():
                current_app.logger.warning((unique.name, kwargs.get('attributes'), ci_type.alias))
                return abort(400, ErrFormat.ad_not_unique_key.format(unique.name))

        if isinstance(kwargs.get('extra_option'), dict) and kwargs['extra_option'].get('secret'):
            if current_user.uid != existed.uid:
                return abort(403, ErrFormat.adt_secret_no_permission)
        if isinstance(kwargs.get('extra_option'), dict) and kwargs['extra_option'].get('password'):
            if current_user.uid != existed.uid:
                return abort(403, ErrFormat.adt_secret_no_permission)

        return existed

    def update(self, _id, **kwargs):

        if kwargs.get('is_plugin') and kwargs.get('plugin_script'):
            kwargs = check_plugin_script(**kwargs)

        encrypt_account(kwargs.get('extra_option'))

        inst = self._can_update(_id=_id, **kwargs)
        if len(kwargs) == 1 and 'enabled' in kwargs:  # enable or disable
            pass
        elif inst.agent_id != kwargs.get('agent_id') or inst.query_expr != kwargs.get('query_expr'):
            for item in AutoDiscoveryRuleSyncHistory.get_by(adt_id=inst.id, to_dict=False):
                item.delete(commit=False)
            db.session.commit()

            SystemConfigManager.create_or_update("ad_rules_updated_at",
                                                 dict(v=datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")))

        obj = inst.update(_id=_id, filter_none=False, **kwargs)

        return obj

    def _can_delete(self, **kwargs):
        """删除ADT前的验证
        
        Args:
            kwargs: 包含要删除的ADT的ID
            
        Returns:
            要删除的ADT实例
            
        Raises:
            HTTPException: 如果验证失败或没有权限
        """
        if AutoDiscoveryCICRUD.get_by_adt_id(kwargs['_id']):
            return abort(400, ErrFormat.cannot_delete_adt)

        existed = self.cls.get_by_id(kwargs['_id']) or abort(
            404, ErrFormat.ad_not_found.format("id={}".format(kwargs['_id'])))
            
        # 处理 Prefect 类型的删除
        adr = AutoDiscoveryRule.get_by_id(existed.adr_id)
        if adr and adr.type == AutoDiscoveryType.PREFECT:
            self._delete_prefect_deployment(existed)

        return existed
        
    @staticmethod
    def _delete_prefect_deployment(existed):
        """删除 Prefect 部署"""
        try:
            deployment_id = (existed.extra_option or {}).get('prefect_deployment_id')
            if deployment_id:
                success = PrefectManager.delete_deployment(deployment_id)
                if not success:
                    current_app.logger.warning(f"删除Prefect部署失败: {deployment_id}")
                    # 记录警告但不阻止删除
        except Exception as e:
            current_app.logger.error(f"删除Prefect部署时发生错误: {e}", exc_info=True)
            # 记录错误但不阻止删除

    def delete(self, _id):
        inst = self._can_delete(_id=_id)

        inst.soft_delete()

        for item in AutoDiscoveryRuleSyncHistory.get_by(adt_id=inst.id, to_dict=False):
            item.delete(commit=False)
        db.session.commit()

        attributes = self.get_ad_attributes(inst.type_id)
        for item in AutoDiscoveryCITypeRelationCRUD.get_by_type_id(inst.type_id):
            if item.ad_key not in attributes:
                item.soft_delete()

        return inst


class AutoDiscoveryCITypeRelationCRUD(DBMixin):
    cls = AutoDiscoveryCITypeRelation

    @classmethod
    def get_all(cls, type_ids=None):
        res = cls.cls.get_by(to_dict=False)
        return [i for i in res if type_ids is None or i.ad_type_id in type_ids]

    @classmethod
    def get_by_type_id(cls, type_id, to_dict=False):
        return cls.cls.get_by(ad_type_id=type_id, to_dict=to_dict)

    def upsert(self, ad_type_id, relations):
        existed = self.cls.get_by(ad_type_id=ad_type_id, to_dict=False)
        existed = {(i.ad_key, i.peer_type_id, i.peer_attr_id): i for i in existed}

        new = []
        for r in relations:
            k = (r.get('ad_key'), r.get('peer_type_id'), r.get('peer_attr_id'))
            if len(list(filter(lambda x: x, k))) == 3 and k not in existed:
                self.cls.create(ad_type_id=ad_type_id, **r)

            new.append(k)

        for deleted in set(existed.keys()) - set(new):
            existed[deleted].soft_delete()

        return self.get_by_type_id(ad_type_id, to_dict=True)

    def _can_add(self, **kwargs):
        pass

    def _can_update(self, **kwargs):
        pass

    def _can_delete(self, **kwargs):
        pass


class AutoDiscoveryCICRUD(DBMixin):
    cls = AutoDiscoveryCI

    @classmethod
    def get_by_adt_id(cls, adt_id):
        return cls.cls.get_by(adt_id=adt_id, to_dict=False)

    @classmethod
    def get_type_name(cls, adc_id):
        adc = cls.cls.get_by_id(adc_id) or abort(404, ErrFormat.adc_not_found)

        ci_type = CITypeCache.get(adc.type_id)

        return ci_type and ci_type.name

    @staticmethod
    def get_ci_types(need_other):
        result = CITypeGroupManager.get(need_other, False)
        adt = {i.type_id for i in AutoDiscoveryCITypeCRUD.get_all()}
        for item in result:
            item['ci_types'] = [i for i in (item.get('ci_types') or []) if i['id'] in adt]

        return result

    @staticmethod
    def get_attributes_by_type_id(type_id):
        from api.lib.cmdb.ci_type import CITypeAttributeManager
        attributes = [i for i in CITypeAttributeManager.get_attributes_by_type_id(type_id) or []]

        attr_names = set()
        adts = AutoDiscoveryCITypeCRUD.get_by_type_id(type_id)
        for adt in adts:
            attr_names |= set((adt.attributes or {}).values())
        return [attr for attr in attributes if attr['name'] in attr_names]

    @classmethod
    def search(cls, page, page_size, fl=None, **kwargs):
        type_id = kwargs['type_id']
        adts = AutoDiscoveryCITypeCRUD.get_by_type_id(type_id)
        if not adts:
            return 0, []
        adt2attr_map = {i.id: i.attributes or {} for i in adts}

        query = db.session.query(cls.cls).filter(cls.cls.deleted.is_(False))

        count_query = db.session.query(func.count(cls.cls.id)).filter(cls.cls.deleted.is_(False))

        for k in kwargs:
            if hasattr(cls.cls, k):
                query = query.filter(getattr(cls.cls, k) == kwargs[k])
                count_query = count_query.filter(getattr(cls.cls, k) == kwargs[k])

        query = query.order_by(cls.cls.is_accept.desc()).order_by(cls.cls.id.desc())

        result = []
        for i in query.offset((page - 1) * page_size).limit(page_size):
            item = i.to_dict()
            adt_id = item['adt_id']
            item['instance'] = {adt2attr_map[adt_id][k]: v for k, v in item.get('instance').items()
                                if (not fl or k in fl) and adt2attr_map.get(adt_id, {}).get(k)}
            result.append(item)

        numfound = query.count()

        return numfound, result

    @staticmethod
    def _get_unique_key(type_id):
        ci_type = CITypeCache.get(type_id)
        if ci_type:
            attr = CITypeAttributeCache.get(type_id, ci_type.unique_id)
            return attr and attr.name

    def _can_add(self, **kwargs):
        pass

    def upsert(self, **kwargs):

        adt = AutoDiscoveryCIType.get_by_id(kwargs['adt_id']) or abort(404, ErrFormat.adt_not_found)

        existed = self.cls.get_by(type_id=kwargs['type_id'],
                                  unique_value=kwargs.get("unique_value"),
                                  first=True, to_dict=False)
        changed = False
        if existed is not None:
            if existed.instance != kwargs['instance']:
                instance = copy.deepcopy(existed.instance) or {}
                instance.update(kwargs['instance'])
                kwargs['instance'] = instance
                existed.update(filter_none=False, **kwargs)
                AutoDiscoveryExecHistoryCRUD().add(type_id=adt.type_id,
                                                   stdout="update resource: {}".format(kwargs.get('unique_value')))
                changed = True
        else:
            existed = self.cls.create(**kwargs)
            AutoDiscoveryExecHistoryCRUD().add(type_id=adt.type_id,
                                               stdout="add resource: {}".format(kwargs.get('unique_value')))
            changed = True

        if adt.auto_accept and changed:
            try:
                self.accept(existed)
            except Exception as e:
                current_app.logger.error(e)
                return abort(400, str(e))
        elif changed:
            existed.update(is_accept=False, accept_time=None, accept_by=None, filter_none=False)

        return existed

    def _can_update(self, **kwargs):
        existed = self.cls.get_by_id(kwargs['_id']) or abort(404, ErrFormat.adc_not_found)

        return existed

    def _can_delete(self, **kwargs):
        if AutoDiscoveryCICRUD.get_by_adt_id(kwargs['_id']):
            return abort(400, ErrFormat.cannot_delete_adt)

        existed = self.cls.get_by_id(kwargs['_id']) or abort(
            404, ErrFormat.ad_not_found.format("id={}".format(kwargs['_id'])))
            
        # 处理 Prefect 类型的删除
        adr = AutoDiscoveryRule.get_by_id(existed.adr_id)
        if adr and adr.type == AutoDiscoveryType.PREFECT:
            self._delete_prefect_deployment(existed)

        return existed
        
    @staticmethod
    def _delete_prefect_deployment(existed):
        """删除 Prefect 部署"""
        try:
            deployment_id = (existed.extra_option or {}).get('prefect_deployment_id')
            if deployment_id:
                success = PrefectManager.delete_deployment(deployment_id)
                if not success:
                    current_app.logger.warning(f"删除Prefect部署失败: {deployment_id}")
                    # 记录警告但不阻止删除
        except Exception as e:
            current_app.logger.error(f"删除Prefect部署时发生错误: {e}", exc_info=True)
            # 记录错误但不阻止删除

    def delete(self, _id):
        inst = self._can_delete(_id=_id)

        inst.delete()

        adt = AutoDiscoveryCIType.get_by_id(inst.adt_id)
        if adt:
            adt.update(updated_at=datetime.datetime.now())

        AutoDiscoveryExecHistoryCRUD().add(type_id=inst.type_id,
                                           stdout="delete resource: {}".format(inst.unique_value))

        self._after_delete(inst)

        return inst

    @classmethod
    def delete2(cls, type_id, unique_value):
        existed = cls.cls.get_by(type_id=type_id, unique_value=unique_value, first=True, to_dict=False) or abort(
            404, ErrFormat.adc_not_found)

        if current_app.config.get("USE_ACL"):
            ci_type = CITypeCache.get(type_id) or abort(404, ErrFormat.ci_type_not_found)

            not is_app_admin("cmdb") and validate_permission(ci_type.name, ResourceTypeEnum.CI, PermEnum.DELETE, "cmdb")

        existed.delete()

        adt = AutoDiscoveryCIType.get_by_id(existed.adt_id)
        if adt:
            adt.update(updated_at=datetime.datetime.now())

        AutoDiscoveryExecHistoryCRUD().add(type_id=type_id,
                                           stdout="delete resource: {}".format(unique_value))
        # TODO: delete ci

    @classmethod
    def accept(cls, adc, adc_id=None, nickname=None):
        if adc_id is not None:
            adc = cls.cls.get_by_id(adc_id) or abort(404, ErrFormat.adc_not_found)

        adt = AutoDiscoveryCITypeCRUD.get_by_id(adc.adt_id) or abort(404, ErrFormat.adt_not_found)

        ci_id = None

        ad_key2attr = adt.attributes or {}
        if ad_key2attr:
            ci_dict = {ad_key2attr[k]: None if not v and isinstance(v, (list, dict)) else v
                       for k, v in adc.instance.items() if k in ad_key2attr}
            extra_option = adt.extra_option or {}
            mapping, path_mapping = AutoDiscoveryHTTPManager.get_predefined_value_mapping(
                extra_option.get('provider'), extra_option.get('category'))
            if mapping:
                ci_dict = {k: (mapping.get(k) or {}).get(str(v), v) for k, v in ci_dict.items()}
            if path_mapping:
                ci_dict = {k: jsonpath.jsonpath(v, path_mapping[k]) if k in path_mapping else v
                           for k, v in ci_dict.items()}
            ci_id, _ = CIManager.add(adc.type_id, exist_policy=ExistPolicy.REPLACE, is_auto_discovery=True, _is_admin=True, **ci_dict)
            AutoDiscoveryExecHistoryCRUD().add(type_id=adt.type_id,
                                             stdout="accept resource: {}".format(adc.unique_value))

        build_relations_for_ad_accept.apply_async(args=(adc.to_dict(), ci_id, ad_key2attr), queue=CMDB_QUEUE)

        adc.update(is_accept=True,
                   accept_by=nickname or current_user.nickname,
                   accept_time=datetime.datetime.now(),
                   ci_id=ci_id)


class AutoDiscoveryHTTPManager(object):
    @staticmethod
    def get_categories(name):
        current_app.logger.info(f"get_categories: {name}")
        name = "aliyun"

        categories = (CLOUD_MAP.get(name) or {}) or []
        for item in copy.deepcopy(categories):
            item.pop('map', None)
            item.pop('collect_key_map', None)

        return categories

    def get_resources(self, name):
        en_name = None
        for i in DEFAULT_INNER:
            if i['name'] == name:
                en_name = i['en']
                break

        if en_name:
            categories = self.get_categories(en_name)

            return [j for i in categories for j in i['items']]

        return []

    @staticmethod
    def get_attributes(provider, resource):
        current_app.logger.info(f"get_attributes: {provider}, {resource}")
        provider = "aliyun"
        for item in (CLOUD_MAP.get(provider) or {}):
            for _resource in (item.get('map') or {}):
                if _resource == resource:
                    tpt = item['map'][_resource]
                    if isinstance(tpt, dict):
                        tpt = tpt.get('template')
                    if tpt and os.path.exists(os.path.join(PWD, tpt)):
                        with open(os.path.join(PWD, tpt)) as f:
                            return json.loads(f.read())

        return []

    @staticmethod
    def get_mapping(provider, resource):
        for item in (CLOUD_MAP.get(provider) or {}):
            for _resource in (item.get('map') or {}):
                if _resource == resource:
                    mapping = item['map'][_resource]
                    if not isinstance(mapping, dict):
                        return {}
                    name = mapping.get('mapping')
                    mapping = AutoDiscoveryMappingCache.get(name)
                    if isinstance(mapping, dict):
                        return {mapping[key][provider]['key'].split('.')[0]: key for key in mapping if
                                (mapping[key].get(provider) or {}).get('key')}

        return {}

    @staticmethod
    def get_predefined_value_mapping(provider, resource):
        for item in (CLOUD_MAP.get(provider) or {}):
            for _resource in (item.get('map') or {}):
                if _resource == resource:
                    mapping = item['map'][_resource]
                    if not isinstance(mapping, dict):
                        return {}, {}
                    name = mapping.get('mapping')
                    mapping = AutoDiscoveryMappingCache.get(name)
                    if isinstance(mapping, dict):
                        return ({key: mapping[key][provider].get('map') for key in mapping if
                                 mapping[key].get(provider, {}).get('map')},
                                {key: mapping[key][provider]['key'].split('.', 1)[1] for key in mapping if
                                 ((mapping[key].get(provider) or {}).get('key') or '').split('.')[1:]})

        return {}, {}


class AutoDiscoverySNMPManager(object):

    @staticmethod
    def get_attributes():
        if os.path.exists(os.path.join(PWD, "templates/net_device.json")):
            with open(os.path.join(PWD, "templates/net_device.json")) as f:
                return json.loads(f.read())

        return []


class AutoDiscoveryComponentsManager(object):

    @staticmethod
    def get_attributes(name):
        if os.path.exists(os.path.join(PWD, "templates/{}.json".format(name))):
            with open(os.path.join(PWD, "templates/{}.json".format(name))) as f:
                return json.loads(f.read())

        return []


class AutoDiscoveryRuleSyncHistoryCRUD(DBMixin):
    cls = AutoDiscoveryRuleSyncHistory

    def _can_add(self, **kwargs):
        pass

    def _can_update(self, **kwargs):
        pass

    def _can_delete(self, **kwargs):
        pass

    def upsert(self, **kwargs):
        existed = self.cls.get_by(adt_id=kwargs.get('adt_id'),
                                  oneagent_id=kwargs.get('oneagent_id'),
                                  oneagent_name=kwargs.get('oneagent_name'),
                                  first=True,
                                  to_dict=False)

        if existed is not None:
            existed.update(**kwargs)
        else:
            self.cls.create(**kwargs)


class AutoDiscoveryExecHistoryCRUD(DBMixin):
    cls = AutoDiscoveryExecHistory

    def _can_add(self, **kwargs):
        pass

    def _can_update(self, **kwargs):
        pass

    def _can_delete(self, **kwargs):
        pass


class AutoDiscoveryCounterCRUD(DBMixin):
    cls = AutoDiscoveryCounter

    def get(self, type_id):
        res = self.cls.get_by(type_id=type_id, first=True, to_dict=True)
        if res is None:
            return dict(rule_count=0, exec_target_count=0, instance_count=0, accept_count=0,
                        this_month_count=0, this_week_count=0, last_month_count=0, last_week_count=0)

        return res

    def _can_add(self, **kwargs):
        pass

    def _can_update(self, **kwargs):
        pass

    def _can_delete(self, **kwargs):
        pass


def encrypt_account(config):
    if isinstance(config, dict):
        if config.get('secret'):
            config['secret'] = AESCrypto.encrypt(config['secret'])
        if config.get('password'):
            config['password'] = AESCrypto.encrypt(config['password'])


def decrypt_account(config, uid):
    if isinstance(config, dict):
        if config.get('password'):
            if not (current_user.username in PRIVILEGED_USERS or current_user.uid == uid):
                config.pop('password', None)
            else:
                try:
                    config['password'] = AESCrypto.decrypt(config['password'])
                except Exception as e:
                    current_app.logger.error('decrypt account failed: {}'.format(e))

        if config.get('secret'):
            if not (current_user.username in PRIVILEGED_USERS or current_user.uid == uid):
                config.pop('secret', None)
            else:
                try:
                    config['secret'] = AESCrypto.decrypt(config['secret'])
                except Exception as e:
                    current_app.logger.error('decrypt account failed: {}'.format(e))


class AutoDiscoveryAccountCRUD(DBMixin):
    cls = AutoDiscoveryAccount

    def get(self, adr_id):
        res = self.cls.get_by(adr_id=adr_id, to_dict=True)

        for i in res:
            decrypt_account(i.get('config'), i['uid'])

        return res

    def get_config_by_id(self, _id):
        res = self.cls.get_by_id(_id)
        if not res:
            return {}

        config = res.to_dict().get('config') or {}

        decrypt_account(config, res.uid)

        return config

    def _can_add(self, **kwargs):
        encrypt_account(kwargs.get('config'))

        kwargs['uid'] = current_user.uid

        return kwargs

    def upsert(self, adr_id, accounts):
        existed_all = self.cls.get_by(adr_id=adr_id, to_dict=False)
        account_names = {i['name'] for i in accounts}

        name_changed = dict()
        for account in accounts:
            existed = None
            if account.get('id'):
                existed = self.cls.get_by_id(account.get('id'))
                if existed is None:
                    continue

                account.pop('id')
                name_changed[existed.name] = account.get('name')
            else:
                account = self._can_add(**account)

            if existed is not None:
                if current_user.uid == existed.uid:
                    config = copy.deepcopy(existed.config) or {}
                    config.update(account.get('config') or {})
                    account['config'] = config
                    existed.update(**account)
            else:
                self.cls.create(adr_id=adr_id, **account)

        for item in existed_all:
            if name_changed.get(item.name, item.name) not in account_names:
                if current_user.uid == item.uid:
                    item.soft_delete()

    def _can_update(self, **kwargs):
        existed = self.cls.get_by_id(kwargs['_id']) or abort(404, ErrFormat.not_found)

        if isinstance(kwargs.get('config'), dict) and kwargs['config'].get('secret'):
            if current_user.uid != existed.uid:
                return abort(403, ErrFormat.adt_secret_no_permission)
        if isinstance(kwargs.get('config'), dict) and kwargs['config'].get('password'):
            if current_user.uid != existed.uid:
                return abort(403, ErrFormat.adt_secret_no_permission)

        return existed

    def update(self, _id, **kwargs):

        if kwargs.get('is_plugin') and kwargs.get('plugin_script'):
            kwargs = check_plugin_script(**kwargs)

        encrypt_account(kwargs.get('config'))

        inst = self._can_update(_id=_id, **kwargs)

        obj = inst.update(_id=_id, filter_none=False, **kwargs)

        return obj

    def _can_delete(self, **kwargs):
        pass
