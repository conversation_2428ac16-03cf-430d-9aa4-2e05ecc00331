[{"name": "status", "type": "string", "desc": "弹性云服务器状态。\n\n取值范围:\n\nACTIVE、BUILD、DELETED、ERROR、HARD_REBOOT、MIGRATING、PAUSED、REBOOT、REBUILD、RESIZE、REVERT_RESIZE、SHUTOFF、SHELVED、SHELVED_OFFLOADED、SOFT_DELETED、SUSPENDED、VERIFY_RESIZE\n\n弹性云服务器状态说明请参考[云服务器状态](https://support.huaweicloud.com/api-ecs/ecs_08_0002.html)", "example": "ACTIVE"}, {"name": "updated", "type": "string", "desc": "弹性云服务器更新时间。\n\n时间格式例如:2019-05-22T03:30:52Z", "example": "2019-05-22T03:30:52Z"}, {"name": "auto_terminate_time", "type": "string", "desc": "弹性云服务器定时删除时间。\n\n时间格式例如:2020-01-19T03:30:52Z", "example": "2020-01-19T03:30:52Z"}, {"name": "hostId", "type": "string", "desc": "弹性云服务器所在主机的主机ID。", "example": "c7145889b2e3202cd295ceddb1742ff8941b827b586861fd0acedf64"}, {"name": "OS-EXT-SRV-ATTR:host", "type": "string", "desc": "弹性云服务器所在主机的主机名称。", "example": "pod01.cn-north-1c"}, {"name": "addresses", "type": "object", "desc": "弹性云服务器的网络属性。", "example": ""}, {"name": "key_name", "type": "string", "desc": "弹性云服务器使用的密钥对名称。", "example": "KeyPair-test"}, {"name": "image", "type": "", "desc": "弹性云服务器镜像信息。", "example": ""}, {"name": "OS-EXT-STS:task_state", "type": "string", "desc": "扩展属性,弹性云服务器当前任务的状态。\n\n取值范围请参考[云服务器状态](https://support.huaweicloud.com/api-ecs/ecs_08_0002.html)表3。", "example": "rebooting"}, {"name": "OS-EXT-STS:vm_state", "type": "string", "desc": "扩展属性,弹性云服务器当前状态。\n\n云服务器状态说明请参考[云服务器状态](https://support.huaweicloud.com/api-ecs/ecs_08_0002.html)。", "example": "active"}, {"name": "OS-EXT-SRV-ATTR:instance_name", "type": "string", "desc": "扩展属性,弹性云服务器别名。", "example": "instance-0048a91b"}, {"name": "OS-EXT-SRV-ATTR:hypervisor_hostname", "type": "string", "desc": "扩展属性,弹性云服务器所在虚拟化主机名。", "example": "nova022@36"}, {"name": "flavor", "type": "", "desc": "弹性云服务器规格信息。", "example": ""}, {"name": "id", "type": "string", "desc": "弹性云服务器ID,格式为UUID。", "example": "4f4b3dfa-eb70-47cf-a60a-998a53bd6666"}, {"name": "security_groups", "type": "array", "desc": "弹性云服务器所属安全组列表。", "example": ""}, {"name": "OS-EXT-AZ:availability_zone", "type": "string", "desc": "扩展属性,弹性云服务器所在可用区名称。", "example": "cn-north-1c"}, {"name": "user_id", "type": "string", "desc": "创建弹性云服务器的用户ID,格式为UUID。", "example": "05498fe56b8010d41f7fc01e280b6666"}, {"name": "name", "type": "string", "desc": "弹性云服务器名称。", "example": "ecs-test-server"}, {"name": "created", "type": "string", "desc": "弹性云服务器创建时间。\n\n时间格式例如:2019-05-22T03:19:19Z", "example": "2017-07-15T11:30:52Z"}, {"name": "tenant_id", "type": "string", "desc": "弹性云服务器所属租户ID,即项目id,和project_id表示相同的概念,格式为UUID。", "example": "743b4c0428d94531b9f2add666646666"}, {"name": "OS-DCF:diskConfig", "type": "string", "desc": "扩展属性, diskConfig的类型。\n\n- MANUAL,镜像空间不会扩展。\n- AUTO,系统盘镜像空间会自动扩展为与flavor大小一致。", "example": "AUTO"}, {"name": "accessIPv4", "type": "string", "desc": "预留属性。", "example": ""}, {"name": "accessIPv6", "type": "string", "desc": "预留属性。", "example": ""}, {"name": "fault", "type": "", "desc": "弹性云服务器故障信息。\n\n可选参数,在弹性云服务器状态为ERROR且存在异常的情况下返回。", "example": ""}, {"name": "progress", "type": "integer", "desc": "弹性云服务器进度。", "example": 0}, {"name": "OS-EXT-STS:power_state", "type": "integer", "desc": "扩展属性,弹性云服务器电源状态。", "example": 4}, {"name": "config_drive", "type": "string", "desc": "config drive信息。", "example": ""}, {"name": "metadata", "type": "object", "desc": "弹性云服务器元数据。\n\n> 说明:\n> \n> 元数据包含系统默认添加字段和用户设置的字段。\n\n系统默认添加字段\n\n1. charging_mode\n云服务器的计费类型。\n\n- “0”:按需计费(即postPaid-后付费方式)。\n- “1”:按包年包月计费(即prePaid-预付费方式)。\"2\":竞价实例计费\n\n2. metering.order_id\n按“包年/包月”计费的云服务器对应的订单ID。\n\n3. metering.product_id\n按“包年/包月”计费的云服务器对应的产品ID。\n\n4. vpc_id\n云服务器所属的虚拟私有云ID。\n\n5. EcmResStatus\n云服务器的冻结状态。\n\n- normal:云服务器正常状态(未被冻结)。\n- freeze:云服务器被冻结。\n\n> 当云服务器被冻结或者解冻后,系统默认添加该字段,且该字段必选。\n\n6. metering.image_id\n云服务器操作系统对应的镜像ID\n\n7.  metering.imagetype\n镜像类型,目前支持:\n\n- 公共镜像(gold)\n- 私有镜像(private)\n- 共享镜像(shared)\n\n8. metering.resourcespeccode\n云服务器对应的资源规格。\n\n9. image_name\n云服务器操作系统对应的镜像名称。\n\n10. os_bit\n操作系统位数,一般取值为“32”或者“64”。\n\n11. lockCheckEndpoint\n回调URL,用于检查弹性云服务器的加锁是否有效。\n\n- 如果有效,则云服务器保持锁定状态。\n- 如果无效,解除锁定状态,删除失效的锁。\n\n12. lockSource\n弹性云服务器来自哪个服务。订单加锁(ORDER)\n\n13. lockSourceId\n弹性云服务器的加锁来自哪个ID。lockSource为“ORDER”时,lockSourceId为订单ID。\n\n14. lockScene\n弹性云服务器的加锁类型。\n\n- 按需转包周期(TO_PERIOD_LOCK)\n\n15. virtual_env_type\n\n- IOS镜像创建虚拟机,\"virtual_env_type\": \"IsoImage\" 属性;\n- 非IOS镜像创建虚拟机,在19.5.0版本以后创建的虚拟机将不会添加virtual_env_type 属性,而在此之前的版本创建的虚拟机可能会返回\"virtual_env_type\": \"FusionCompute\"属性 。\n\n> virtual_env_type属性不允许用户增加、删除和修改。\n\n16. metering.resourcetype\n云服务器对应的资源类型。\n\n17. os_type\n操作系统类型,取值为:Linux、Windows。\n\n18. cascaded.instance_extrainfo\n系统内部虚拟机扩展信息。\n\n19. __support_agent_list\n云服务器是否支持企业主机安全、主机监控。\n\n- “hss”:企业主机安全\n-  “ces”:主机监控\n\n20. agency_name\n委托的名称。\n\n委托是由租户管理员在统一身份认证服务(Identity and Access Management,IAM)上创建的,可以为弹性云服务器提供访问云服务的临时凭证。", "example": ""}, {"name": "OS-SRV-USG:launched_at", "type": "string", "desc": "弹性云服务器启动时间。时间格式例如:2019-05-22T03:23:59.000000", "example": "2018-08-15T14:21:22.000000"}, {"name": "OS-SRV-USG:terminated_at", "type": "string", "desc": "弹性云服务器删除时间。\n\n时间格式例如:2019-05-22T03:23:59.000000", "example": "2019-05-22T03:23:59.000000"}, {"name": "os-extended-volumes:volumes_attached", "type": "array", "desc": "挂载到弹性云服务器上的磁盘。", "example": ""}, {"name": "description", "type": "string", "desc": "弹性云服务器的描述信息。", "example": "ecs description"}, {"name": "host_status", "type": "string", "desc": "nova-compute状态。\n\n- UP:服务正常\n- UNKNOWN:状态未知\n- DOWN:服务异常\n- MAINTENANCE:维护状态\n- 空字符串:弹性云服务器无主机信息", "example": "UP"}, {"name": "OS-EXT-SRV-ATTR:hostname", "type": "string", "desc": "弹性云服务器的主机名。", "example": ""}, {"name": "OS-EXT-SRV-ATTR:reservation_id", "type": "string", "desc": "批量创建场景,弹性云服务器的预留ID。", "example": "r-f06p3js8"}, {"name": "OS-EXT-SRV-ATTR:launch_index", "type": "integer", "desc": "批量创建场景,弹性云服务器的启动顺序。", "example": 0}, {"name": "OS-EXT-SRV-ATTR:kernel_id", "type": "string", "desc": "若使用AMI格式的镜像,则表示kernel image的UUID;否则,留空。", "example": ""}, {"name": "OS-EXT-SRV-ATTR:ramdisk_id", "type": "string", "desc": "若使用AMI格式镜像,则表示ramdisk image的UUID;否则,留空。", "example": ""}, {"name": "OS-EXT-SRV-ATTR:root_device_name", "type": "string", "desc": "弹性云服务器系统盘的设备名称。", "example": "/dev/vda"}, {"name": "OS-EXT-SRV-ATTR:user_data", "type": "string", "desc": "创建弹性云服务器时指定的user_data。", "example": "IyEvYmluL2Jhc2gKZWNobyAncm9vdDokNiRjcGRkSjckWm5WZHNiR253Z0l0SGlxUjZxbWtLTlJaeU9lZUtKd3dPbG9XSFdUeGFzWjA1STYwdnJYRTdTUTZGbEpFbWlXZ21WNGNmZ1pac1laN1BkMTBLRndyeC8nIHwgY2hwYXNzd2Q6666"}, {"name": "locked", "type": "boolean", "desc": "弹性云服务器是否为锁定状态。\n\n- true:锁定\n- false:未锁定", "example": false}, {"name": "tags", "type": "array", "desc": "弹性云服务器标签。", "example": ""}, {"name": "os:scheduler_hints", "type": "", "desc": "弹性云服务器调度信息", "example": ""}, {"name": "enterprise_project_id", "type": "string", "desc": "弹性云服务器所属的企业项目ID。", "example": "0"}, {"name": "sys_tags", "type": "array", "desc": "弹性云服务器系统标签。", "example": ""}, {"name": "cpu_options", "type": "", "desc": "自定义CPU选项。", "example": ""}, {"name": "hypervisor", "type": "", "desc": "hypervisor信息。", "example": ""}]