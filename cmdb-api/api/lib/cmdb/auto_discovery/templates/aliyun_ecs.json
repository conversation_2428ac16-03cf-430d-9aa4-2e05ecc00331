[{"name": "CreationTime", "type": "string", "desc": "实例创建时间。以ISO 8601为标准，并使用UTC+0时间，格式为yyyy-MM-ddTHH:mmZ。更多信息，请参见[ISO 8601](~~25696~~)。", "example": "2017-12-10T04:04Z"}, {"name": "SerialNumber", "type": "string", "desc": "实例序列号。", "example": "51d1353b-22bf-4567-a176-8b3e12e4****"}, {"name": "Status", "type": "string", "desc": "实例状态。", "example": "Running"}, {"name": "DeploymentSetId", "type": "string", "desc": "部署集ID。", "example": "ds-bp67acfmxazb4p****"}, {"name": "KeyPairName", "type": "string", "desc": "密钥对名称。", "example": "testKeyPairName"}, {"name": "SaleCycle", "type": "string", "desc": "> 该参数已弃用，不再返回有意义的数据。", "example": "month"}, {"name": "SpotStrategy", "type": "string", "desc": "按量实例的竞价策略。可能值：\n\n- NoSpot：正常按量付费实例。\n- SpotWithPriceLimit：设置上限价格的抢占式实例。\n- SpotAsPriceGo：系统自动出价，最高按量付费价格的抢占式实例。", "example": "NoSpot"}, {"name": "DeviceAvailable", "type": "boolean", "desc": "实例是否可以挂载数据盘。\n\n- true：可以挂载数据盘。\n- false：不可以挂载数据盘。", "example": "true"}, {"name": "LocalStorageCapacity", "type": "integer", "desc": "实例挂载的本地存储容量。单位：GiB。", "example": "1000"}, {"name": "Description", "type": "string", "desc": "实例描述。", "example": "testDescription"}, {"name": "SpotDuration", "type": "integer", "desc": "抢占式实例的保留时长，单位为小时。可能值：\n\n- 1：创建后阿里云会保证实例运行1小时不会被自动释放；超过1小时后，系统会自动比较出价与市场价格、检查资源库存，来决定实例的持有和回收。\n- 0：创建后，阿里云不保证实例运行1小时，系统会自动比较出价与市场价格、检查资源库存，来决定实例的持有和回收。\n\n实例回收前5分钟阿里云会通过ECS系统事件向您发送通知。抢占式实例按秒计费，建议您结合具体任务执行耗时来选择合适的保留时长。\n\n>当SpotStrategy值为SpotWithPriceLimit或SpotAsPriceGo时返回该参数。", "example": "1"}, {"name": "InstanceNetworkType", "type": "string", "desc": "实例网络类型。可能值：\n\n- classic：经典网络。\n- vpc：专有网络VPC。", "example": "vpc"}, {"name": "InstanceName", "type": "string", "desc": "实例名称。", "example": "InstanceNameTest"}, {"name": "OSNameEn", "type": "string", "desc": "实例操作系统的英文名称。", "example": "CentOS  7.4 64 bit"}, {"name": "HpcClusterId", "type": "string", "desc": "实例所属的HPC集群ID。", "example": "hpc-bp67acfmxazb4p****"}, {"name": "SpotPriceLimit", "type": "number", "desc": "实例的每小时最高价格。支持最大3位小数，参数SpotStrategy=SpotWithPriceLimit时，该参数生效。", "example": "0.98"}, {"name": "Memory", "type": "integer", "desc": "内存大小，单位为MiB。", "example": "16384"}, {"name": "OSName", "type": "string", "desc": "实例的操作系统名称。", "example": "CentOS  7.4 64 位"}, {"name": "DeploymentSetGroupNo", "type": "integer", "desc": "ECS实例绑定部署集分散部署时，实例在部署集中的分组位置。", "example": "1"}, {"name": "ImageId", "type": "string", "desc": "实例运行的镜像ID。", "example": "m-bp67acfmxazb4p****"}, {"name": "VlanId", "type": "string", "desc": "实例的VLAN ID。\n\n>该参数即将被弃用，为提高兼容性，请尽量使用其他参数。", "example": "10"}, {"name": "ClusterId", "type": "string", "desc": "实例所在的集群ID。\n\n>该参数即将被弃用，为提高兼容性，请尽量使用其他参数。", "example": "c-bp67acfmxazb4p****"}, {"name": "GPUSpec", "type": "string", "desc": "实例规格附带的GPU类型。", "example": "NVIDIA V100"}, {"name": "AutoReleaseTime", "type": "string", "desc": "按量付费实例的自动释放时间。", "example": "2017-12-10T04:04Z"}, {"name": "DeletionProtection", "type": "boolean", "desc": "实例释放保护属性，指定是否支持通过控制台或API（DeleteInstance）释放实例。\n\n- true：已开启实例释放保护。\n- false：未开启实例释放保护。\n\n> 该属性仅适用于按量付费实例，且只能限制手动释放操作，对系统释放操作不生效。", "example": "false"}, {"name": "StoppedMode", "type": "string", "desc": "实例停机后是否继续收费。可能值：\n\n- KeepCharging：停机后继续收费，为您继续保留库存资源。\n- StopCharging：停机后不收费。停机后，我们释放实例对应的资源，例如vCPU、内存和公网IP等资源。重启是否成功依赖于当前地域中是否仍有资源库存。\n- Not-applicable：本实例不支持停机不收费功能。", "example": "KeepCharging"}, {"name": "GPUAmount", "type": "integer", "desc": "实例规格附带的GPU数量。", "example": "4"}, {"name": "HostName", "type": "string", "desc": "实例主机名。", "example": "testHostName"}, {"name": "InstanceId", "type": "string", "desc": "实例ID。", "example": "i-bp67acfmxazb4p****"}, {"name": "InternetMaxBandwidthOut", "type": "integer", "desc": "公网出带宽最大值，单位：Mbit/s。", "example": "5"}, {"name": "InternetMaxBandwidthIn", "type": "integer", "desc": "公网入带宽最大值，单位：Mbit/s。", "example": "50"}, {"name": "InstanceType", "type": "string", "desc": "实例规格。", "example": "ecs.g5.large"}, {"name": "InstanceChargeType", "type": "string", "desc": "实例的计费方式。可能值：\n\n- PrePaid：包年包月。\n- PostPaid：按量付费。", "example": "PostPaid"}, {"name": "RegionId", "type": "string", "desc": "实例所属地域ID。", "example": "cn-hangzhou"}, {"name": "IoOptimized", "type": "boolean", "desc": "是否为I/O优化型实例。\n\n- true：是。\n- false：否。", "example": "true"}, {"name": "StartTime", "type": "string", "desc": "实例最近一次的启动时间。以ISO 8601为标准，并使用UTC+0时间，格式为yyyy-MM-ddTHH:mmZ。更多信息，请参见[ISO 8601](~~25696~~)。", "example": "2017-12-10T04:04Z"}, {"name": "Cpu", "type": "integer", "desc": "vCPU数。", "example": "8"}, {"name": "LocalStorageAmount", "type": "integer", "desc": "实例挂载的本地存储数量。", "example": "2"}, {"name": "ExpiredTime", "type": "string", "desc": "过期时间。以ISO 8601为标准，并使用UTC+0时间，格式为yyyy-MM-ddTHH:mmZ。更多信息，请参见[ISO 8601](~~25696~~)。", "example": "2017-12-10T04:04Z"}, {"name": "ResourceGroupId", "type": "string", "desc": "实例所属的企业资源组ID。", "example": "rg-bp67acfmxazb4p****"}, {"name": "InternetChargeType", "type": "string", "desc": "网络计费类型。可能值：\n\n- PayByBandwidth：按固定带宽计费。\n- PayByTraffic：按使用流量计费。", "example": "PayByTraffic"}, {"name": "ZoneId", "type": "string", "desc": "实例所属可用区。", "example": "cn-hangzhou-g"}, {"name": "Recyclable", "type": "boolean", "desc": "实例是否可以回收。", "example": "false"}, {"name": "ISP", "type": "string", "desc": "> 该参数正在邀测中，暂未开放使用。", "example": "null"}, {"name": "CreditSpecification", "type": "string", "desc": "突发性能实例的运行模式。可能值：\n\n- Standard：标准模式。有关实例性能的更多信息，请参见[什么是突发性能实例](~~59977~~)中的性能约束模式章节。\n- Unlimited：无性能约束模式，有关实例性能的更多信息，请参见[什么是突发性能实例](~~59977~~)中的无性能约束模式章节。", "example": "Standard"}, {"name": "InstanceTypeFamily", "type": "string", "desc": "实例规格族。", "example": "ecs.g5"}, {"name": "OSType", "type": "string", "desc": "实例的操作系统类型，分为Windows Server和Linux两种。可能值：\n\n- windows。\n- linux。", "example": "linux"}, {"name": "NetworkInterfaces", "type": "array", "desc": "实例包含的弹性网卡集合。", "example": ""}, {"name": "OperationLocks", "type": "array", "desc": "实例的锁定原因。", "example": ""}, {"name": "Tags", "type": "array", "desc": "实例的标签集合。", "example": ""}, {"name": "RdmaIpAddress", "type": "array", "desc": "HPC实例的RDMA网络IP列表。", "example": ""}, {"name": "SecurityGroupIds", "type": "array", "desc": "实例所属安全组ID列表。", "example": ""}, {"name": "PublicIpAddress", "type": "array", "desc": "实例公网IP地址列表。", "example": ""}, {"name": "InnerIpAddress", "type": "array", "desc": "经典网络类型实例的内网IP地址列表。", "example": ""}, {"name": "VpcAttributes", "type": "object", "desc": "专有网络VPC属性。", "example": ""}, {"name": "Eip<PERSON><PERSON><PERSON>", "type": "object", "desc": "弹性公网IP绑定信息。", "example": ""}, {"name": "HibernationOptions", "type": "object", "desc": "> 该参数正在邀测中，暂未开放使用。", "example": ""}, {"name": "DedicatedHostAttribute", "type": "object", "desc": "由专有宿主机集群ID（DedicatedHostClusterId）、专有宿主机ID（DedicatedHostId）和名称（DedicatedHostName）组成的宿主机属性数组。", "example": ""}, {"name": "EcsCapacityReservationAttr", "type": "object", "desc": "云服务器ECS的容量预留相关参数。", "example": ""}, {"name": "DedicatedInstanceAttribute", "type": "object", "desc": "专有宿主机实例的属性。", "example": ""}, {"name": "CpuOptions", "type": "object", "desc": "CPU配置详情。", "example": ""}, {"name": "MetadataOptions", "type": "object", "desc": "元数据选项集合。", "example": ""}, {"name": "ImageOptions", "type": "object", "desc": "镜像相关属性信息。", "example": ""}, {"name": "SpotInterruptionBehavior", "type": "string", "desc": "平台发起抢占式实例中断时，抢占式实例的中断模式。可能值：\n\n- Terminate：释放。\n\n- Stop：节省停机。", "example": "Terminate"}]