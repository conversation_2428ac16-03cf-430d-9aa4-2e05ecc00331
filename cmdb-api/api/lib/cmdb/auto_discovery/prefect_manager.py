# -*- coding:utf-8 -*-

from typing import Dict, Any, List, Tuple
from contextlib import contextmanager
import json
import time

from flask import current_app
# 直接导入所需模块，不进行检查
from prefect.client.schemas.filters import FlowFilter, FlowRunFilter, DeploymentFilter
from prefect.client.schemas.sorting import FlowRunSort
from prefect.client.schemas.actions import DeploymentUpdate
from prefect.client.schemas.schedules import CronSchedule
from prefect.client.schemas.objects import TERMINAL_STATES
from prefect.client.schemas.actions import DeploymentScheduleCreate




class PrefectException(Exception):
    """Prefect操作异常"""
    pass

@contextmanager
def get_prefect_client():
    """
    获取Prefect客户端的上下文管理器，确保使用同步模式

    Returns:
        client: Prefect同步客户端
    """
    try:
        from prefect import get_client
        with get_client(sync_client=True) as client:
            current_app.logger.info(f"Prefect客户端创建成功: {client}")
            yield client
    except Exception as e:
        current_app.logger.error(f"Prefect客户端创建或使用出错: {str(e)}")
        raise PrefectException(f"Prefect客户端访问失败: {str(e)}")

class PrefectManager:
    """
    Prefect管理器，封装与Prefect API的交互逻辑
    负责创建/管理/查询Deployment和Flow运行
    """

    @staticmethod
    def _get_or_create_flow(flow_name: str) -> str:
        """
        获取或创建Flow (同步方法)

        Args:
            flow_name: Flow的名称

        Returns:
            flow_id: Flow的ID
        """

        try:
            # 使用封装的同步客户端
            with get_prefect_client() as client:
                # 尝试查找现有Flow
                flow_filter = FlowFilter(name={"any_": [flow_name]})
                flows = client.read_flows(flow_filter=flow_filter)

                if flows:
                    return str(flows[0].id)

                # 没找到，创建新Flow
                flow_id = client.create_flow_from_name(flow_name)
                return str(flow_id)
        except Exception as e:
            current_app.logger.error(f"创建或获取Flow时出错: {str(e)}")
            raise PrefectException(f"创建或获取Flow失败: {str(e)}")

    @staticmethod
    def create_deployment(
        flow_name: str,
        deployment_name: str,
        entrypoint: str,
        path_to_flow: str = None,
        work_pool_name: str = "default-agent-pool",
        tags: List[str] = None,
        parameters: Dict[str, Any] = None,
        schedule: str = None
    ) -> str:
        """
        创建一个Prefect Deployment (同步接口)

        Args:
            flow_name: Flow的名称
            deployment_name: 部署的名称
            entrypoint: Flow入口点，格式为"file.py:function_name"
            path_to_flow: Flow代码的路径
            work_pool_name: 工作池名称
            tags: 标签列表
            parameters: 参数字典
            schedule: 调度配置，仅支持cron格式，例如: "0 0 * * *"

        Returns:
            deployment_id: 创建的部署ID
        """
        try:
            # 获取或创建Flow
            flow_id = PrefectManager._get_or_create_flow(flow_name)

            # 设置默认值
            tags = tags or []
            parameters = parameters or {}

            # 处理调度配置
            schedules = None
            if schedule:
                cron_schedule_obj = CronSchedule(
                    cron=schedule,
                )

                schedule_to_create = DeploymentScheduleCreate(
                    schedule=cron_schedule_obj,
                    active=True
                )
                schedules = [schedule_to_create]

            with get_prefect_client() as client:
                deployment_id = client.create_deployment(
                    flow_id=flow_id,
                    name=deployment_name,
                    entrypoint=entrypoint,
                    path=path_to_flow,
                    work_pool_name=work_pool_name,
                    tags=tags,
                    parameters=parameters,
                    schedules=schedules
                )

                current_app.logger.info(f"成功创建Prefect部署: {flow_name}/{deployment_name}, ID: {deployment_id}")
                return str(deployment_id)
        except Exception as e:
            current_app.logger.error(f"创建Prefect部署时出错: {str(e)}")
            raise PrefectException(f"创建Prefect部署失败: {str(e)}")

    @staticmethod
    def update_deployment_schedule_to_be_deleted(
        deployment_id: str,
        schedule: Dict[str, Any]
    ) -> bool:
        """
        更新部署的调度 (同步接口)

        Args:
            deployment_id: 部署ID
            schedule: 调度配置，可以包含cron/interval/rrule

        Returns:
            success: 是否成功
        """
        try:
            # 处理schedule格式
            if not isinstance(schedule, (CronSchedule,)):
                # 如果是字典格式，尝试创建调度对象
                if "cron" in schedule:
                    schedule = CronSchedule(cron=schedule["cron"])

            with get_prefect_client() as client:
                # 先删除旧的调度
                try:
                    existing_schedules = client.read_deployment_schedules(deployment_id)
                    for existing_schedule in existing_schedules:
                        client.delete_deployment_schedule(
                            deployment_id=deployment_id,
                            schedule_id=existing_schedule.id
                        )
                except Exception as e:
                    current_app.logger.warning(f"删除旧调度时出错（可能不存在）: {str(e)}")

                # 创建新的调度
                if schedule:
                    try:
                        client.create_deployment_schedules(
                            deployment_id=deployment_id,
                            schedules=[(schedule, True)]  # (schedule对象, 是否激活)
                        )
                    except Exception as e:
                        current_app.logger.error(f"创建新调度时出错: {str(e)}")
                        raise

                current_app.logger.info(f"成功更新部署调度: {deployment_id}")
                return True
        except Exception as e:
            current_app.logger.error(f"更新部署调度时出错: {str(e)}")
            raise PrefectException(f"更新部署调度失败: {str(e)}")

    @staticmethod
    def delete_deployment(deployment_id: str) -> bool:
        """
        删除部署 (同步接口)

        Args:
            deployment_id: 部署ID

        Returns:
            success: 是否成功
        """
        try:
            with get_prefect_client() as client:
                client.delete_deployment(deployment_id)
                current_app.logger.info(f"成功删除部署: {deployment_id}")
                return True
        except Exception as e:
            current_app.logger.error(f"删除部署时出错: {str(e)}")
            raise PrefectException(f"删除部署失败: {str(e)}")

    @staticmethod
    def get_flow_run_status(deployment_id: str) -> Dict[str, Any]:
        """
        获取指定部署最新的Flow Run状态 (同步接口)

        Args:
            deployment_id: 部署ID

        Returns:
            status: 包含状态信息的字典
        """
        try:
            with get_prefect_client() as client:
                # 获取最新的一次Flow Run
                flow_runs = client.read_flow_runs(
                    flow_run_filter=FlowRunFilter(deployment_id={"any_": [deployment_id]}),
                    sort=FlowRunSort.START_TIME_DESC,
                    limit=1
                )

                if not flow_runs:
                    return {
                        "status": "unknown",
                        "message": "没有找到运行记录",
                        "last_run_time": None,
                        "last_run_id": None
                    }

                flow_run = flow_runs[0]
                run_state = flow_run.state

                return {
                    "status": run_state.type.value if hasattr(run_state.type, 'value') else str(run_state.type),
                    "message": run_state.message,
                    "last_run_time": flow_run.start_time.isoformat() if flow_run.start_time else None,
                    "last_run_id": str(flow_run.id)
                }
        except Exception as e:
            current_app.logger.error(f"获取Flow Run状态时出错: {str(e)}")
            raise PrefectException(f"获取Flow Run状态失败: {str(e)}")

    @staticmethod
    def trigger_flow_run(deployment_id: str, parameters: Dict[str, Any] = None) -> str:
        """
        触发流程运行 (同步接口)

        Args:
            deployment_id: 部署ID
            parameters: 运行参数

        Returns:
            flow_run_id: 触发的Flow Run的ID
        """
        try:
            with get_prefect_client() as client:
                # 触发部署运行，对于元数据获取，设置不持久化结果
                if parameters and parameters.get("get_metadata_only"):
                    flow_run = client.create_flow_run_from_deployment(
                        deployment_id=deployment_id,
                        parameters=parameters or {}
                    )
                else:
                    flow_run = client.create_flow_run_from_deployment(
                        deployment_id=deployment_id,
                        parameters=parameters or {}
                    )

                flow_run_id = str(flow_run.id)
                current_app.logger.info(f"成功触发部署运行: {deployment_id}, Run ID: {flow_run_id}")

                return flow_run_id
        except Exception as e:
            current_app.logger.error(f"触发Flow Run时出错: {str(e)}")
            raise PrefectException(f"触发Flow Run失败: {str(e)}")

    @staticmethod
    def pause_deployment(deployment_id: str) -> bool:
        """
        暂停部署的调度 (同步接口)

        Args:
            deployment_id: 部署ID

        Returns:
            success: 是否成功
        """
        try:
            with get_prefect_client() as client:
                # 创建更新对象
                deployment_update = DeploymentUpdate(paused=True)

                # 更新部署
                client.update_deployment(
                    deployment_id=deployment_id,
                    deployment=deployment_update
                )
                current_app.logger.info(f"成功暂停部署调度: {deployment_id}")
                return True
        except Exception as e:
            current_app.logger.error(f"暂停部署调度时出错: {str(e)}")
            raise PrefectException(f"暂停部署调度失败: {str(e)}")

    @staticmethod
    def resume_deployment(deployment_id: str) -> bool:
        """
        恢复部署的调度 (同步接口)

        Args:
            deployment_id: 部署ID

        Returns:
            success: 是否成功
        """
        try:
            with get_prefect_client() as client:
                # 创建更新对象
                deployment_update = DeploymentUpdate(paused=False)

                # 更新部署
                client.update_deployment(
                    deployment_id=deployment_id,
                    deployment=deployment_update
                )
                current_app.logger.info(f"成功恢复部署调度: {deployment_id}")
                return True
        except Exception as e:
            current_app.logger.error(f"恢复部署调度时出错: {str(e)}")
            raise PrefectException(f"恢复部署调度失败: {str(e)}")

    @staticmethod
    def update_deployment(deployment_id: str, parameters: Dict[str, Any] = None) -> bool:
        """
        更新部署的参数 (同步接口)

        Args:
            deployment_id: 部署ID
            parameters: 更新参数，支持：
                - cron: cron表达式，例如 "0 0 * * *"
                - is_active: 是否激活，True/False
                - parameters: 运行参数

        Returns:
            success: 是否成功
        """
        if not parameters:
            current_app.logger.warning("没有提供需要更新的参数")
            return True

        try:
            with get_prefect_client() as client:
                current_app.logger.info(f"更新部署参数: {parameters}")

                # 初始化更新对象
                deployment_update_kwargs = {}

                # 处理cron调度
                if parameters.get('cron'):
                    cron_schedule_obj = CronSchedule(cron=parameters['cron'])
                    schedule_to_create = DeploymentScheduleCreate(
                        schedule=cron_schedule_obj,
                        active=True
                    )
                    deployment_update_kwargs['schedules'] = [schedule_to_create]

                # 处理激活状态
                if parameters.get('is_active') is not None:
                    deployment_update_kwargs['paused'] = not parameters['is_active']

                if parameters.get('parameters'):
                    deployment_update_kwargs['parameters'] = parameters['parameters']

                # 如果没有需要更新的参数，直接返回
                if not deployment_update_kwargs:
                    current_app.logger.info("没有需要更新的有效参数")
                    return True

                # 创建更新对象并执行更新
                deployment_update = DeploymentUpdate(**deployment_update_kwargs)
                client.update_deployment(
                    deployment_id=deployment_id,
                    deployment=deployment_update
                )

                current_app.logger.info(f"成功更新部署参数: {deployment_id}, 更新内容: {deployment_update_kwargs}")
                return True

        except Exception as e:
            raise PrefectException(f"更新部署参数失败: {str(e)}")

    @staticmethod
    def get_flow_run_result(flow_run_id: str) -> Tuple[str, Any]:
        """
        获取Flow Run的状态和结果 (同步接口)

        Args:
            flow_run_id: Flow Run的ID

        Returns:
            state: 状态字符串
            result: 运行结果
        """
        try:
            with get_prefect_client() as client:
                flow_run = client.read_flow_run(flow_run_id)

                # 获取状态
                state = flow_run.state.type.value if hasattr(flow_run.state.type, 'value') else str(flow_run.state.type)

                # 获取结果
                result = None
                if state == "COMPLETED" and flow_run.state.data is not None:
                    try:
                        # 检查state.data是否是ResultRecordMetadata
                        current_app.logger.info(f"flow_run: {flow_run}")
                        if isinstance(flow_run.state.data, dict) and "storage_key" in flow_run.state.data:
                            artifact_id = flow_run.state.data.get("artifact_id")
                            if artifact_id:
                                artifact = client.read_artifact(artifact_id)
                                result = artifact.data
                    except Exception as e:
                        current_app.logger.warning(f"获取Flow Run结果时出错: {str(e)}")

                return state, result
        except Exception as e:
            current_app.logger.error(f"获取Flow Run结果时出错: {str(e)}")
            raise PrefectException(f"获取Flow Run结果失败: {str(e)}")

    @staticmethod
    def read_deployments(
        flow_filter=None,
        deployment_filter=None,
        limit=None,
        offset=0
    ) -> List[Dict[str, Any]]:
        """
        获取符合条件的deployments列表 (同步接口)

        Args:
            flow_filter: 流程过滤条件，例如 {"name": {"any_": ["flow_name"]}}
            deployment_filter: 部署过滤条件，例如 {"name": {"any_": ["deployment_name"]}}
            limit: 返回结果数量限制
            offset: 结果偏移量

        Returns:
            deployments: 部署列表
        """
        try:
            with get_prefect_client() as client:
                # 转换为Prefect过滤器对象
                _flow_filter = FlowFilter(**flow_filter) if flow_filter else None
                _deployment_filter = DeploymentFilter(**deployment_filter) if deployment_filter else None

                # 获取部署列表
                deployments = client.read_deployments(
                    flow_filter=_flow_filter,
                    deployment_filter=_deployment_filter,
                    limit=limit,
                    offset=offset
                )

                # 转换为字典列表
                result = [
                    {
                        "id": str(deployment.id),
                        "name": deployment.name,
                        "flow_id": str(deployment.flow_id) if hasattr(deployment, 'flow_id') else None,
                        "tags": deployment.tags if hasattr(deployment, 'tags') else [],
                        "parameters": deployment.parameters if hasattr(deployment, 'parameters') else {},
                        "created": deployment.created.isoformat() if hasattr(deployment, 'created') and deployment.created else None,
                        "updated": deployment.updated.isoformat() if hasattr(deployment, 'updated') and deployment.updated else None,
                        "path": deployment.path if hasattr(deployment, 'path') else None,
                        "entrypoint": deployment.entrypoint if hasattr(deployment, 'entrypoint') else None,
                        "work_pool_name": deployment.work_pool_name if hasattr(deployment, 'work_pool_name') else None
                    }
                    for deployment in deployments
                ]

                current_app.logger.info(f"成功获取{len(result)}个部署")
                return result
        except Exception as e:
            current_app.logger.error(f"获取部署列表时出错: {str(e)}")
            raise PrefectException(f"获取部署列表失败: {str(e)}")

    @staticmethod
    def get_flow_metadata(deployment_id: str, max_wait_time: int = 30) -> Tuple[str, Any]:
        """
        获取Flow的元数据信息 (同步接口)
        触发一次特殊的Flow Run获取元数据，并在结束后尝试清理部署。

        Args:
            deployment_id: 部署ID
            max_wait_time: 最大等待时间(秒)

        Returns:
            Tuple[str, Any]: (最终状态字符串, Flow返回的元数据或None)

        Raises:
            PrefectException: 当操作超时或发生Prefect相关错误时抛出
        """
        flow_run_id = None
        final_state = "UNKNOWN"
        metadata_result = None

        try:
            # 1. 触发带有get_metadata_only参数的Flow Run
            flow_run_id = PrefectManager.trigger_flow_run(
                deployment_id=deployment_id,
                parameters={"get_metadata_only": True}
            )
            current_app.logger.info(f"已触发元数据获取Flow Run: {flow_run_id} for deployment {deployment_id}")

            # 2. 等待Flow运行完成
            start_time = time.time()

            with get_prefect_client() as client:
                while time.time() - start_time < max_wait_time:
                    current_run = client.read_flow_run(flow_run_id)
                    state = current_run.state.type.value if hasattr(current_run.state.type, 'value') else str(current_run.state.type)
                    current_app.logger.debug(f"Flow run {flow_run_id} current state: {state}")

                    if state not in TERMINAL_STATES:
                        time.sleep(1)
                        continue

                    final_state = state
                    current_app.logger.info(f"Flow run {flow_run_id} reached terminal state: {final_state}")

                    if final_state == "COMPLETED":
                        metadata_result = PrefectManager._parse_metadata_from_artifacts(client, flow_run_id)
                    break
                else:
                    # 超时时抛出异常而不是返回状态
                    error_msg = f"等待Flow Run {flow_run_id} 结果超时 (>{max_wait_time}秒)"
                    current_app.logger.warning(error_msg)
                    raise PrefectException(error_msg)

        except PrefectException:
            # 重新抛出PrefectException
            raise
        except Exception as e:
            error_msg = f"获取Flow元数据时发生意外错误 (Deployment: {deployment_id}, Run ID: {flow_run_id}): {str(e)}"
            current_app.logger.error(error_msg, exc_info=True)
            raise PrefectException(error_msg)
        finally:
            if flow_run_id:
                PrefectManager._cleanup_deployment(deployment_id)
            else:
                current_app.logger.warning(f"由于未能成功触发Flow Run，跳过部署清理: {deployment_id}")

        return final_state, metadata_result

    @staticmethod
    def _parse_metadata_from_artifacts(client, flow_run_id: str) -> Dict[str, Any]:
        """解析Flow Run的元数据信息"""
        flow_run_filter = FlowRunFilter(id={"any_": [flow_run_id]})
        artifacts = client.read_artifacts(
            key="cmdb-discovery-metadata",
            flow_run_filter=flow_run_filter
        )
        current_app.logger.info(f"获取到artifacts for flow run {flow_run_id}: {artifacts}")

        metadata_artifact = next(
            (art for art in artifacts if art.key == "cmdb-discovery-metadata"),
            None
        )

        if not metadata_artifact or not metadata_artifact.data:
            current_app.logger.warning(f"未找到或artifact数据为空: key='cmdb-discovery-metadata' for flow run {flow_run_id}")
            return None

        try:
            table_data = json.loads(metadata_artifact.data)
            metadata = {
                "unique_key": None,
                "attributes": []
            }

            for field in table_data:
                field_name = field["key"]
                field_info = field["value"]
                if field_info.get("is_primary_key"):
                    metadata["unique_key"] = field_name
                metadata["attributes"].append(
                    (field_name, field_info["type"], field_info["description"])
                )

            current_app.logger.info(f"成功从artifact重建元数据: {metadata}")
            return metadata
        except Exception as e:
            current_app.logger.error(f"解析元数据时出错: {str(e)}")
            return None

    @staticmethod
    def _cleanup_deployment(deployment_id: str) -> None:
        """清理部署"""
        current_app.logger.info(f"开始清理部署: {deployment_id}")
        try:
            PrefectManager.delete_deployment(deployment_id)
            current_app.logger.info(f"成功清理部署: {deployment_id}")
        except Exception as e:
            error_type = "Prefect" if isinstance(e, PrefectException) else "意外"
            current_app.logger.error(f"清理部署 {deployment_id} 时发生{error_type}错误: {str(e)}",
                        exc_info=not isinstance(e, PrefectException))
