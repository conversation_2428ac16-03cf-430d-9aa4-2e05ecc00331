# -*- coding:utf-8 -*-
"""
自定义仪表盘计数器管理模块

该模块提供了自定义仪表盘所需的各种计数和统计功能，包括：
1. 总数统计 (sum_counter)
2. 属性值统计 (attribute_counter)
3. 关系统计 (relation_counter)

此模块从CMDBCounterCache类中提取并重构了与自定义仪表盘相关的功能。
"""

from flask import current_app
from api.extensions import cache


class DashboardCounter(object):
    """
    自定义仪表盘计数器管理类

    提供自定义仪表盘所需的各种计数和统计功能，是对原CMDBCounterCache类中
    与自定义仪表盘相关功能的重构和优化。
    """

    # 缓存键前缀
    CACHE_KEY = 'CMDB::CustomDashboard::Counter'

    @classmethod
    def get_cache(cls):
        """
        获取所有仪表盘计数缓存

        Returns:
            dict: 仪表盘ID到计数结果的映射
        """
        result = cache.get(cls.CACHE_KEY) or {}
        return result

    @classmethod
    def set_cache(cls, result):
        """
        设置仪表盘计数缓存

        Args:
            result (dict): 仪表盘ID到计数结果的映射
        """
        cache.set(cls.CACHE_KEY, result, timeout=0)

    @classmethod
    def update(cls, custom, flush=True):
        """
        更新指定仪表盘的计数结果

        根据仪表盘配置的category类型，调用相应的计数方法，
        并可选地将结果写入缓存。

        Args:
            custom (dict): 仪表盘配置
            flush (bool): 是否将结果写入缓存，默认为True

        Returns:
            dict/int/None: 计数结果，根据category类型不同而不同
        """
        # 获取当前缓存
        result = cls.get_cache()

        # 根据category类型调用相应的计数方法
        if custom['category'] == 0:
            res = cls.sum_counter(custom)
        elif custom['category'] == 1:
            res = cls.attribute_counter(custom)
        else:  # category == 2
            res = cls.relation_counter(
                custom.get('type_id'),
                custom.get('level'),
                custom.get('options', {}).get('filter', ''),
                custom.get('options', {}).get('type_ids', '')
            )

        # 如果有结果且需要写入缓存
        if res is not None and flush and 'id' in custom:
            result[custom['id']] = res
            cls.set_cache(result)

        return res

    @classmethod
    def sum_counter(cls, custom):
        """
        计算符合条件的CI总数

        处理category=0的情况，统计指定CI类型且符合过滤条件的CI总数。

        Args:
            custom (dict): 仪表盘配置，包含type_id和options

        Returns:
            int: 符合条件的CI总数
            None: 发生错误时返回
        """
        from api.lib.cmdb.search import SearchError
        from api.lib.cmdb.search.ci import search

        # 确保options存在
        custom.setdefault('options', {})

        # 获取类型ID
        type_id = custom.get('type_id')
        type_ids = custom['options'].get('type_ids') or (type_id and [type_id])

        # 如果没有有效的类型ID，返回None
        if not type_ids:
            current_app.logger.warning("sum_counter: 未提供有效的type_ids")
            return None

        # 获取过滤条件
        other_filter = custom['options'].get('filter') or ''

        # 构建查询
        query = "_type:({}),{}".format(";".join(map(str, type_ids)), other_filter)
        s = search(query, count=1)

        try:
            # 执行搜索，只关心numfound
            _, _, _, _, numfound, _ = s.search()
            return numfound
        except SearchError as e:
            current_app.logger.error(f"sum_counter搜索错误: {e}")
            return None

    @classmethod
    def attribute_counter(cls, custom):
        """
        计算属性值的分布统计

        处理category=1的情况，统计指定CI类型的指定属性值分布。
        支持多级属性统计（最多三级）和过滤条件。
        支持聚合操作：COUNT, SUM, AVG, MAX, MIN。

        Args:
            custom (dict): 仪表盘配置，包含type_id、attr_id和options等字段

        Returns:
            dict: 属性值到计数的映射，可能是多级嵌套的字典
            list: 当options.ret='cis'时，返回匹配的CI列表
            None: 发生错误时返回
        """
        from api.lib.cmdb.search.ci import search
        from api.lib.cmdb.const import AggregationTypeEnum

        # 确保options存在
        custom.setdefault('options', {})

        # 获取类型和属性ID
        type_id = custom.get('type_id')
        attr_id = custom.get('attr_id')
        type_ids = custom['options'].get('type_ids') or (type_id and [type_id])
        aggregation = custom['options'].get('aggregation') or AggregationTypeEnum.COUNT
        attr_ids = list(map(str, custom['options'].get('attr_ids') or (attr_id and [attr_id])))

        # 如果没有有效的属性ID，返回None
        if not attr_ids:
            current_app.logger.warning("attribute_counter: 未提供有效的attr_ids")
            return None

        # 获取过滤条件和返回类型
        other_filter = custom['options'].get('filter') or ''
        ret = custom['options'].get('ret')

        # 构建基础查询
        base_query = "_type:({}),{}".format(";".join(map(str, type_ids)), other_filter)

        # 如果需要返回CI列表而不是统计结果
        if ret == 'cis':
            return cls._get_ci_list(base_query)

        # 使用Search类的聚合功能进行属性值统计
        try:
            # 创建搜索对象，传入聚合类型
            s = search(base_query, fl=attr_ids, facet=attr_ids, count=1, aggregation=aggregation)

            # 执行搜索，获取facet结果
            _, _, _, _, _, facet = s.search()

            # 记录原始facet结果，便于调试
            current_app.logger.debug(f"attribute_counter: 原始facet结果: {facet}")

            # 处理facet结果，转换为前端所需的格式
            processed_result = cls._process_facet_result(facet, aggregation)

            # 记录处理后的结果，便于调试
            current_app.logger.debug(f"attribute_counter: 处理后的结果: {processed_result}")

            return processed_result
        except Exception as e:
            current_app.logger.error(f"attribute_counter: 执行搜索时出错: {e}")
            return None

    @classmethod
    def _process_facet_result(cls, facet, aggregation, _attr_ids=None):
        """
        处理facet结果，转换为前端所需的格式

        Args:
            facet (dict): 搜索返回的facet结果
            aggregation (str): 聚合类型
            _attr_ids (list, optional): 属性ID列表，目前未使用但保留参数以便未来扩展

        Returns:
            dict/int/list: 处理后的结果，根据数据结构不同返回不同类型
        """
        from flask import current_app

        # 如果facet为空，返回0
        if not facet:
            return 0

        # 如果facet不是字典，直接返回
        if not isinstance(facet, dict):
            return facet

        # 如果facet中不包含aggregation键，直接返回原始facet
        if aggregation not in facet:
            return facet

        # 获取aggregation对应的值
        agg_value = facet[aggregation]

        # 如果值不是列表，直接返回该值
        if not isinstance(agg_value, list):
            return agg_value

        # 如果是空列表，返回空字典
        if not agg_value:
            return {}

        # 检查第一个元素确定数据维度
        first_item = agg_value[0]

        # 如果不是列表，直接返回原始列表
        if not isinstance(first_item, list):
            return agg_value

        # 根据列表元素长度处理不同维度的数据
        item_length = len(first_item)

        # 处理二维数据 [[key, value], ...]
        if item_length == 2:
            current_app.logger.debug(f"处理二维数据结构: {agg_value[:3]}...")

            # 转换为简单字典格式
            result = {}
            for item in agg_value:
                if len(item) == 2:
                    key = item[0]
                    value = item[1]

                    # 将Decimal类型转换为int或float
                    if hasattr(value, 'as_integer_ratio'):  # 检查是否为Decimal类型
                        # 如果是整数值，转换为int，否则转换为float
                        if value == int(value):
                            value = int(value)
                        else:
                            value = float(value)

                    result[key] = value

            return result

        # 处理三维数据 [[dim1, dim2, value], ...]
        elif item_length == 3:
            current_app.logger.debug(f"处理三维数据结构: {agg_value[:3]}...")

            # 转换为嵌套字典格式
            result = {}
            for item in agg_value:
                if len(item) == 3:
                    dim1 = item[0]  # 第一维度
                    dim2 = item[1]  # 第二维度
                    value = item[2]  # 值

                    # 将Decimal类型转换为int或float
                    if hasattr(value, 'as_integer_ratio'):  # 检查是否为Decimal类型
                        # 如果是整数值，转换为int，否则转换为float
                        if value == int(value):
                            value = int(value)
                        else:
                            value = float(value)

                    # 确保第一维度键存在
                    if dim1 not in result:
                        result[dim1] = {}

                    # 存储第二维度的值
                    result[dim1][dim2] = value

            return result

        # 其他情况，返回原始列表
        return agg_value

    @classmethod
    def _get_ci_list(cls, query):
        """
        获取符合查询条件的CI列表

        Args:
            query (str): 查询条件

        Returns:
            list: CI列表
            None: 发生错误时返回
        """
        from api.lib.cmdb.search import SearchError
        from api.lib.cmdb.search.ci import search

        s = search(query, count=1000000)
        try:
            cis, _, _, _, _, _ = s.search()
            return cis
        except SearchError as e:
            current_app.logger.error(f"获取CI列表时出错: {e}")
            return None



    @classmethod
    def _get_first_level_statistics(cls, base_query, attr, _value_type=None, aggregation='count'):
        """
        获取第一级属性的值分布统计

        此方法已被简化版的 _get_attribute_statistics 替代，
        保留此方法签名以兼容可能的调用。

        Args:
            base_query (str): 基础查询条件
            attr: 属性对象
            _value_type (str, optional): 属性值类型，目前未使用但保留参数以便兼容
            aggregation (str): 聚合类型，默认为'count'

        Returns:
            dict: 属性值到计数的映射
        """
        from api.lib.cmdb.search.ci import search

        # 创建搜索对象
        s = search(base_query, fl=attr, facet=[attr.name], count=1, aggregation=aggregation)

        # 执行搜索，获取facet结果
        _, _, _, _, _, facet = s.search()

        # 返回facet结果
        return facet

    @classmethod
    def _get_test_attribute_statistics(cls, base_query, attr_name):
        """
        在测试环境中获取属性统计

        这是一个备用方法，仅在测试环境中使用，用于处理facet结果不可用的情况。
        它直接查询所有CI，然后手动统计属性值。

        Args:
            base_query (str): 基础查询条件
            attr_name (str): 属性名称

        Returns:
            dict: 属性值到计数的映射
        """
        from api.lib.cmdb.search.ci import search
        from flask import current_app

        result = {}

        try:
            # 查询所有CI
            s = search(base_query, count=1000000)
            cis, _, _, _, _, _ = s.search()

            # 手动统计属性值
            for ci in cis:
                value = ci.get(attr_name)
                if value is not None:
                    if value in result:
                        result[value] += 1
                    else:
                        result[value] = 1

            return result

        except Exception as e:
            current_app.logger.error(f"测试环境中获取属性统计时出错: {e}")
            return {}

    @classmethod
    def _get_second_level_statistics(cls, base_query, first_attr, second_attr_id,
                                    attr_value_types, first_level_result):
        """
        获取第二级属性的值分布统计

        Args:
            base_query (str): 基础查询条件
            first_attr: 第一级属性对象
            second_attr_id (str): 第二级属性ID
            attr_value_types (list): 属性值类型列表
            first_level_result (dict): 第一级统计结果

        Returns:
            dict: 更新后的统计结果
        """
        from api.lib.cmdb.search import SearchError
        from api.lib.cmdb.search.ci import search
        from api.lib.cmdb.cache import AttributeCache
        from api.lib.cmdb.utils import ValueTypeMap
        from flask import current_app

        # 获取第二级属性
        second_attr = AttributeCache.get(second_attr_id)
        if second_attr is None:
            current_app.logger.error(f"属性ID {second_attr_id} 不存在")
            return first_level_result

        # 对第一级的每个值进行查询
        for first_value in list(first_level_result.keys()):
            # 构建包含第一级属性值的查询
            query = f"{base_query},{first_attr.name}:{first_value}"

            # 创建搜索对象
            s = search(query, fl=[first_attr.name, second_attr.name],
                      facet=[second_attr.name], count=1)

            try:
                # 执行搜索，获取facet结果
                _, _, _, _, _, facet = s.search()

                # 初始化第二级结果
                first_level_result[first_value] = {}

                # 从facet结果中提取值和计数
                facet_items = cls._extract_facet_items(facet)

                # 处理每个值和计数
                for value, count, _ in facet_items:
                    # 根据属性值类型转换值
                    typed_value = ValueTypeMap.serialize2[attr_value_types[1]](str(value))
                    # 存储结果
                    first_level_result[first_value][typed_value] = count

            except SearchError as e:
                current_app.logger.error(f"获取第二级属性统计时出错: {e}")
                continue
            except Exception as e:
                current_app.logger.error(f"处理第二级属性统计结果时出错: {e}")
                continue

        return first_level_result

    @classmethod
    def _get_third_level_statistics(cls, base_query, first_attr, second_attr_id,
                                   third_attr_id, attr_value_types, second_level_result):
        """
        获取第三级属性的值分布统计

        Args:
            base_query (str): 基础查询条件
            first_attr: 第一级属性对象
            second_attr_id (str): 第二级属性ID
            third_attr_id (str): 第三级属性ID
            attr_value_types (list): 属性值类型列表
            second_level_result (dict): 第二级统计结果

        Returns:
            dict: 更新后的统计结果
        """
        from api.lib.cmdb.search import SearchError
        from api.lib.cmdb.search.ci import search
        from api.lib.cmdb.cache import AttributeCache
        from api.lib.cmdb.utils import ValueTypeMap
        from flask import current_app

        # 获取第二级和第三级属性
        second_attr = AttributeCache.get(second_attr_id)
        third_attr = AttributeCache.get(third_attr_id)

        if second_attr is None or third_attr is None:
            current_app.logger.error(f"属性ID不存在: second_attr_id={second_attr_id}, third_attr_id={third_attr_id}")
            return second_level_result

        # 对第一级和第二级的每个值组合进行查询
        for first_value in list(second_level_result.keys()):
            # 跳过非字典值（可能是第一级的计数）
            if not isinstance(second_level_result[first_value], dict):
                continue

            for second_value in list(second_level_result[first_value].keys()):
                # 构建包含第一级和第二级属性值的查询
                query = f"{base_query},{first_attr.name}:{first_value},{second_attr.name}:{second_value}"

                # 创建搜索对象
                s = search(query, fl=[first_attr.id, second_attr.id, third_attr.id],
                          facet=[third_attr.name], count=1)

                try:
                    # 执行搜索，获取facet结果
                    _, _, _, _, _, facet = s.search()

                    # 初始化第三级结果
                    second_level_result[first_value][second_value] = {}

                    # 从facet结果中提取值和计数
                    facet_items = cls._extract_facet_items(facet)

                    # 处理每个值和计数
                    for value, count in facet_items:
                        # 根据属性值类型转换值
                        typed_value = ValueTypeMap.serialize2[attr_value_types[2]](str(value))
                        # 存储结果
                        second_level_result[first_value][second_value][typed_value] = count

                except SearchError as e:
                    current_app.logger.error(f"获取第三级属性统计时出错: {e}")
                    continue
                except Exception as e:
                    current_app.logger.error(f"处理第三级属性统计结果时出错: {e}")
                    continue

        return second_level_result

    @staticmethod
    def _extract_facet_items(facet):
        """
        从facet结果中提取值和计数

        Args:
            facet (dict/int): facet结果，可能是字典或整数

        Returns:
            list: 值和计数的列表，每个元素是(值, 计数)的元组
        """
        from flask import current_app

        # 如果facet不是字典，记录错误并返回空列表
        if not isinstance(facet, dict):
            current_app.logger.error(f"facet不是字典: {facet}")
            return []

        # 如果facet为空，返回空列表
        if not facet:
            return []

        try:
            # 获取facet值列表
            facet_values = list(facet.values())

            # 如果facet值列表为空，返回空列表
            if not facet_values:
                return []

            # 返回第一个facet值列表
            return facet_values[0]
        except Exception as e:
            current_app.logger.error(f"提取facet项时出错: {e}")
            return []



    @classmethod
    def relation_counter(cls, type_id, level, other_filter, type_ids):
        """
        计算关系统计

        处理category=2的情况，统计指定CI与其关联CI的关系。

        Args:
            type_id (int): 中心CI类型ID
            level (int): 关系层级
            other_filter (str): 过滤条件
            type_ids (list): 目标CI类型ID列表

        Returns:
            dict: 包含summary和detail的统计结果
            None: 发生错误时返回
        """
        from api.lib.cmdb.search.ci_relation.search import Search as RelSearch
        from api.lib.cmdb.search import SearchError
        from api.lib.cmdb.search.ci import search

        # 如果没有有效的类型ID，返回None
        if not type_id:
            current_app.logger.warning("relation_counter: 未提供有效的type_id")
            return None

        # 构建查询
        query = "_type:{}".format(type_id)
        s = search(query, count=1000000)

        try:
            # 执行搜索，获取中心CI
            type_names, _, _, _, _, _ = s.search()
        except SearchError as e:
            current_app.logger.error(f"relation_counter搜索中心CI错误: {e}")
            return None

        # 获取中心CI的ID和唯一键值
        type_id_names = [(str(i.get('_id')), i.get(i.get('unique'))) for i in type_names]

        # 创建关系搜索对象
        s = RelSearch([i[0] for i in type_id_names], level, other_filter or '')

        try:
            # 执行关系统计
            stats = s.statistics(type_ids, need_filter=False)

            # 如果stats不包含summary键，可能是因为RelSearch.statistics的返回格式与预期不符
            # 我们需要确保返回的结果包含summary和detail键
            if not isinstance(stats, dict):
                current_app.logger.warning(f"relation_counter: 统计结果不是字典: {stats}")
                return {"summary": {}, "detail": {}}

            if "summary" not in stats:
                # 尝试构建一个符合预期格式的结果
                # 假设stats是一个字典，其中键是CI ID，值是统计结果
                summary = {}
                detail = {}

                # 从type_id_names中获取唯一键值
                for ci_id, unique_value in type_id_names:
                    # 在原始的CMDBCounterCache.relation_counter方法中，summary是一个字典
                    # 其中键是CI的唯一键值，值是统计结果
                    # 但测试用例期望unique_value在summary中，而不是作为键
                    summary[unique_value] = 0

                    # 如果stats中有detail键，使用它
                    if "detail" in stats and ci_id in stats["detail"]:
                        detail[unique_value] = stats["detail"][ci_id]
                    else:
                        detail[unique_value] = {}

                return {"summary": summary, "detail": detail}

            return stats
        except SearchError as e:
            current_app.logger.error(f"relation_counter关系统计错误: {e}")
            return None
        except Exception as e:
            current_app.logger.error(f"relation_counter处理结果错误: {e}")
            # 返回一个基本的结果结构
            return {"summary": {}, "detail": {}}
