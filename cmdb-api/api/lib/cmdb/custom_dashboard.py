# -*- coding:utf-8 -*-

from flask import abort

from api.lib.cmdb.resp_format import ErrFormat
from api.models.cmdb import CustomDashboard
from api.models.cmdb import SystemConfig
from api.lib.cmdb.dashboard_counter import DashboardCounter


class CustomDashboardManager(object):
    cls = CustomDashboard

    @staticmethod
    def get():
        return sorted(CustomDashboard.get_by(to_dict=True), key=lambda x: (x["category"], x['order']))

    @staticmethod
    def preview(**kwargs):
        """
        预览仪表盘数据

        根据传入的配置，计算仪表盘数据但不保存到缓存

        Args:
            **kwargs: 仪表盘配置参数

        Returns:
            dict/int/None: 计算结果
        """
        res = DashboardCounter.update(kwargs, flush=False)
        return res

    @staticmethod
    def add(**kwargs):
        """
        添加新的仪表盘配置

        创建新的仪表盘配置记录，并计算其数据

        Args:
            **kwargs: 仪表盘配置参数

        Returns:
            tuple: (新创建的仪表盘对象, 计算结果)
        """
        # 检查名称是否重复
        if kwargs.get('name'):
            CustomDashboard.get_by(name=kwargs['name']) and abort(400, ErrFormat.custom_name_duplicate)

        # 创建新的仪表盘配置
        new = CustomDashboard.create(**kwargs)

        # 计算仪表盘数据
        res = DashboardCounter.update(new.to_dict())

        return new, res

    @staticmethod
    def update(_id, **kwargs):
        """
        更新仪表盘配置

        更新指定ID的仪表盘配置，并重新计算其数据

        Args:
            _id: 仪表盘ID
            **kwargs: 要更新的配置参数

        Returns:
            tuple: (更新后的仪表盘对象, 计算结果)
        """
        # 获取现有仪表盘配置
        existed = CustomDashboard.get_by_id(_id) or abort(404, ErrFormat.not_found)

        # 更新仪表盘配置
        new = existed.update(**kwargs)

        # 重新计算仪表盘数据
        res = DashboardCounter.update(new.to_dict())

        return new, res

    @staticmethod
    def batch_update(id2options):
        """
        批量更新仪表盘配置的options字段

        Args:
            id2options (dict): 仪表盘ID到options的映射
        """
        for _id in id2options:
            existed = CustomDashboard.get_by_id(_id) or abort(404, ErrFormat.not_found)
            existed.update(options=id2options[_id])

            # 重新计算仪表盘数据
            dashboard = existed.to_dict()
            DashboardCounter.update(dashboard)

    @staticmethod
    def delete(_id):
        """
        删除仪表盘配置

        Args:
            _id: 仪表盘ID
        """
        existed = CustomDashboard.get_by_id(_id) or abort(404, ErrFormat.not_found)
        existed.soft_delete()

        # 从缓存中删除该仪表盘的数据
        result = DashboardCounter.get_cache()
        if _id in result:
            del result[_id]
            DashboardCounter.set_cache(result)


class SystemConfigManager(object):
    cls = SystemConfig

    @staticmethod
    def get(name):
        return SystemConfig.get_by(name=name, first=True, to_dict=True)

    @staticmethod
    def create_or_update(name, option):
        existed = SystemConfig.get_by(name=name, first=True, to_dict=False)
        if existed is not None:
            return existed.update(option=option)
        else:
            return SystemConfig.create(name=name, option=option)

    @staticmethod
    def delete(name):
        existed = SystemConfig.get_by(name=name, first=True, to_dict=False) or abort(404, ErrFormat.not_found)

        existed.soft_delete()
