# -*- coding:utf-8 -*-

from api.lib.cmdb.ci import CIManager
from api.lib.cmdb.const import ExistPolicy
from flask import abort
from api.lib.cmdb.resp_format import ErrFormat
from api.lib.cmdb.cloud_history import CloudAttributeHistoryManger
from api.lib.cmdb.const import BusinessType

PASSWORD_DEFAULT_SHOW = "******"



class CloudCIManager(CIManager):
    
    @classmethod
    def add(cls, ci_type_name, bussiness_type,
            exist_policy=ExistPolicy.REJECT,
            _no_attribute_policy=ExistPolicy.IGNORE,
            is_auto_discovery=False,
            _is_admin=False,
            ticket_id=None,
            **ci_dict):
        """
        add ci
        :param ci_type_name:
        :param bussiness_type:
        :param exist_policy: replace or reject or need
        :param _no_attribute_policy: ignore or reject
        :param is_auto_discovery: default is False
        :param _is_admin: default is False
        :param ticket_id:
        :param ci_dict:
        :return:
        """
        if bussiness_type == "billing":
            bussiness_flag = BusinessType.BILLING
            if not all(ci_dict.get(key) for key in ['计费调整日期', '计费调整单号']):
                return abort(400, ErrFormat.billing_must_have.format())
        elif bussiness_type == "nonbilling":
            bussiness_flag = BusinessType.NONBILLING
            if not all(ci_dict.get(key) for key in ['非计费调整日期', '非计费调整工单号']):
                return abort(400, ErrFormat.nonbilling_must_have.format())
        elif bussiness_type == "normal":
            bussiness_flag = BusinessType.NORMAL
        else:
            return abort(400, ErrFormat.not_have_billing_type.format())

        id, record_id = super().add(ci_type_name, exist_policy, _no_attribute_policy, is_auto_discovery, _is_admin, ticket_id, **ci_dict)
        
        CloudAttributeHistoryManger.update_business_type(record_id, bussiness_flag) # 没有返回判断
        
        return id, record_id
        