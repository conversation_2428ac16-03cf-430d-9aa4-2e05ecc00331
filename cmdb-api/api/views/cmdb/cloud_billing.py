# -*- coding:utf-8 -*- 


import datetime
import time
from flask import current_app

from flask import abort
from flask import request

from api.lib.cmdb.history import AttributeHistoryManger
from api.lib.cmdb.cloud_history import CloudAttributeHistoryManger
from api.lib.cmdb.resp_format import Err<PERSON>ormat
from api.lib.common_setting.decorator import perms_role_required
from api.lib.common_setting.role_perm_base import CMDBApp
from api.lib.utils import get_page
from api.lib.utils import get_page_size
from api.resource import APIView
from api.lib.utils import handle_arg_list
from api.lib.cmdb.const import Ret<PERSON><PERSON>, BillingFieldEnum, BillingStatusEnum
from api.lib.cmdb.search import SearchError
from api.lib.cmdb.search.ci import search
from flask_login import current_user
from api.lib.perm.acl.acl import ACLManager
from api.lib.cmdb.search.ci_relation.search import Search
import sys
from collections import defaultdict
import json
from api.lib.cmdb.cache import BillingCache
from api.lib.cmdb.cloud_billing import CloudBillingManager


app_cli = CMDBApp()


class CloudBillingView(APIView):
    url_prefix = ("/cloud_history/billing")

    @perms_role_required(app_cli.app_name, app_cli.resource_type_name, app_cli.op.Operation_Audit,
                         app_cli.op.read, app_cli.admin_name)
    def post(self):
        """处理业务日志导出请求"""
        start_time = time.time()
        current_app.logger.debug("开始处理业务日志导出请求")

        # 获取请求参数
        source = request.values.get("source")
        target = request.values.get("target")
        path = request.values.get("path")
        current_app.logger.debug(f"请求参数: source={source}, target={target}, path={path}")

        page = get_page(request.values.get("page", 1))
        page_size = get_page_size(request.values.get("count") or request.values.get("page_size"))
        need_transform = request.values.get("transform", "").lower() == "true"

        # 1. 检查是否有完整的缓存数据
        cached_data = BillingCache.get_billing_data(source, target, path)
        if cached_data:
            result = CloudBillingManager.handle_pagination(
                cached_data['all_processed_paths'],
                cached_data['id2ci_copy'],
                cached_data['paths'],
                page,
                page_size
            )
            
            result.update({
                'relation_types': cached_data['relation_types'],
                'type2show_key': cached_data['type2show_key'],
                'type2multishow_key': cached_data['type2multishow_key']
            })

            if need_transform:
                transformed_paths = {}
                for path_type, path_list in result['paths'].items():
                    transformed_paths[path_type] = [
                        CloudBillingManager.transform_path_to_data(path, result['id2ci']) 
                        for path in path_list
                    ]
                result['transformed_paths'] = transformed_paths
                del result['paths']
                del result['id2ci']

            return self.jsonify(**result)

        # 2. 检查生成状态
        current_app.logger.debug("开始检查数据生成状态")
        generation_status = BillingCache.get_generation_status(source, target, path)
        if generation_status:
            current_app.logger.info(f"获取到生成状态: {generation_status}")
            if generation_status['status'] == BillingCache.STATUS_PROCESSING:
                current_app.logger.info("数据正在生成中，返回处理中状态")
                return self.jsonify({
                    'status': BillingCache.STATUS_PROCESSING,
                    'message': '数据正在生成中，请稍后重试',
                    'timestamp': generation_status['timestamp']
                })
            elif generation_status['status'] == BillingCache.STATUS_FAILED:
                current_app.logger.info("之前生成失败，重新开始生成")
                BillingCache.set_generation_status(source, target, path, 
                                                BillingCache.STATUS_PROCESSING,
                                                "开始重新生成数据")
        else:
            current_app.logger.info("未找到生成状态，开始首次生成")
            BillingCache.set_generation_status(source, target, path, 
                                            BillingCache.STATUS_PROCESSING,
                                            "开始生成数据")
            # 4. 启动异步任务
            current_app.logger.debug("启动异步生成任务")
            from api.tasks.cmdb import generate_billing_data
            generate_billing_data.delay(source, target, path, current_user.uid)
        
        end_time = time.time()
        current_app.logger.info(f"处理请求完成，耗时: {end_time - start_time:.2f}秒")
        return self.jsonify({
            'status': BillingCache.STATUS_PROCESSING,
            'message': '数据开始生成，请稍后重试'
        })

    @perms_role_required(app_cli.app_name, app_cli.resource_type_name, app_cli.op.Operation_Audit,
                         app_cli.op.read, app_cli.admin_name)
    def delete(self):
        """清除缓存"""
        try:
            # 获取请求参数
            source = request.values.get("source")
            target = request.values.get("target")
            path = request.values.get("path")
            
            # 验证参数
            if not all([source, target, path]):
                return abort(400, "Missing required parameters")
            
            current_app.logger.debug(f"清除特定缓存: source={source}, target={target}, path={path}")
            
            # 使用BillingCache的方法清除缓存
            if BillingCache.clean_billing_data(source, target, path):
                return self.jsonify(message="Cache cleared successfully")
            else:
                return abort(500, "Failed to clear cache")
        except Exception as e:
            current_app.logger.error(f"清除缓存失败: {str(e)}")
            return abort(500, str(e))
        
        