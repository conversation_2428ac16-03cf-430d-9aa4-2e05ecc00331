# -*- coding:utf-8 -*- 


import datetime
import time
from flask import current_app

from flask import abort
from flask import request

from api.lib.cmdb.history import AttributeHistoryManger
from api.lib.cmdb.cloud_history import CloudAttributeHistoryManger
from api.lib.cmdb.resp_format import ErrFormat
from api.lib.common_setting.decorator import perms_role_required
from api.lib.common_setting.role_perm_base import CMDBApp
from api.lib.utils import get_page
from api.lib.utils import get_page_size
from api.resource import APIView
from api.lib.utils import handle_arg_list
from api.lib.cmdb.const import RetKey
from api.lib.cmdb.search import SearchError
from api.lib.cmdb.search.ci import search
from flask_login import current_user
from api.lib.perm.acl.acl import ACLManager
from api.lib.cmdb.search.ci_relation.search import Search
import sys


app_cli = CMDBApp()


class CloudRecordView(APIView):
    url_prefix = ("/cloud_history/records/attribute")

    def get(self):
        page = get_page(request.values.get("page", 1))
        page_size = get_page_size(request.values.get("page_size"))
        _start = request.values.get("start")
        _end = request.values.get("end")
        username = request.values.get("username", "")
        operate_type = request.values.get("operate_type", "")
        record_id = request.values.get("record_id", "")
        business_type = request.values.get("business_type", "")
        type_id = request.values.get("type_id")
         
        response_ids, start, end = None, None, None
        user_info = ACLManager.get_user_info(current_user.username)
        if not any(role in user_info['parents'] for role in ['admin', 'cmdb_admin', 'CMDB_READ_ALL']):
            username = user_info['username']
        if _start:
            try:
                start = datetime.datetime.strptime(_start, "%Y-%m-%d %H:%M:%S")
            except ValueError:
                return abort(400, ErrFormat.datetime_argument_invalid.format("start"))
        if _end:
            try:
                end = datetime.datetime.strptime(_end, "%Y-%m-%d %H:%M:%S")
            except ValueError:
                return abort(400, ErrFormat.datetime_argument_invalid.format("start"))
        
        query = request.values.get('index_word', "")
        if query:
            query = f"*{query}*"
            count = 100000
            s = search(query=query, page=page, count=count)
            try:
                response, _, _, _, _, _ = s.search()
                response_ids = [item['_id'] for item in response]  # 提取所有的_id到一个列表
                current_app.logger.debug(f"response_ids: {response_ids}")
                if not response_ids:
                    return self.jsonify(records=[], total=0, **request.values)
            except SearchError as e:
                return abort(400, str(e))
        
        if "attribute" in request.url:
            total, res, numfound = AttributeHistoryManger.get_records_for_attributes(
                start,
                end,
                username,
                page,
                page_size,
                operate_type,
                business_type,
                type_id,
                record_id,
                request.values.get("ci_id"),
                request.values.get("attr_id"),
                ci_ids=response_ids,
            )
            return self.jsonify(records=res, total=total, numfound=numfound, **request.values)
        else:
            total, res, cis = AttributeHistoryManger.get_records_for_relation(
                start,
                end,
                username,
                page,
                page_size,
                operate_type,
                type_id,
                request.values.get("first_ci_id"),
                request.values.get("second_ci_id"),
            )
            return self.jsonify(records=res, total=total, cis=cis, **request.values)


class CloudCIHistoryExportView(APIView):
    url_prefix = ("/cloud_history/bussiness_log", "/cloud_history/bussiness_log/<int:record_id>")

    @perms_role_required(app_cli.app_name, app_cli.resource_type_name, app_cli.op.Operation_Audit,
                         app_cli.op.read, app_cli.admin_name)
    def get(self):
        """@params: q: query statement
                    fl: filter by column
                    count/page_size: the number of ci
                    ret_key: id, name, alias
                    facet: statistic
        """
        current_app.logger.debug("开始处理业务日志导出请求")
        
        page = get_page(request.values.get("page", 1))
        count = get_page_size(request.values.get("count") or request.values.get("page_size"))
        current_app.logger.debug(f"分页参数: page={page}, count={count}")

        query = request.values.get('q', "")
        begin_date = request.values.get('begin_date', "2000-01-01")
        end_date = request.values.get('end_date', "2099-12-31")
        current_app.logger.debug(f"查询参数: query={query}, begin_date={begin_date}, end_date={end_date}")
        
        type_id = query.split(":")[1].strip('()') if query else None
        fl = handle_arg_list(request.values.get('fl', ""))
        excludes = handle_arg_list(request.values.get('excludes', ""))
        ret_key = request.values.get('ret_key', RetKey.NAME)
        if ret_key not in (RetKey.NAME, RetKey.ALIAS, RetKey.ID):
            ret_key = RetKey.NAME
        facet = handle_arg_list(request.values.get("facet", ""))
        sort = request.values.get("sort")
        use_id_filter = request.values.get("use_id_filter", False) in current_app.config.get('BOOL_TRUE')
        current_app.logger.debug(f"过滤参数: type_id={type_id}, fl={fl}, excludes={excludes}, ret_key={ret_key}")

        start = time.time()
        s = search(query, fl, facet, page, ret_key, count, sort, excludes, use_id_filter=use_id_filter)
        try:
            response, counter, total, page, numfound, facet = s.search()
            current_app.logger.debug(f"搜索结果: total={total}, numfound={numfound}")
        except SearchError as e:
            current_app.logger.error(f"搜索出错: {str(e)}")
            return abort(400, str(e))

        total, res = CloudAttributeHistoryManger.get_records_for_attributes(None, None, None, 1, 10,
                                                                       None,
                                                                       None,
                                                                       type_id,
                                                                       more=True)
        current_app.logger.debug(f"获取属性历史记录: total={total}")

        current_app.logger.debug("search time is: {0}".format(time.time() - start))

        business_history = []
        from datetime import datetime
        begin_date = datetime.strptime(begin_date, '%Y-%m-%d')
        end_date = datetime.strptime(end_date, '%Y-%m-%d')

        business_history = [_res[1] for _res in res if any(_dist['business_type'] == '1' and _dist['business_is_active'] == True for _dist in _res[1])]
        current_app.logger.debug(f"业务历史记录数量: {len(business_history)}")

        from collections import defaultdict
        ci_history_grouped = defaultdict(list)
        for history_entry in business_history:
            ci_history_grouped[history_entry[0]['ci_id']].append(history_entry)
        current_app.logger.debug(f"分组后的CI历史记录数量: {len(ci_history_grouped)}")

        def process_single_record(response_item):
            """处理单条记录的字段转换"""
            process_item = response_item.copy()
            process_item['ShangCiJiFeiTiaoZhengDanHao'] = process_item['JiFeiTiaoZhengDanHao']
            process_item['ShangCiJiFeiTiaoZhengRiQi'] = process_item['JiFeiTiaoZhengRiQI']
            process_item['JiFeiTiaoZhengRiQI'] = None
            process_item['JiFeiTiaoZhengDanHao'] = None
            return process_item

        def process_history_record(process_item, history_entry):
            """处理历史记录条目"""
            temp_JiFeiTiaoZheng = {
                'DanHao': process_item['JiFeiTiaoZhengDanHao'],
                'RiQi': process_item['JiFeiTiaoZhengRiQI']
            }

            for change_record in history_entry:
                process_item[change_record['attr_name']] = change_record['old']

            process_item.update({
                'ShangCiJiFeiTiaoZhengDanHao': process_item['JiFeiTiaoZhengDanHao'],
                'ShangCiJiFeiTiaoZhengRiQi': process_item['JiFeiTiaoZhengRiQI'],
                'JiFeiTiaoZhengDanHao': temp_JiFeiTiaoZheng['DanHao'],
                'JiFeiTiaoZhengRiQI': temp_JiFeiTiaoZheng['RiQi'],
                'ZhuangTai': '调整过程'
            })
            return process_item
        current_app.logger.debug(f"CI历史记录的keys: {list(ci_history_grouped.keys())}")
        current_app.logger.debug(f"response中的_id: {[item.get('_id') for item in response]}")

        # 主处理逻辑
        updated_responses = []
        for response_item in response:
            # 处理初始记录
            updated_responses.append(process_single_record(response_item))

            # 处理历史记录
            if response_item['_id'] in ci_history_grouped:
                current_app.logger.debug(f"处理CI {response_item['_id']}的历史记录")
                temp_process_item = response_item.copy()
                for history_entry in ci_history_grouped[response_item['_id']]:
                    processed_item = process_history_record(temp_process_item.copy(), history_entry)
                    updated_responses.append(processed_item)
                    temp_process_item = processed_item.copy()
                    temp_process_item.update({
                        'JiFeiTiaoZhengDanHao': temp_process_item['ShangCiJiFeiTiaoZhengDanHao'],
                        'JiFeiTiaoZhengRiQI': temp_process_item['ShangCiJiFeiTiaoZhengRiQi']
                    })
            else:
                current_app.logger.debug(f"未找到CI {response_item['_id']}的历史记录")

        current_app.logger.debug(f"最终处理结果数量: {len(updated_responses)}")
        return self.jsonify(numfound=numfound,
                            total=total,
                            page=page,
                            facet=facet,
                            counter=counter,
                            result=updated_responses)

    @perms_role_required(app_cli.app_name, app_cli.resource_type_name, app_cli.op.Operation_Audit,
                         app_cli.op.read, app_cli.admin_name)
    def put(self, record_id):
        res = CloudAttributeHistoryManger.update_business_active(record_id)
        return self.jsonify(res)
    
    @perms_role_required(app_cli.app_name, app_cli.resource_type_name, app_cli.op.Operation_Audit,
                         app_cli.op.read, app_cli.admin_name)
    def post(self):
        return self.get()

