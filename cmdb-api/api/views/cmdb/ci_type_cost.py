# -*- coding:utf-8 -*- 


from flask import abort
from flask import request

from api.lib.cmdb.ci_type import CITypeManager
from api.lib.cmdb.ci_type import CITypeCostManager
from api.lib.cmdb.const import PermEnum
from api.lib.cmdb.const import ResourceTypeEnum
from api.lib.cmdb.const import RoleEnum
from api.lib.cmdb.preference import PreferenceManager
from api.lib.cmdb.resp_format import ErrFormat
from api.lib.common_setting.decorator import perms_role_required
from api.lib.common_setting.role_perm_base import CMDBApp
from api.lib.decorator import args_required
from api.lib.perm.acl.acl import ACLManager
from api.lib.perm.acl.acl import has_perm_from_args
from api.lib.perm.acl.acl import is_app_admin
from api.lib.perm.acl.acl import role_required
from api.resource import APIView

app_cli = CMDBApp()


class CITypeCostView(APIView):
    url_prefix = ("/ci_type_cost", "/ci_type_cost/<int:ci_type_id>", "/ci_type_cost/del/<int:cost_id>")

    # @perms_role_required(app_cli.app_name, app_cli.resource_type_name, app_cli.op.Service_Tree_Definition,
    #                      app_cli.op.read, app_cli.admin_name)
    def get(self, ci_type_id):
        res = CITypeCostManager.get(ci_type_id)

        return self.jsonify(res)

    # @has_perm_from_args("parent_id", ResourceTypeEnum.CI, PermEnum.CONFIG, CITypeManager.get_name_by_id)
    def post(self, ci_type_id):
        cost_type_id = request.values.get("cost_type_id")
        attr_num_id = request.values.get("attr_num_id", None)
        attr_text_id = request.values.get("attr_text_id", None)
        attr_float_id = request.values.get("attr_float_id")
        cost_unit_id = request.values.get("cost_unit_id")
        cost_unit_price = request.values.get("cost_unit_price")
        cost_id = CITypeCostManager.add(ci_type_id,
                                        cost_type_id,
                                        attr_float_id,
                                        cost_unit_id,
                                        cost_unit_price,
                                        attr_num_id,
                                        attr_text_id
                                        )
        return self.jsonify(cost_id=cost_id)
        
        
        # constraint = request.values.get("constraint")
        # parent_attr_ids = request.values.get("parent_attr_ids")
        # child_attr_ids = request.values.get("child_attr_ids")
        # ctr_id = CITypeCostManager.add(parent_id, child_id, cost_type_id, constraint,
        #                                    parent_attr_ids, child_attr_ids)

        # return self.jsonify(ctr_id=ctr_id)

    # @has_perm_from_args("parent_id", ResourceTypeEnum.CI, PermEnum.CONFIG, CITypeManager.get_name_by_id)
    def delete(self, cost_id):
        CITypeCostManager.delete(cost_id)
        return self.jsonify(code=200, cost_id=cost_id)
