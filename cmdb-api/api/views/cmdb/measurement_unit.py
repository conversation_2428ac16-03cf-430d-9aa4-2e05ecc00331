# -*- coding:utf-8 -*-


from flask import abort
from flask import request

from api.lib.cmdb.relation_type import RelationTypeManager
from api.lib.cmdb.measurement_unit import MeasurementUnitManager
from api.lib.cmdb.resp_format import ErrFormat
from api.lib.common_setting.decorator import perms_role_required
from api.lib.common_setting.role_perm_base import CMDBApp
from api.lib.decorator import args_required
from api.lib.decorator import args_validate
from api.resource import APIView

app_cli = CMDBApp()


class MeasuremenUnitView(APIView):
    url_prefix = ("/measurement_unit", "/measurement_unit/<int:mu_id>")

    def get(self):
        return self.jsonify([i.to_dict() for i in MeasurementUnitManager.get_all()])

    @perms_role_required(app_cli.app_name, app_cli.resource_type_name, app_cli.op.Measurement_Unit,
                         app_cli.op.read, app_cli.admin_name)
    @args_required("name")
    @args_validate(MeasurementUnitManager.cls)
    def post(self):
        name = request.values.get("name") or abort(400, ErrFormat.argument_value_required.format("name"))
        unit_num = request.values.get("unit_num")
        
        rel = MeasurementUnitManager.add(name,unit_num)

        return self.jsonify(rel.to_dict())

    @perms_role_required(app_cli.app_name, app_cli.resource_type_name, app_cli.op.Measurement_Unit,
                         app_cli.op.read, app_cli.admin_name)
    @args_required("name")
    @args_validate(MeasurementUnitManager.cls)
    def put(self, mu_id):
        
        name = request.values.get("name") or abort(400, ErrFormat.argument_value_required.format("name"))
        unit_num = request.values.get("unit_num")
        
        rel = MeasurementUnitManager.update(mu_id,
                                            name,
                                            unit_num)

        return self.jsonify(rel.to_dict())

    @perms_role_required(app_cli.app_name, app_cli.resource_type_name, app_cli.op.Measurement_Unit,
                         app_cli.op.read, app_cli.admin_name)
    def delete(self, mu_id):
        MeasurementUnitManager.delete(mu_id)

        return self.jsonify(mu_id=mu_id)
