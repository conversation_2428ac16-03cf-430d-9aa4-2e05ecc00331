# -*- coding:utf-8 -*- 

import time

import six
from flask import abort
from flask import current_app
from flask import request

from api.lib.cmdb.cache import CITypeCache
from api.lib.cmdb.ci import CIManager
from api.lib.cmdb.cloud_ci import CloudCIManager
from api.lib.cmdb.ci import CIRelationManager
from api.lib.cmdb.const import ExistPolicy
from api.lib.cmdb.const import ResourceTypeEnum, PermEnum
from api.lib.cmdb.const import RetKey
from api.lib.cmdb.perms import has_perm_for_ci
from api.lib.cmdb.search import SearchError
from api.lib.cmdb.search.ci import search
from api.lib.decorator import args_required
from api.lib.perm.acl.acl import has_perm_from_args
from api.lib.utils import get_page
from api.lib.utils import get_page_size
from api.lib.utils import handle_arg_list
from api.models.cmdb import CI
from api.resource import APIView

from api.lib.cmdb.history import AttributeHistoryManger

class CloudCIView(APIView):
    url_prefix = ("/clound_ci/<int:ci_id>", "/clound_ci")

    @staticmethod
    def _wrap_ci_dict():
        ci_dict = {k: v.strip() if isinstance(v, six.string_types) else v for k, v in request.values.items()
                   if k != "ci_type" and not k.startswith("_")}

        return ci_dict

    @has_perm_for_ci("ci_type", ResourceTypeEnum.CI, PermEnum.ADD, lambda x: CITypeCache.get(x))
    def post(self):
        ci_type = request.values.get("ci_type")
        bussiness_type = request.values.pop("bussiness_type")
        ticket_id = request.values.pop("ticket_id", None)
        _no_attribute_policy = request.values.get("no_attribute_policy", ExistPolicy.IGNORE)

        exist_policy = request.values.pop('exist_policy', None)

        ci_dict = self._wrap_ci_dict()

        manager = CloudCIManager()
        ci_id = manager.add(ci_type,
                            bussiness_type=bussiness_type,
                            exist_policy=exist_policy or ExistPolicy.REJECT,
                            _no_attribute_policy=_no_attribute_policy,
                            _is_admin=request.values.pop('__is_admin', None) or False,
                            ticket_id=ticket_id,
                            **ci_dict)

        return self.jsonify(ci_id=ci_id)

