# -*- coding:utf-8 -*-

from flask import request
from api.lib.cmdb.load.load_history import LoadHistoryManager
from api.lib.common_setting.decorator import perms_role_required
from api.lib.common_setting.role_perm_base import CMDBApp
from api.lib.utils import get_page, get_page_size
from api.resource import APIView

app_cli = CMDBApp()

class LoadHistoryView(APIView):
    """负载数据导入历史记录视图"""
    url_prefix = ("/load/histories", "/load/histories/<int:history_id>")

    def get(self, history_id=None):
        """获取导入历史记录
        
        Args:
            history_id: 历史记录ID，如果提供则返回单个记录详情
            
        Query Parameters:
            page: 页码，默认1
            page_size: 每页大小，默认20
            username: 用户名，支持模糊搜索
            type_id: CI类型ID
            status: 状态 (PENDING/PROCESSING/COMPLETED/FAILED)
            file_name: 文件名，支持模糊搜索
            
        Returns:
            JSON响应:
                - 单个记录时返回记录详情
                - 列表时返回:
                    total: 总记录数
                    page: 当前页码
                    page_size: 每页大小
                    items: 历史记录列表
        """
        if history_id is not None:
            # 获取单个记录详情
            from flask import current_app
            current_app.logger.debug(f"获取单个记录详情: {history_id}")
            history = LoadHistoryManager.get_by_id(history_id)
            if not history:
                return self.jsonify(message=f"History record {history_id} not found"), 404
            return self.jsonify(history)
            
        # 获取查询参数
        page = get_page(request.values.get("page", 1))
        page_size = get_page_size(request.values.get("page_size", 20))
        username = request.values.get("username")
        type_id = request.values.get("type_id")
        if type_id:
            type_id = int(type_id)
        status = request.values.get("status")
        file_name = request.values.get("file_name")
        
        # 查询历史记录
        total, items = LoadHistoryManager.get(
            page=page,
            page_size=page_size,
            username=username,
            type_id=type_id,
            status=status,
            file_name=file_name
        )
        
        return self.jsonify({
            'total': total,
            'page': page,
            'page_size': page_size,
            'items': items
        })
