# -*- coding:utf-8 -*-

import json
from flask import request, abort, current_app

from api.lib.cmdb.load.load_attr import LoadAttrManager, LoadRelationsManager, LoadDataManager
from api.lib.cmdb.load.const import LoadValueTypeEnum, PeriodTypeEnum, HistoryStatusEnum, OperateTypeEnum, TimeSlotEnum
from api.lib.common_setting.decorator import perms_role_required
from api.lib.common_setting.role_perm_base import CMDBApp
from api.lib.decorator import args_required
from api.lib.utils import get_page, get_page_size
from api.models.cmdb import LoadAttribute, LoadDataImportHistory
from api.resource import APIView
from api.lib.cmdb.resp_format import ErrFormat

app_cli = CMDBApp()


class LoadAttrView(APIView):
    """负载字段管理视图"""
    url_prefix = ("/load/attributes", "/load/attributes/<int:attr_id>")

    @perms_role_required(app_cli.app_name, app_cli.resource_type_name, app_cli.op.Operation_Audit,
                         app_cli.op.read, app_cli.admin_name)
    def get(self, attr_id=None):
        """获取负载字段列表或单个字段详情
        
        Args:
            attr_id: 字段ID，如果提供则返回单个字段详情
            
        Query Parameters:
            page: 页码，默认1
            page_size: 每页大小，默认20
            name: 字段名称，支持模糊搜索
            value_type: 值类型
            
        Returns:
            单个字段详情或分页列表
        """
        
        if attr_id is not None:
            attr = LoadAttrManager.get_attr(attr_id)
            if not attr:
                current_app.logger.warning(f"负载字段不存在: {attr_id}")
                return abort(404, ErrFormat.load_attr_not_found.format(attr_id))
            return self.jsonify(attr.to_dict())

        # 获取查询参数
        page = get_page(request.values.get("page", 1))
        page_size = get_page_size(request.values.get("page_size"))
        name = request.values.get("name")
        value_type = request.values.get("value_type")
        
        current_app.logger.debug(
            f"查询负载字段列表: page={page}, page_size={page_size}, "
            f"name={name}, value_type={value_type}"
        )
        
        # 构建查询
        query = LoadAttribute.query.filter_by(deleted=False)
        if name:
            query = query.filter(LoadAttribute.name.like(f"%{name}%"))
        if value_type:
            if not LoadValueTypeEnum.is_valid(value_type):
                current_app.logger.warning(f"无效的值类型: {value_type}")
                return abort(400, ErrFormat.load_attr_value_type_invalid.format(value_type))
            query = query.filter_by(value_type=value_type)
            
        # 执行查询
        total = query.count()
        attrs = query.order_by(LoadAttribute.id.desc())\
            .offset((page - 1) * page_size)\
            .limit(page_size)\
            .all()
        
        return self.jsonify(
            page=page,
            page_size=page_size,
            total=total,
            attrs=[attr.to_dict() for attr in attrs]
        )

    @perms_role_required(app_cli.app_name, app_cli.resource_type_name, app_cli.op.Operation_Audit,
                         app_cli.op.read, app_cli.admin_name)
    @args_required("name", "value_type")
    def post(self):
        """创建负载字段
        
        Required Parameters:
            name: 字段名称
            value_type: 值类型
            
        Optional Parameters:
            alias: 字段别名
            is_monthly: 是否为月度字段
            
        Returns:
            创建的字段信息
        """
        name = request.values.get("name")
        value_type = request.values.get("value_type")
        alias = request.values.get("alias")
        is_monthly = request.values.get("is_monthly", False)
        try:
            attr = LoadAttrManager.add_attr(
                name=name,
                value_type=value_type,
                alias=alias,
                is_monthly=is_monthly
            )
            current_app.logger.debug(f"负载字段创建成功: {attr.id}")
            return self.jsonify(attr.to_dict())
        except Exception as e:
            current_app.logger.error(f"创建负载字段失败: {str(e)}")
            raise

    @perms_role_required(app_cli.app_name, app_cli.resource_type_name, app_cli.op.Operation_Audit,
                         app_cli.op.read, app_cli.admin_name)
    def put(self, attr_id):
        """更新负载字段
        
        Args:
            attr_id: 字段ID
            
        Optional Parameters:
            name: 字段名称
            value_type: 值类型
            alias: 字段别名
            is_monthly: 是否为月度字段
            
        Returns:
            更新后的字段信息
        """
        updates = {}
        for key in ["name", "value_type", "alias", "is_monthly"]:
            if key in request.values:
                updates[key] = request.values.get(key)
                

        try:
            attr = LoadAttrManager.update_attr(attr_id, **updates)
            current_app.logger.debug(f"负载字段更新成功: {attr_id}")
            return self.jsonify(attr.to_dict())
        except Exception as e:
            current_app.logger.error(f"更新负载字段失败: {str(e)}")
            raise

    @perms_role_required(app_cli.app_name, app_cli.resource_type_name, app_cli.op.Operation_Audit,
                         app_cli.op.read, app_cli.admin_name)
    def delete(self, attr_id):
        """删除负载字段
        
        Args:
            attr_id: 字段ID
            
        Returns:
            删除结果
        """
        
        try:
            LoadAttrManager.delete_attr(attr_id)
            current_app.logger.debug(f"负载字段删除成功: {attr_id}")
            return self.jsonify(message="success")
        except Exception as e:
            current_app.logger.error(f"删除负载字段失败: {str(e)}")
            raise


class LoadRelationsView(APIView):
    """CI类型负载字段关联管理视图"""
    url_prefix = ("/load/relations", "/load/relations/<int:type_id>")

    def get(self, type_id=None):
        """获取CI类型关联的负载字段"""
        if type_id is None:
            type_id = request.values.get("type_id")

        attrs = LoadRelationsManager.get_type_attrs(type_id)
        return self.jsonify(attrs=[attr.to_dict() for attr in attrs])

    @perms_role_required(app_cli.app_name, app_cli.resource_type_name, app_cli.op.Operation_Audit,
                         app_cli.op.read, app_cli.admin_name)
    @args_required("type_id", "attr_configs")
    def post(self):
        """批量添加CI类型的负载字段关联"""
        type_id = request.values.get("type_id")
        attr_configs = request.values.get("attr_configs")
        if not isinstance(attr_configs, list):
            return abort(400, ErrFormat.argument_attributes_must_be_list)

        # 确保每个配置都包含必要的字段
        for config in attr_configs:
            if not isinstance(config, dict) or "attr_id" not in config:
                return abort(400, ErrFormat.argument_value_invalid.format("attr_configs"))
            
            LoadRelationsManager.add_type_attr(
                type_id=type_id,
                attr_id=config["attr_id"],
                is_required=config.get("is_required", False),
                order=config.get("order", 0)
            )
        
        attrs = LoadRelationsManager.get_type_attrs(type_id)
        return self.jsonify(attrs=[attr.to_dict() for attr in attrs])


    @perms_role_required(app_cli.app_name, app_cli.resource_type_name, app_cli.op.Operation_Audit,
                         app_cli.op.read, app_cli.admin_name)
    def delete(self, type_id):
        """删除CI类型的字段关联"""
        attr_id = request.values.get("attr_id")
        if attr_id:
            # 删除单个字段关联
            LoadRelationsManager.delete_type_attr(type_id, int(attr_id))
            current_app.logger.debug(f"Deleted attr {attr_id} from CI type {type_id}")
        else:
            # 删除所有字段关联
            attr_relations = LoadRelationsManager.get_type_attrs(type_id)
            for relation in attr_relations:
                LoadRelationsManager.delete_type_attr(type_id, relation.id)
            current_app.logger.debug(f"Deleted all attrs from CI type {type_id}")
        return self.jsonify(message="success")


class LoadDataView(APIView):
    """负载数据管理视图"""
    url_prefix = ("/load/data", "/load/data/<int:type_id>")

    def get(self, type_id=None):
        """查询负载数据 - 支持时间段
        
        Query Parameters:
            ci_ids: CI ID列表,逗号分隔
            unique_values: 唯一标识值列表,逗号分隔
            type_id: CI类型ID (当使用unique_values时必填)
            attribute_ids: 属性ID列表,逗号分隔
            start_period: 开始周期 (YYYY-MM-DD或YYYY-MM)
            end_period: 结束周期 (YYYY-MM-DD或YYYY-MM)
            period_type: 周期类型 (0/1)
            time_slot: 时间段标识(可选)，只对每日数据有效
        """
        # 解析查询参数
        ci_ids = request.values.get("ci_ids")
        unique_values = request.values.get("unique_values")
        attribute_ids = request.values.get("attribute_ids")
        start_period = request.values.get("start_period")
        end_period = request.values.get("end_period")
        period_type = request.values.get("period_type", PeriodTypeEnum.DAILY)
        
        # 添加时间段参数解析
        time_slot = request.values.get("time_slot")
        
        # 新增分页参数
        page = get_page(request.values.get("page", 1))
        page_size = get_page_size(request.values.get("page_size"))

        current_app.logger.debug(
            f"Query load data with params: ci_ids={ci_ids}, unique_values={unique_values}, "
            f"type_id={type_id}, attribute_ids={attribute_ids}, start_period={start_period}, "
            f"time_slot={time_slot}, "
            f"end_period={end_period}, period_type={period_type}, page={page}, page_size={page_size}"
        )

        # 验证period_type
        if not PeriodTypeEnum.is_valid(period_type):
            current_app.logger.error(f"Invalid period_type: {period_type}")
            return abort(400, ErrFormat.argument_value_invalid.format("period_type"))

        # 验证时间段参数
        if time_slot and not TimeSlotEnum.validate_slot(time_slot):
            return abort(400, ErrFormat.load_attr_time_slot_invalid.format(time_slot))
        elif not time_slot:
            time_slot = TimeSlotEnum.DEFAULT_SLOT

        # 转换参数
        if ci_ids:
            ci_ids = [int(x) for x in ci_ids.split(",")]
        if unique_values:
            unique_values = unique_values.split(",")
        if attribute_ids:
            attribute_ids = [int(x) for x in attribute_ids.split(",")]
            
        # 如果URL中提供了type_id,使用URL中的值
        if not type_id:
            type_id = request.values.get("type_id")
            if type_id:
                type_id = int(type_id)

        # 查询数据
        try:
            result = LoadDataManager.query_data(
                ci_ids=ci_ids,
                unique_values=unique_values,
                type_id=type_id,
                attribute_ids=attribute_ids,
                start_period=start_period,
                end_period=end_period,
                period_type=period_type,
                time_slot=time_slot,  # 传递时间段参数
                page=page,
                page_size=page_size
            )
            return self.jsonify(result)
        except Exception as e:
            current_app.logger.error(f"Failed to query load data: {str(e)}")
            raise

    @args_required("upload_data")
    def post(self, type_id):
        """批量导入负载数据 - 支持时间段
        
        Args:
            type_id: CI类型ID
            
        Request Parameters:
            upload_data: 数据列表,格式为:
                [
                    {
                        "unique_value": "xxx",
                        "2024-12-01": {
                            "field1": value1,
                            "field2": value2
                        },
                        "2024-12-02": {
                            "field1": value3,
                            "field2": value4
                        }
                    }
                ]
            file_name: 文件名（可选）
            batch_size: 批次大小（可选，默认5000）
            time_slot: 时间段标识（可选）
        """
        try:
            upload_data = request.values.get("upload_data")
            if isinstance(upload_data, str):
                try:
                    upload_data = json.loads(upload_data)
                except json.JSONDecodeError:
                    return abort(400, ErrFormat.argument_value_invalid.format("upload_data must be valid JSON"))

            if not isinstance(upload_data, list):
                return abort(400, ErrFormat.argument_value_invalid.format("upload_data must be a list"))

            # 获取可选参数
            file_name = request.values.get("file_name")
            batch_size = int(request.values.get("batch_size", 5000))
            
            # 添加时间段参数解析
            time_slot = request.values.get("time_slot")

            # 验证时间段参数
            if time_slot and not TimeSlotEnum.validate_slot(time_slot):
                return abort(400, ErrFormat.load_attr_time_slot_invalid.format(time_slot))

            # 执行批量导入
            result = LoadDataManager.batch_import(
                type_id=type_id,
                data=upload_data,
                time_slot=time_slot,  # 传递时间段参数
                batch_size=batch_size,
                file_name=file_name
            )

            current_app.logger.debug(f"Batch import result: {result}")
            return self.jsonify(result)

        except Exception as e:
            current_app.logger.error(f"导入失败: {str(e)}")
            raise


class LoadAttrGroupedCITypesView(APIView):
    """负载字段关联的CI类型分组视图"""
    url_prefix = "/load/ci_types/groups"

    def get(self):
        """获取已配置负载字段的CI类型分组列表
        
        Returns:
            JSON响应:
                groups: 分组列表，每个分组包含:
                    - id: 分组ID
                    - name: 分组名称
                    - ci_types: 该分组下配置了负载字段的CI类型列表
        """
        
        # 调用manager方法获取分组数据
        grouped_ci_types = LoadAttrManager.get_grouped_ci_types_with_load_attrs()
        
        current_app.logger.debug(
            f"成功获取CI类型分组列表: 共{len(grouped_ci_types)}个分组, "
        )
        
        return self.jsonify(grouped_ci_types)


class TimeSlotInfoView(APIView):
    """时间段信息查询视图，暂时没有实际用途，保留"""
    url_prefix = "/load/time_slots"

    def get(self):
        """获取所有时间段信息"""
        return self.jsonify({
            "time_slots": TimeSlotEnum.get_all_slots_info(),
            "default_slot": TimeSlotEnum.DEFAULT_SLOT,
            "description": "时间段用于区分每日负载数据的不同时间窗口"
        })

