from flask import request
from api.lib.cmdb.ci import CIRelationManager
from api.resource import APIView





class CloudCIRelationView(APIView):
    url_prefix = "/cloud_ci_relations/<int:first_ci_id>/<int:second_ci_id>"

    def post(self, first_ci_id, second_ci_id):
        ancestor_ids = request.values.get('ancestor_ids') or None

        manager = CIRelationManager()
        res = manager.add(first_ci_id, second_ci_id, ancestor_ids=ancestor_ids)

        return self.jsonify(cr_id=res)

    def delete(self, first_ci_id, second_ci_id):
        ancestor_ids = request.values.get('ancestor_ids') or None

        manager = CIRelationManager()
        manager.delete_2(first_ci_id, second_ci_id, ancestor_ids=ancestor_ids)

        return self.jsonify(message="CIType Relation is deleted")