# -*- coding:utf-8 -*- 

import time
import datetime

import six
from flask import abort
from flask import current_app
from flask import request
from flask_login import current_user

from api.lib.cmdb.cache import CITypeCache
from api.lib.cmdb.ci import CIManager
from api.lib.cmdb.ci import CIRelationManager
from api.lib.cmdb.const import ExistPolicy
from api.lib.cmdb.const import ResourceTypeEnum, PermEnum
from api.lib.cmdb.const import RetKey
from api.lib.cmdb.perms import has_perm_for_ci
from api.lib.cmdb.resp_format import ErrFormat
from api.lib.cmdb.search import SearchError
from api.lib.cmdb.search.ci import search
from api.lib.decorator import args_required
from api.lib.perm.acl.acl import has_perm_from_args, ACLManager
from api.lib.utils import get_page
from api.lib.utils import get_page_size
from api.lib.utils import handle_arg_list
from api.models.cmdb import CI
from api.resource import APIView
from api.lib.common_setting.role_perm_base import CMDBApp
from api.lib.common_setting.decorator import perms_role_required

app_cli = CMDBApp()


class CIsByTypeView(APIView):
    url_prefix = "/ci/type/<int:type_id>"

    def get(self, type_id):
        fields = handle_arg_list(request.values.get("fields", ""))

        ret_key = request.values.get("ret_key", RetKey.NAME)
        if ret_key not in (RetKey.NAME, RetKey.ALIAS, RetKey.ID):
            ret_key = RetKey.NAME

        page = get_page(request.values.get("page", 1))
        count = get_page_size(request.values.get("count"))

        manager = CIManager()
        res = manager.get_cis_by_type(type_id,
                                      ret_key=ret_key,
                                      fields=fields,
                                      page=page,
                                      per_page=count)

        return self.jsonify(type_id=type_id,
                            numfound=res[0],
                            total=len(res[2]),
                            page=res[1],
                            cis=res[2])


class CIView(APIView):
    url_prefix = ("/ci/<int:ci_id>", "/ci")

    def get(self, ci_id):
        fields = handle_arg_list(request.values.get("fields", ""))

        ret_key = request.values.get("ret_key", RetKey.NAME)
        if ret_key not in (RetKey.NAME, RetKey.ALIAS, RetKey.ID):
            ret_key = RetKey.NAME
        manager = CIManager()
        ci = manager.get_ci_by_id_from_db(ci_id, ret_key=ret_key, fields=fields, valid=True)

        return self.jsonify(ci_id=ci_id, ci=ci)

    @staticmethod
    def _wrap_ci_dict():
        ci_dict = {k: v.strip() if isinstance(v, six.string_types) else v for k, v in request.values.items()
                   if k != "ci_type" and not k.startswith("_")}

        return ci_dict

    @perms_role_required(app_cli.app_name, app_cli.resource_type_name, app_cli.op.Operation_Audit,
                         app_cli.op.read, app_cli.admin_name)
    @has_perm_for_ci("ci_type", ResourceTypeEnum.CI, PermEnum.ADD, lambda x: CITypeCache.get(x))
    def post(self):
        ci_type = request.values.get("ci_type")
        ticket_id = request.values.pop("ticket_id", None)
        _no_attribute_policy = request.values.get("no_attribute_policy", ExistPolicy.IGNORE)

        exist_policy = request.values.pop('exist_policy', None)

        ci_dict = self._wrap_ci_dict()

        manager = CIManager()
        ci_id = manager.add(ci_type,
                            exist_policy=exist_policy or ExistPolicy.REJECT,
                            _no_attribute_policy=_no_attribute_policy,
                            _is_admin=request.values.pop('__is_admin', None) or False,
                            ticket_id=ticket_id,
                            **ci_dict)

        return self.jsonify(ci_id=ci_id)

    @perms_role_required(app_cli.app_name, app_cli.resource_type_name, app_cli.op.Operation_Audit,
                         app_cli.op.read, app_cli.admin_name)
    @has_perm_for_ci("ci_id", ResourceTypeEnum.CI, PermEnum.UPDATE, CIManager.get_type)
    def put(self, ci_id=None):
        args = request.values
        ci_type = args.get("ci_type")
        ticket_id = request.values.pop("ticket_id", None)
        _no_attribute_policy = args.get("no_attribute_policy", ExistPolicy.IGNORE)

        ci_dict = self._wrap_ci_dict()
        manager = CIManager()
        if ci_id is not None:
            manager.update(ci_id,
                           _is_admin=request.values.pop('__is_admin', None) or False,
                           ticket_id=ticket_id,
                           **ci_dict)
        else:
            request.values.pop('exist_policy', None)
            ci_id = manager.add(ci_type,
                                exist_policy=ExistPolicy.REPLACE,
                                _no_attribute_policy=_no_attribute_policy,
                                _is_admin=request.values.pop('__is_admin', None) or False,
                                ticket_id=ticket_id,
                                **ci_dict)

        return self.jsonify(ci_id=ci_id)

    @perms_role_required(app_cli.app_name, app_cli.resource_type_name, app_cli.op.Operation_Audit,
                         app_cli.op.read, app_cli.admin_name)
    @has_perm_for_ci("ci_id", ResourceTypeEnum.CI, PermEnum.DELETE, CIManager.get_type)
    def delete(self, ci_id):
        manager = CIManager()
        manager.delete(ci_id)

        return self.jsonify(message="ok")


class CIDetailView(APIView):
    url_prefix = "/ci/<int:ci_id>/detail"

    def get(self, ci_id):
        _ci = CI.get_by_id(ci_id).to_dict()

        return self.jsonify(**_ci)


class CISearchView(APIView):
    url_prefix = ("/ci/s", "/ci/search")

    def get(self):
        """@params: q: query statement
                    fl: filter by column
                    count/page_size: the number of ci
                    ret_key: id, name, alias
                    facet: statistic
        """
        page = get_page(request.values.get("page", 1))
        count = get_page_size(request.values.get("count") or request.values.get("page_size"))

        query = request.values.get('q', "")
        fl = handle_arg_list(request.values.get('fl', ""))
        excludes = handle_arg_list(request.values.get('excludes', ""))
        ret_key = request.values.get('ret_key', RetKey.NAME)
        if ret_key not in (RetKey.NAME, RetKey.ALIAS, RetKey.ID):
            ret_key = RetKey.NAME
        facet = handle_arg_list(request.values.get("facet", ""))
        sort = request.values.get("sort")
        use_id_filter = request.values.get("use_id_filter", False) in current_app.config.get('BOOL_TRUE')

        start = time.time()
        s = search(query, fl, facet, page, ret_key, count, sort, excludes, use_id_filter=use_id_filter)
        try:
            response, counter, total, page, numfound, facet = s.search()
        except SearchError as e:
            return abort(400, str(e))

        if request.values.get('need_children') in current_app.config.get('BOOL_TRUE') and len(response) == 1:
            children = CIRelationManager.get_children(response[0]['_id'], ret_key=ret_key)  # one floor
            response[0].update(children)

        current_app.logger.debug("search time is: {0}".format(time.time() - start))

        return self.jsonify(numfound=numfound,
                            total=total,
                            page=page,
                            facet=facet,
                            counter=counter,
                            result=response)

    def post(self):
        return self.get()


class CIUnique(APIView):
    url_prefix = "/ci/<int:ci_id>/unique"

    @has_perm_from_args("ci_id", ResourceTypeEnum.CI, PermEnum.UPDATE, CIManager.get_type_name)
    def put(self, ci_id):
        params = request.values
        unique_name = list(params.keys())[0]
        unique_value = list(params.values())[0]

        CIManager.update_unique_value(ci_id, unique_name, unique_value)

        return self.jsonify(ci_id=ci_id)


class CIHeartbeatView(APIView):
    url_prefix = ("/ci/heartbeat", "/ci/heartbeat/<string:ci_type>/<string:unique>")

    def get(self):
        page = get_page(request.values.get("page", 1))
        ci_type = request.values.get("ci_type", "").strip()
        try:
            type_id = CITypeCache.get(ci_type).type_id
        except AttributeError:
            return self.jsonify(numfound=0, result=[])
        agent_status = request.values.get("agent_status")
        if agent_status:
            agent_status = int(agent_status)

        numfound, result = CIManager.get_heartbeat(page, type_id, agent_status=agent_status)

        return self.jsonify(numfound=numfound, result=result)

    @perms_role_required(app_cli.app_name, app_cli.resource_type_name, app_cli.op.Operation_Audit,
                         app_cli.op.read, app_cli.admin_name)
    def post(self, ci_type, unique):
        if not unique or not ci_type:
            return self.jsonify(message="error")

        msg, cmd = CIManager().add_heartbeat(ci_type, unique)

        return self.jsonify(message=msg, cmd=cmd)


class CIFlushView(APIView):
    url_prefix = ("/ci/flush", "/ci/<int:ci_id>/flush")

    def get(self, ci_id=None):
        from api.tasks.cmdb import ci_cache
        from api.lib.cmdb.const import CMDB_QUEUE
        if ci_id is not None:
            ci_cache.apply_async(args=(ci_id, None, None), queue=CMDB_QUEUE)
        else:
            cis = CI.get_by(to_dict=False)
            for ci in cis:
                ci_cache.apply_async(args=(ci.id, None, None), queue=CMDB_QUEUE)

        return self.jsonify(code=200)


class CIAutoDiscoveryStatisticsView(APIView):
    url_prefix = "/ci/adc/statistics"

    def get(self):
        return self.jsonify(CIManager.get_ad_statistics())


class CIPasswordView(APIView):
    url_prefix = "/ci/<int:ci_id>/attributes/<int:attr_id>/password"

    def get(self, ci_id, attr_id):
        return self.jsonify(ci_id=ci_id, attr_id=attr_id, value=CIManager.load_password(ci_id, attr_id))

    def post(self, ci_id, attr_id):
        return self.get(ci_id, attr_id)


class CIBaselineView(APIView):
    url_prefix = ("/ci/baseline", "/ci/<int:ci_id>/baseline/rollback")

    def _check_time_limit(self, before_date):
        """检查非管理员用户的时间限制和权限
        
        Args:
            before_date: 查询的时间点
            
        Returns:
            None 如果检查通过，否则返回abort响应
        """
        user_info = ACLManager.get_user_info(current_user.username)
        is_admin = any(role in user_info['parents'] for role in ['admin', 'cmdb_admin'])
        
        # 检查时间限制
        try:
            before_date_time = datetime.datetime.strptime(before_date, "%Y-%m-%d %H:%M:%S")
            now = datetime.datetime.now()
            if not is_admin and (now - before_date_time).total_seconds() > 24 * 60 * 60:
                return abort(400, ErrFormat.time_limit_exceeded.format("24小时"))
        except ValueError:
            return abort(400, ErrFormat.datetime_argument_invalid.format("before_date"))
        
        return None, is_admin

    @args_required("before_date")
    def get(self):
        ci_ids = handle_arg_list(request.values.get('ci_ids'))
        before_date = request.values.get('before_date')
        
        # 检查用户权限和时间限制
        time_check, is_admin = self._check_time_limit(before_date)
        if time_check:
            return time_check
        
        # 非管理员用户只能查询自己的变更
        username = None if is_admin else current_user.username
        
        result, _ = CIManager().baseline(list(map(int, ci_ids)), before_date, username=username)

        return self.jsonify(result)

    @args_required("before_date")
    @has_perm_for_ci("ci_id", ResourceTypeEnum.CI, PermEnum.UPDATE, CIManager.get_type)
    def post(self, ci_id):
        if 'rollback' in request.url:
            before_date = request.values.get('before_date')
            
            # 检查用户权限和时间限制
            time_check, is_admin = self._check_time_limit(before_date)
            if time_check:
                return time_check
                
            # 非管理员用户只能回滚自己的变更
            username = None if is_admin else current_user.username

            return self.jsonify(**CIManager().rollback(ci_id, before_date, username=username))

        return self.get(ci_id)
