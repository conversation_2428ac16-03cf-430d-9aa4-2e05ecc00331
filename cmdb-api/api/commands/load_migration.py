import click
import time
from flask import current_app
from flask.cli import with_appcontext
from sqlalchemy import text
from api.extensions import db
from api.lib.cmdb.load.const import TimeSlotEnum

@click.command()
@with_appcontext
@click.option('--dry-run', is_flag=True, help='只显示将要执行的操作，不实际执行')
@click.option('--batch-size', default=1000, help='批处理大小，默认1000')
def migrate_load_time_slot(dry_run, batch_size):
    """为现有每日负载数据设置默认时间段"""
    
    tables = [
        'c_load_value_ints',
        'c_load_value_floats', 
        'c_load_value_texts',
        'c_load_value_lists'
    ]
    
    default_time_slot = TimeSlotEnum.DEFAULT_SLOT
    
    click.echo("开始为现有每日数据设置默认时间段...")
    click.echo(f"默认时间段: {default_time_slot} ({TimeSlotEnum.get_slot_description(default_time_slot)})")
    
    total_processed = 0
    
    for table in tables:
        click.echo(f"\n处理表: {table}")
        
        # 统计需要更新的记录数
        count_sql = f"""
            SELECT COUNT(*) as count 
            FROM {table} 
            WHERE collect_date IS NOT NULL 
            AND (time_slot IS NULL OR time_slot = '')
        """
        
        if dry_run:
            click.echo(f"[试运行] 统计SQL: {count_sql}")
        else:
            result = db.session.execute(text(count_sql)).fetchone()
            total_count = result[0] if result else 0
            
            if total_count == 0:
                click.echo(f"表 {table} 无需要更新的记录")
                continue
            
            click.echo(f"表 {table} 需要更新 {total_count} 条记录")
            
            # 分批更新
            updated_count = 0
            batch_count = 0
            
            while updated_count < total_count:
                batch_count += 1
                start_time = time.time()
                
                update_sql = f"""
                    UPDATE {table} 
                    SET time_slot = '{default_time_slot}' 
                    WHERE collect_date IS NOT NULL 
                    AND (time_slot IS NULL OR time_slot = '') 
                    LIMIT {batch_size}
                """
                
                try:
                    result = db.session.execute(text(update_sql))
                    affected_rows = result.rowcount
                    db.session.commit()
                    
                    updated_count += affected_rows
                    total_processed += affected_rows
                    end_time = time.time()
                    
                    click.echo(f"  批次 {batch_count}: 更新 {affected_rows} 条记录 "
                              f"(总进度: {updated_count}/{total_count}) "
                              f"耗时: {end_time - start_time:.2f}s")
                    
                    if affected_rows == 0:
                        break
                        
                except Exception as e:
                    click.echo(f"✗ 批次 {batch_count} 更新失败: {str(e)}")
                    db.session.rollback()
                    break
    
    if not dry_run:
        click.echo(f"\n数据迁移完成! 总共处理 {total_processed} 条记录")
    else:
        click.echo("\n试运行完成，使用不带 --dry-run 参数执行实际迁移")


@click.command()
@with_appcontext
def verify_load_time_slot():
    """验证负载数据时间段迁移结果"""
    
    tables = [
        'c_load_value_ints',
        'c_load_value_floats', 
        'c_load_value_texts',
        'c_load_value_lists'
    ]
    
    click.echo("开始验证迁移结果...")
    
    all_success = True
    
    for table in tables:
        # 检查是否还有未设置时间段的每日数据
        check_sql = f"""
            SELECT COUNT(*) as count 
            FROM {table} 
            WHERE collect_date IS NOT NULL 
            AND (time_slot IS NULL OR time_slot = '')
        """
        
        result = db.session.execute(text(check_sql)).fetchone()
        remaining = result[0] if result else 0
        
        if remaining > 0:
            click.echo(f"❌ 表 {table}: 仍有 {remaining} 条每日数据未设置时间段")
            all_success = False
        else:
            click.echo(f"✅ 表 {table}: 验证通过，所有每日数据已设置时间段")
    
    if all_success:
        click.echo("\n🎉 所有表验证通过，迁移成功!")
    else:
        click.echo("\n⚠️ 部分表验证失败，请检查迁移过程") 