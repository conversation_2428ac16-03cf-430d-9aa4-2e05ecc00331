[pytest]
testpaths = .
python_files = test_*.py
python_classes = Test*
python_functions = test_*

log_cli = true
log_cli_level = WARNING
log_cli_format = %(asctime)s [%(levelname)s] %(message)s (%(filename)s:%(lineno)s)
log_cli_date_format = %Y-%m-%d %H:%M:%S

addopts = -v --tb=short

filterwarnings =
    ignore::DeprecationWarning:flask_sqlalchemy.*:
    ignore::DeprecationWarning:flask_caching.*:
    ignore::DeprecationWarning:marshmallow.*:
    ignore::DeprecationWarning:pyasn1.*:
    ignore::sqlalchemy.exc.SAWarning:
    ignore::DeprecationWarning:api.lib.perm.*:
    ignore::sqlalchemy.exc.LegacyAPIWarning:
    ignore::DeprecationWarning:flask.*:
    ignore::DeprecationWarning