from flask import has_request_context, _request_ctx_stack, current_app
from flask.testing import FlaskClient
import logging

logger = logging.getLogger(__name__)

TEST_USER_CONFIGS = {
    'admin': {
        'username': 'admin',
        'session_data': {
            'acl': {
                'parentRoles': ['acl_admin', 'CMDB_ADMIN'],
                'username': 'admin',
                'uid': 1
            }
        }
    }
}


class CMDBTestClient(FlaskClient):
    """增强的测试客户端，支持会话管理和上下文复用"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._auth_user = None
        self._session_data = {}
        self._current_ctx = None
        logger.info("初始化CMDBTestClient")
        
    def login(self, username='admin', session_data=None):
        """设置认证信息"""
        logger.info(f"设置认证信息: username={username}, session_data={session_data}")
        self._auth_user = TEST_USER_CONFIGS[username]
        self._session_data = self._auth_user['session_data']
        return self

    def open(self, *args, **kwargs):
        """发送请求，支持上下文复用
        
        如果已存在请求上下文，则复用该上下文，否则创建新的上下文。
        这样可以避免在同一测试中出现上下文冲突。
        """
        method = kwargs.get('method', 'GET')
        url = args[0] if args else kwargs.get('path', '')
        logger.info(f"发送请求: method={method}, url={url}")
        
        ctx_stack = _request_ctx_stack.top
        if ctx_stack:
            logger.info(f"当前请求上下文: id={id(ctx_stack)}")
        
        if has_request_context():
            logger.info(f"复用现有请求上下文: id={id(_request_ctx_stack.top)}")
            # 在现有上下文中执行请求
            if self._auth_user:
                logger.info(f"在现有上下文中更新会话数据: user={self._auth_user}")
                with self.session_transaction() as sess:
                    sess.update(self._session_data)
            return super().open(*args, **kwargs)
        else:
            logger.info("创建新的请求上下文")
            # 创建新的请求上下文
            response = super().open(*args, **kwargs)
            if self._auth_user:
                logger.info(f"在新上下文中设置会话数据: user={self._auth_user}")
                with self.session_transaction() as sess:
                    sess.update(self._session_data)
            
            if _request_ctx_stack.top:
                logger.info(f"新创建的请求上下文: id={id(_request_ctx_stack.top)}")
            return response

    def __enter__(self):
        """支持with语句，确保上下文的正确管理"""
        if not has_request_context():
            self._current_ctx = self.application.test_request_context()
            self._current_ctx.push()
            logger.info(f"进入新的请求上下文: id={id(self._current_ctx)}")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出时清理上下文"""
        if self._current_ctx is not None:
            logger.info(f"退出并清理请求上下文: id={id(self._current_ctx)}")
            self._current_ctx.pop()
            self._current_ctx = None 