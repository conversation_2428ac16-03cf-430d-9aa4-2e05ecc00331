"""
测试辅助函数

提供测试数据初始化和管理的工具函数
"""
import random
import uuid
from typing import List, Optional
import logging
from faker import Faker

from api.models.cmdb import (
    Attribute,
    CIType,
    CITypeAttributeGroup,
    CITypeAttribute,
    CITypeRelation,
    RelationType,
    CI
)
from api.lib.cmdb.ci import CIManager, CIRelationManager
from api.lib.cmdb.ci_type import CITypeAttributeManager
from functools import wraps
from flask import current_app
from flask_login import login_user

logger = logging.getLogger(__name__)

# 测试用户配置
TEST_USER_CONFIGS = {
    'admin': {
        'username': 'admin',
        'session_data': {
            'acl': {
                'parentRoles': ['acl_admin', 'CMDB_ADMIN'],
                'username': 'admin',
                'uid': 1
            }
        }
    }
}

# 全局计数器字典
_unique_counters = {
    "0": 0,  # INT类型计数器
    "1": 0,  # FLOAT类型计数器
    "2": set()  # TEXT类型已使用的uuid集合
}

def with_request_context(f):
    """确保函数在请求上下文中执行的装饰器"""
    @wraps(f)
    def wrapper(*args, **kwargs):
        from api.models.acl import User
        from flask import current_app, has_request_context
        from flask import session
        
        if has_request_context():
            # 如果已经在请求上下文中，直接执行
            logger.info("已经在请求上下文中，直接执行")
            return f(*args, **kwargs)

        logger.info("不在请求上下文中，需要创建请求上下文")
        # 获取应用实例
        app = current_app._get_current_object() if current_app else None
        if app is None:
            from api.app import create_app
            app = create_app()
            
        # 创建请求上下文并设置用户会话
        with app.test_request_context():
            # 设置测试用户和权限
            user_config = TEST_USER_CONFIGS.get('admin')
            user = User.query.filter_by(username=user_config['username']).first()
            login_user(user)
            session.update(user_config['session_data'])
            logger.info(f"设置用户会话: {dict(session)}")
            return f(*args, **kwargs)
    return wrapper

def init_attributes(num=1, value_type: Optional[str] = None, unique: bool = False) -> List[Attribute]:
    """初始化指定数量的属性
    
    Args:
        num: 需要生成的属性数量
        value_type: 属性值类型, 可选 "0"(INT), "1"(FLOAT), "2"(TEXT), 默认随机选择
        unique: 是否生成主键字段
        
    Returns:
        List[Attribute]: 生成的属性列表
    """
    attrs = []
    # 用于确保名称不重复
    used_names = set()
    
    # 如果是主键字段，使用特定的命名模式
    if unique:
        for i in range(num):
            current_value_type = value_type or str(random.randint(0, 2))
            # 生成主键字段名称
            if current_value_type == "0":
                name = f"unique_int_{i+1:04d}"
                alias = "整数主键"
            elif current_value_type == "1":
                name = f"unique_float_{i+1:04d}"
                alias = "浮点主键"
            else:
                name = f"unique_text_{i+1:04d}"
                alias = "文本主键"
                
            attrs.append(Attribute.create(
                name=name,
                alias=alias,
                value_type=current_value_type
            ))
        return attrs
    
    # 非主键字段的原有逻辑
    metric_prefixes = [
        'cpu', 'memory', 'disk', 'network', 'io', 
        'process', 'thread', 'connection', 'request', 'response',
        'cache', 'queue', 'buffer', 'heap', 'stack'
    ]
    
    metric_suffixes = [
        'usage', 'utilization', 'count', 'rate', 'latency',
        'throughput', 'bandwidth', 'capacity', 'load', 'time'
    ]

    for i in range(num):
        # 如果未指定value_type,则随机选择一个
        current_value_type = value_type or str(random.randint(0, 2))
        
        # 生成不重复的名称
        while True:
            prefix = random.choice(metric_prefixes)
            suffix = random.choice(metric_suffixes)
            name = f"{prefix}_{suffix}"
            if name not in used_names:
                used_names.add(name)
                break
                
        # 根据不同的value_type生成对应的alias
        if current_value_type == "0":  # INT
            alias = random.choice([
                "进程数量", "线程数", "连接数", "请求数",
                "队列长度", "缓存命中数", "错误计数", "重试次数"
            ])
        elif current_value_type == "1":  # FLOAT
            alias = random.choice([
                "使用率", "负载率", "响应时间", "吞吐量",
                "命中率", "丢包率", "延迟", "可用性"
            ])
        else:  # TEXT
            alias = random.choice([
                "服务状态", "运行模式", "部署环境", "错误类型",
                "日志级别", "协议类型", "调度策略", "监控指标"
            ])
            
        attrs.append(Attribute.create(
            name=name,
            alias=alias,
            value_type=current_value_type
        ))
        
    return attrs

def init_ci_types(num=1) -> List[CIType]:
    """初始化指定数量的CI类型"""
    fake = Faker(['zh_CN'])
    attrs = init_attributes(num, unique=True)
    
    used_names = set()
    ci_types = []
    
    for i in range(num):
        while True:
            name = fake.word(ext_word_list=[
                'server', 'database', 'cache', 'gateway', 'service',
                'cluster', 'container', 'instance', 'node', 'pod',
                'volume', 'network', 'middleware', 'application', 'component'
            ]).lower()
            name = f"{name}_{fake.random_int(1000, 9999)}"
            if name not in used_names:
                used_names.add(name)
                break
        
        alias = random.choice([
            f"{fake.word(ext_word_list=['核心', '边缘', '容器', '虚拟', '物理'])}服务器",
            f"{fake.word(ext_word_list=['主', '从', '分布式', '集群', '缓存'])}数据库",
            f"{fake.word(ext_word_list=['API', '负载均衡', '反向代理', '安全', 'VPN'])}网关",
        ])
        
        ci_type = CIType.create(
            name=name,
            alias=alias,
            unique_id=attrs[i].id
        )
        
        CITypeAttribute.create(
            type_id=ci_type.id,
            attr_id=attrs[i].id,
        )
        
        ci_types.append(ci_type)

    return ci_types

def init_attribute_groups(num=1) -> List[CITypeAttributeGroup]:
    """初始化指定数量的属性组
    
    Args:
        num: 需要生成的属性组数量
        
    Returns:
        List[CITypeAttributeGroup]: 生成的属性组列表
    """
    logger.info(f"开始初始化 {num} 个属性组")
    
    # 创建CI类型
    ci_types = init_ci_types(num)
    
    # 用于生成组名称
    group_prefixes = [
        '基本', '系统', '业务', '监控', '性能',
        '安全', '网络', '存储', '配置', '运维'
    ]
    
    group_suffixes = [
        '信息', '属性', '指标', '参数', '配置',
        '数据', '状态', '设置', '详情', '统计'
    ]
    
    groups = []
    used_names = set()
    
    for i in range(num):
        # 生成不重复的组名
        while True:
            prefix = random.choice(group_prefixes)
            suffix = random.choice(group_suffixes)
            name = f"{prefix}{suffix}"
            if name not in used_names:
                used_names.add(name)
                break
        
        # 创建属性组
        group = CITypeAttributeGroup.create(
            name=name,
            type_id=ci_types[i].id,
            order=i * 100  # 使用递增的顺序
        )
        logger.info(f"创建属性组: {name}, type_id={ci_types[i].id}, order={i * 100}")
        
        groups.append(group)
    
    logger.info(f"属性组初始化完成，共创建 {len(groups)} 个")
    return groups

def fake_attr_value(attr_dict: dict) -> dict:
    """生成属性值
    
    Args:
        attr_dict: 属性定义字典
        
    Returns:
        dict: 属性名和生成的值的映射
    """
    global _unique_counters
    fake = Faker()
    attr_type = attr_dict["value_type"]
    attr_name = attr_dict["name"]
    
    # 检查是否是主键字段
    is_unique_field = attr_name.startswith('unique_')
    if is_unique_field:
        if attr_type == "0":  # INT
            # 整数主键使用全局计数器自增
            _unique_counters["0"] += 1
            value = _unique_counters["0"]
        elif attr_type == "1":  # FLOAT
            # 浮点主键使用全局计数器自增
            _unique_counters["1"] += 1
            value = float(_unique_counters["1"]) + 0.001
        else:  # TEXT
            # 文本主键使用uuid并确保不重复
            while True:
                unique_id = uuid.uuid4().hex
                if unique_id not in _unique_counters["2"]:
                    _unique_counters["2"].add(unique_id)
                    value = f"unique_{unique_id}"
                    break
        
        return {attr_name: value}
    
    # 非主键字段的原有逻辑
    name_parts = attr_name.lower().split('_')
    metric_type = name_parts[0] if name_parts else ''
    
    if attr_type == "0":  # INT
        if 'count' in name_parts:
            # 计数类指标，生成较小的整数
            value = fake.random_int(0, 1000)
        elif any(x in name_parts for x in ['process', 'thread', 'connection']):
            # 进程/线程/连接数，生成中等大小的整数
            value = fake.random_int(1, 5000)
        elif any(x in name_parts for x in ['memory', 'disk', 'cache']):
            # 容量类指标，生成较大的整数
            value = fake.random_int(1024, 1024*1024*10)
        else:
            # 其他整数指标
            value = fake.random_int(0, 10000)
            
    elif attr_type == "1":  # FLOAT
        if any(x in name_parts for x in ['usage', 'utilization', 'rate']):
            # 使用率/利用率，生成0-100之间的浮点数
            value = round(fake.pyfloat(min_value=0, max_value=100, right_digits=2), 2)
        elif any(x in name_parts for x in ['latency', 'time']):
            # 延迟/时间，生成0-1000之间的浮点数
            value = round(fake.pyfloat(min_value=0, max_value=1000, right_digits=3), 3)
        elif 'load' in name_parts:
            # 负载，生成0-10之间的浮点数
            value = round(fake.pyfloat(min_value=0, max_value=10, right_digits=2), 2)
        else:
            # 其他浮点数指标
            value = round(fake.pyfloat(min_value=0, max_value=1000, right_digits=2), 2)
            
    else:  # TEXT
        if 'status' in name_parts:
            # 状态类型
            value = fake.random_element([
                'running', 'stopped', 'pending', 'error', 'warning',
                'starting', 'stopping', 'degraded', 'unknown'
            ])
        elif any(x in name_parts for x in ['type', 'level']):
            # 类型/级别
            value = f"{metric_type}_{fake.random_int(1, 5)}"
        elif 'mode' in name_parts:
            # 模式
            value = fake.random_element([
                'normal', 'maintenance', 'debug', 'test', 'production',
                'development', 'staging', 'backup'
            ])
        elif 'environment' in name_parts:
            # 环境
            value = fake.random_element([
                'prod', 'pre', 'test', 'dev', 'uat', 'sit', 'dr'
            ])
        else:
            # 其他文本，使用uuid确保唯一性
            value = f"{metric_type}_{uuid.uuid4().hex[:8]}"

    return {attr_name: value}

@with_request_context
def init_ci(num=1, auth_user=None):
    """初始化CI实例
    
    Args:
        num: 需要创建的CI实例数量
        auth_user: 认证用户函数，由fixture提供
        
    Returns:
        list: CI实例列表
    """
    from flask import current_app
    
    current_app.logger.info(f"开始初始化 {num} 个CI实例")
    
    # 如果提供了auth_user，使用它进行认证
    if auth_user:
        user = auth_user()
        if not user:
            raise RuntimeError("用户认证失败，无法初始化CI实例")
        current_app.logger.info(f"使用认证用户: {user.username}")
    
    ci_type = init_ci_types(1)[0]
    current_app.logger.info(f"创建CI类型完成: {ci_type.name}")
    
    attrs = CITypeAttributeManager.get_attributes_by_type_id(ci_type.id)
    manager = CIManager()
    result = []

    for i in range(num):
        current_app.logger.info(f"创建第 {i+1} 个CI实例")
        ci_id = manager.add(ci_type.name, **fake_attr_value(attrs[0]))
        result.append(manager.get_ci_by_id_from_db(ci_id[0]))

    current_app.logger.info(f"CI实例初始化完成，共 {len(result)} 个")
    return result

def reset_unique_counters():
    """重置全局计数器"""
    global _unique_counters
    _unique_counters = {
        "0": 0,
        "1": 0,
        "2": set()
    }
