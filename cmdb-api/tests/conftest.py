"""
测试主配置文件
"""
import logging
from datetime import datetime
import pytest

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 导入所有fixtures
from .fixtures.app import app, app_context, request_context
from .fixtures.database import database, db_session
from .fixtures.celery import celery_worker
from .fixtures.auth import auth_user

def pytest_configure(config):
    """配置pytest运行环境"""
    if not hasattr(config, '_metadata'):
        config._metadata = {}

    config._metadata['项目名称'] = 'CMDB'
    config._metadata['测试环境'] = 'New Test Framework'
    config._metadata['测试时间'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    # 注册自定义标记
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration test"
    )

def pytest_html_report_title(report):
    """自定义HTML报告标题"""
    report.title = "CMDB新框架测试报告"
