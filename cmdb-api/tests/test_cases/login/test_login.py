"""
登录相关测试用例
"""
import logging
import pytest
from flask.testing import FlaskClient
from api.models.cmdb import (
    CIType, Attribute, CITypeAttribute, CITypeAttributeGroup,
    CITypeAttributeGroupItem
)
from tests.fixtures.database import db_session
from tests.fixtures.app import app, client
import pytest
from datetime import datetime
import base64
from Crypto.Cipher import AES
import hashlib
from api.lib.perm.acl.user import UserCRUD


class TestLoginHelper:
    @staticmethod
    def get_gmt_hour():
        """获取当前GMT时间(小时)"""
        now = datetime.utcnow()
        return now.strftime("%a, %d %b %Y %H")

    @staticmethod
    def generate_key(time_str):
        """生成加密密钥"""
        salt = "cmdb_salt_2024"
        key = hashlib.sha256((time_str + salt).encode()).hexdigest()[:32]
        return key.encode()

    @staticmethod
    def encrypt_password(password, time_str):
        """加密密码 - 与前端保持一致"""
        key = TestLoginHelper.generate_key(time_str)
        cipher = AES.new(key, AES.MODE_ECB)
        # 确保数据长度是16的倍数
        data = password.encode()
        if len(data) % 16 != 0:
            padding_length = 16 - (len(data) % 16)
            data += bytes([padding_length]) * padding_length
        encrypted = cipher.encrypt(data)
        return base64.b64encode(encrypted).decode()

def test_login_success(client: FlaskClient, db_session):
    # 准备测试数据
    test_user = UserCRUD.add(
        username="test_user",
        email="<EMAIL>",
        password="test123",
        nickname="Test User",
        account_type="0",
        add_from='common'
    )
    db_session.commit()

    # 获取当前时间并加密密码
    time_str = TestLoginHelper.get_gmt_hour()
    encrypted_password = TestLoginHelper.encrypt_password("test123", time_str)

    # 测试新的加密登录
    resp = client.post("/api/v1/acl/login", json={
        "username": "test_user",
        "password": encrypted_password,
        "timestamp": time_str
    })
    
    print("Response:", resp.data)
    assert resp.status_code == 200
    assert "token" in resp.json
    assert resp.json["username"] == "test_user"

def test_login_invalid_timestamp(client: FlaskClient, db_session):
    # 测试无效的时间戳
    resp = client.post("/api/v1/acl/login", json={
        "username": "test_user",
        "password": "encrypted_password",
        "timestamp": "Invalid timestamp"
    })
    
    assert resp.status_code == 401
    assert "Invalid timestamp" in str(resp.data)

def test_login_expired_timestamp(client: FlaskClient, db_session):
    # 测试过期的时间戳
    expired_time = "Mon, 01 Jan 2020 00"
    encrypted_password = TestLoginHelper.encrypt_password("test123", expired_time)
    
    resp = client.post("/api/v1/acl/login", json={
        "username": "test_user",
        "password": encrypted_password,
        "timestamp": expired_time
    })
    
    assert resp.status_code == 401
    assert "Invalid timestamp" in str(resp.data)

def test_login_invalid_password(client: FlaskClient, db_session):
    # 准备测试数据
    test_user = UserCRUD.add(
        username="test_user2",
        email="<EMAIL>",
        password="test123",
        nickname="Test User 2",
        account_type="0",
        add_from='common'
    )
    db_session.commit()

    # 使用错误的密码进行加密
    time_str = TestLoginHelper.get_gmt_hour()
    encrypted_password = TestLoginHelper.encrypt_password("wrong_password", time_str)

    resp = client.post("/api/v1/acl/login", json={
        "username": "test_user2",
        "password": encrypted_password,
        "timestamp": time_str
    })
    
    assert resp.status_code == 401

def test_login_missing_timestamp(client: FlaskClient, db_session):
    # 测试缺少时间戳参数
    resp = client.post("/api/v1/acl/login", json={
        "username": "test_user",
        "password": "encrypted_password"
    })
    
    assert resp.status_code == 400

def test_login_user_not_exist(client: FlaskClient, db_session):
    # 获取当前时间并加密密码
    time_str = TestLoginHelper.get_gmt_hour()
    encrypted_password = TestLoginHelper.encrypt_password("password", time_str)
    
    # 测试不存在的用户
    resp = client.post("/api/v1/acl/login", json={
        "username": "not_exist_user",
        "password": encrypted_password,
        "timestamp": time_str
    })

    assert resp.status_code == 401

def test_login_empty_params(client: FlaskClient, db_session):
    # 测试参数为空
    resp = client.post("/api/v1/acl/login", json={})
    assert resp.status_code == 400

    resp = client.post("/api/v1/acl/login", json={
        "username": "",
        "password": ""
    })
    assert resp.status_code == 400

@pytest.mark.skip(reason="To be confirmed before testing")
def test_login_with_email(client: FlaskClient, db_session):
    # 准备测试数据
    test_user = UserCRUD.add(
        username="test_user3",
        email="<EMAIL>", 
        password="test123",
        nickname="Test User 3",
        account_type="0",
        add_from='common'
    )
    db_session.commit()

    # 使用email登录
    resp = client.post("/api/v1/acl/login", json={
        "username": "<EMAIL>",
        "password": "test123"
    })

    assert resp.status_code == 200
    assert "token" in resp.json

@pytest.mark.skip(reason="Need LDAP server configuration")
def test_login_with_ldap(client: FlaskClient, db_session):
    # 测试LDAP认证
    resp = client.post("/api/v1/acl/login", json={
        "username": "ldap_user",
        "password": "ldap_pass",
        "auth_with_ldap": True
    })
    
    assert resp.status_code == 200