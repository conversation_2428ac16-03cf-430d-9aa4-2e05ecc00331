"""
Search类测试用例

测试cmdb-api/api/lib/cmdb/search/ci/db/search.py中的Search类
"""
import logging
from flask_login import current_user

from api.lib.cmdb.search.ci.db.search import Search
from api.lib.cmdb.const import Ret<PERSON>ey, ValueTypeEnum

# 导入聚合类型枚举
from api.lib.cmdb.const import AggregationTypeEnum

from api.lib.cmdb.ci import CIManager
from api.lib.cmdb.cache import AttributeCache
from api.lib.cmdb.ci_type import CITypeAttributeManager
from tests.utils.helpers import init_ci_types, init_attributes, with_request_context

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

class TestSearch:
    """Search类测试"""

    @with_request_context
    def test_search_init(self, db_session):
        """测试Search类初始化"""
        logger.info("开始测试Search类初始化")

        # 测试默认参数初始化
        search = Search()
        assert search.orig_query is None
        assert search.fl == []
        assert search.excludes == []
        assert search.facet_field is None
        assert search.page == 1
        assert search.ret_key == RetKey.NAME
        assert search.count == 1
        assert search.sort is None
        assert search.ci_ids == []
        assert search.raw_ci_ids == []
        assert search.query_sql == ""
        assert search.type_id_list == []
        assert search.only_type_query is False
        assert search.parent_node_perm_passed is False
        assert search.use_id_filter is False
        assert search.use_ci_filter is True
        assert search.only_ids is False
        assert search.multi_type_has_ci_filter is False

        # 测试自定义参数初始化
        search = Search(
            query="_type:server",
            fl=["name", "ip"],
            facet_field=["status"],
            page=2,
            ret_key=RetKey.ALIAS,
            count=10,
            sort="-name",
            ci_ids=[1, 2, 3],
            excludes=["password"],
            parent_node_perm_passed=True,
            use_id_filter=True,
            use_ci_filter=False,
            only_ids=True
        )

        assert search.orig_query == "_type:server"
        assert search.fl == ["name", "ip"]
        assert search.excludes == ["password"]
        assert search.facet_field == ["status"]
        assert search.page == 2
        assert search.ret_key == RetKey.ALIAS
        assert search.count == 10
        assert search.sort == "-name"
        assert search.ci_ids == [1, 2, 3]
        assert search.raw_ci_ids == [1, 2, 3]
        assert search.parent_node_perm_passed is True
        assert search.use_id_filter is True
        assert search.use_ci_filter is False
        assert search.only_ids is True

        logger.info("Search类初始化测试通过")

    @with_request_context
    def test_operator_proc(self, db_session):
        """测试_operator_proc方法"""
        logger.info("开始测试_operator_proc方法")

        # 测试默认操作符
        operator, key = Search._operator_proc("name")
        assert operator == "&"
        assert key == "name"

        # 测试+操作符
        operator, key = Search._operator_proc("+name")
        assert operator == "&"
        assert key == "name"

        # 测试-~操作符
        operator, key = Search._operator_proc("-~name")
        assert operator == "|~"
        assert key == "name"

        # 测试-操作符
        operator, key = Search._operator_proc("-name")
        assert operator == "|"
        assert key == "name"

        # 测试~操作符
        operator, key = Search._operator_proc("~name")
        assert operator == "~"
        assert key == "name"

        logger.info("_operator_proc方法测试通过")

    @with_request_context
    def test_search_by_type(self, db_session, auth_user):
        """测试按类型搜索CI实例"""
        logger.info("开始测试按类型搜索CI实例")

        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 创建测试数据
        ci_type = init_ci_types(1)[0]
        logger.info(f"创建CI类型: {ci_type.name}, ID: {ci_type.id}")

        # 创建属性
        attr = init_attributes(1, value_type=ValueTypeEnum.TEXT)[0]
        logger.info(f"创建属性: {attr.name}, ID: {attr.id}")

        # 关联属性到CI类型
        from api.lib.cmdb.ci_type import CITypeAttributeManager
        CITypeAttributeManager.add(ci_type.id, [attr.id])
        logger.info(f"属性 {attr.name} 已关联到CI类型 {ci_type.name}")

        # 创建CI实例
        ci_manager = CIManager()
        # 获取CI类型的唯一键属性
        from api.lib.cmdb.cache import AttributeCache
        unique_attr = AttributeCache.get(ci_type.unique_id)
        logger.info(f"CI类型唯一键属性: {unique_attr.name}")

        # 构建包含唯一键的CI数据
        ci_data = {
            attr.name: "test_value",
            unique_attr.name: 1001  # 为唯一键提供值
        }
        ci_id = ci_manager.add(ci_type.name, **ci_data)[0]
        logger.info(f"创建CI实例: ID={ci_id}")

        # 执行搜索
        search = Search(query=f"_type:{ci_type.name}")
        result, counter, total, page, numfound, facet = search.search()

        # 打印搜索结果以便调试
        logger.info(f"搜索结果: numfound={numfound}, total={total}, counter={counter}")
        logger.info(f"搜索返回的CI实例数量: {len(result)}")
        for idx, ci in enumerate(result):
            logger.info(f"CI[{idx}]: 所有字段={ci.keys()}")
            logger.info(f"CI[{idx}]: 详细信息={ci}")

        # 验证搜索结果
        assert numfound >= 1
        assert total >= 1
        assert ci_type.name in counter
        assert counter[ci_type.name] >= 1
        assert page == 1

        # 验证返回的CI实例
        found = False
        for ci in result:
            # 使用字符串比较，因为返回的ID可能是字符串
            if str(ci.get("_id")) == str(ci_id):
                found = True
                assert ci.get("ci_type") == ci_type.name
                assert ci.get(attr.name) == "test_value"
                break

        assert found, f"未找到创建的CI实例 ID={ci_id}"
        logger.info("按类型搜索CI实例测试通过")

    @with_request_context
    def test_search_by_id(self, db_session, auth_user):
        """测试按ID搜索CI实例"""
        logger.info("开始测试按ID搜索CI实例")

        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 创建测试数据
        ci_type = init_ci_types(1)[0]
        logger.info(f"创建CI类型: {ci_type.name}, ID: {ci_type.id}")

        # 创建属性
        attr = init_attributes(1, value_type=ValueTypeEnum.TEXT)[0]
        logger.info(f"创建属性: {attr.name}, ID: {attr.id}")

        # 关联属性到CI类型
        from api.lib.cmdb.ci_type import CITypeAttributeManager
        CITypeAttributeManager.add(ci_type.id, [attr.id])
        logger.info(f"属性 {attr.name} 已关联到CI类型 {ci_type.name}")

        # 获取CI类型的唯一键属性
        from api.lib.cmdb.cache import AttributeCache
        unique_attr = AttributeCache.get(ci_type.unique_id)
        logger.info(f"CI类型唯一键属性: {unique_attr.name}")

        # 创建CI实例
        ci_manager = CIManager()
        ci_data = {
            attr.name: "test_value_id_search",
            unique_attr.name: 2001  # 为唯一键提供值
        }
        ci_id = ci_manager.add(ci_type.name, **ci_data)[0]
        logger.info(f"创建CI实例: ID={ci_id}")

        # 执行按ID搜索
        search = Search(query=f"_id:{ci_id}")
        result, counter, total, page, numfound, facet = search.search()

        # 打印搜索结果以便调试
        logger.info(f"按ID搜索结果: numfound={numfound}, total={total}")

        # 验证搜索结果
        assert numfound == 1
        assert total == 1
        assert len(result) == 1

        # 验证返回的CI实例
        ci = result[0]
        assert str(ci.get("_id")) == str(ci_id)
        assert ci.get("ci_type") == ci_type.name
        assert ci.get(attr.name) == "test_value_id_search"

        logger.info("按ID搜索CI实例测试通过")

    @with_request_context
    def test_search_with_attribute_value(self, db_session, auth_user):
        """测试按属性值搜索CI实例"""
        logger.info("开始测试按属性值搜索CI实例")

        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 创建测试数据
        ci_type = init_ci_types(1)[0]
        logger.info(f"创建CI类型: {ci_type.name}, ID: {ci_type.id}")

        # 创建属性
        attr = init_attributes(1, value_type=ValueTypeEnum.TEXT)[0]
        logger.info(f"创建属性: {attr.name}, ID: {attr.id}")

        # 关联属性到CI类型
        from api.lib.cmdb.ci_type import CITypeAttributeManager
        CITypeAttributeManager.add(ci_type.id, [attr.id])
        logger.info(f"属性 {attr.name} 已关联到CI类型 {ci_type.name}")

        # 获取CI类型的唯一键属性
        from api.lib.cmdb.cache import AttributeCache
        unique_attr = AttributeCache.get(ci_type.unique_id)
        logger.info(f"CI类型唯一键属性: {unique_attr.name}")

        # 创建CI实例
        ci_manager = CIManager()
        attr_value = "unique_test_value_for_search"
        ci_data = {
            attr.name: attr_value,
            unique_attr.name: 3001  # 为唯一键提供值
        }
        ci_id = ci_manager.add(ci_type.name, **ci_data)[0]
        logger.info(f"创建CI实例: ID={ci_id}")

        # 执行按属性值搜索 - 先只按类型搜索
        search = Search(query=f"_type:{ci_type.name}")
        result, counter, total, page, numfound, facet = search.search()

        # 打印搜索结果以便调试
        logger.info(f"按属性值搜索结果: numfound={numfound}, total={total}")

        # 验证搜索结果
        assert numfound >= 1
        assert total >= 1

        # 验证返回的CI实例
        found = False
        for ci in result:
            if str(ci.get("_id")) == str(ci_id):
                found = True
                assert ci.get("ci_type") == ci_type.name
                assert ci.get(attr.name) == attr_value
                break

        assert found, f"未找到创建的CI实例 ID={ci_id}"
        logger.info("按属性值搜索CI实例测试通过")

    @with_request_context
    def test_search_with_pagination(self, db_session, auth_user):
        """测试搜索分页功能"""
        logger.info("开始测试搜索分页功能")

        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 创建测试数据
        ci_type = init_ci_types(1)[0]
        logger.info(f"创建CI类型: {ci_type.name}, ID: {ci_type.id}")

        # 创建属性
        attr = init_attributes(1, value_type=ValueTypeEnum.TEXT)[0]
        logger.info(f"创建属性: {attr.name}, ID: {attr.id}")

        # 关联属性到CI类型
        from api.lib.cmdb.ci_type import CITypeAttributeManager
        CITypeAttributeManager.add(ci_type.id, [attr.id])
        logger.info(f"属性 {attr.name} 已关联到CI类型 {ci_type.name}")

        # 获取CI类型的唯一键属性
        from api.lib.cmdb.cache import AttributeCache
        unique_attr = AttributeCache.get(ci_type.unique_id)
        logger.info(f"CI类型唯一键属性: {unique_attr.name}")

        # 创建多个CI实例
        ci_manager = CIManager()
        ci_ids = []

        for i in range(5):
            ci_data = {
                attr.name: f"pagination_test_value_{i}",
                unique_attr.name: 4001 + i  # 为唯一键提供值
            }
            ci_id = ci_manager.add(ci_type.name, **ci_data)[0]
            ci_ids.append(ci_id)
            logger.info(f"创建CI实例 {i+1}: ID={ci_id}")

        # 执行分页搜索 - 第1页，每页2条
        search1 = Search(query=f"_type:{ci_type.name}", page=1, count=2)
        result1, counter1, total1, page1, numfound1, facet1 = search1.search()

        # 执行分页搜索 - 第2页，每页2条
        search2 = Search(query=f"_type:{ci_type.name}", page=2, count=2)
        result2, counter2, total2, page2, numfound2, facet2 = search2.search()

        # 打印搜索结果以便调试
        logger.info(f"分页搜索结果1: numfound={numfound1}, total={total1}, page={page1}, count={len(result1)}")
        logger.info(f"分页搜索结果2: numfound={numfound2}, total={total2}, page={page2}, count={len(result2)}")

        # 验证搜索结果
        assert numfound1 >= 5  # numfound是总记录数
        assert total1 >= 2     # total是当前页记录数
        assert page1 == 1
        assert len(result1) == 2

        assert numfound2 >= 5  # numfound是总记录数
        assert total2 >= 2     # total是当前页记录数
        assert page2 == 2
        assert len(result2) == 2

        # 验证两页结果不同
        page1_ids = [str(ci.get("_id")) for ci in result1]
        page2_ids = [str(ci.get("_id")) for ci in result2]

        # 确保两页没有重复的ID
        assert not set(page1_ids).intersection(set(page2_ids))

        logger.info("搜索分页功能测试通过")

    @with_request_context
    def test_search_with_sorting(self, db_session, auth_user):
        """测试搜索排序功能"""
        logger.info("开始测试搜索排序功能")

        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 创建测试数据
        ci_type = init_ci_types(1)[0]
        logger.info(f"创建CI类型: {ci_type.name}, ID: {ci_type.id}")

        # 创建属性 - 使用数值类型以便排序
        attr = init_attributes(1, value_type=ValueTypeEnum.INT)[0]
        logger.info(f"创建属性: {attr.name}, ID: {attr.id}")

        # 关联属性到CI类型
        from api.lib.cmdb.ci_type import CITypeAttributeManager
        CITypeAttributeManager.add(ci_type.id, [attr.id])
        logger.info(f"属性 {attr.name} 已关联到CI类型 {ci_type.name}")

        # 获取CI类型的唯一键属性
        from api.lib.cmdb.cache import AttributeCache
        unique_attr = AttributeCache.get(ci_type.unique_id)
        logger.info(f"CI类型唯一键属性: {unique_attr.name}")

        # 创建多个CI实例，使用不同的数值
        ci_manager = CIManager()
        ci_ids = []
        values = [30, 10, 50, 20, 40]

        for i, value in enumerate(values):
            ci_data = {
                attr.name: value,
                unique_attr.name: 5001 + i  # 为唯一键提供值
            }
            ci_id = ci_manager.add(ci_type.name, **ci_data)[0]
            ci_ids.append(ci_id)
            logger.info(f"创建CI实例 {i+1}: ID={ci_id}, {attr.name}={value}")

        # 执行升序排序搜索
        search_asc = Search(query=f"_type:{ci_type.name}", sort=f"+{attr.name}")
        result_asc, counter_asc, total_asc, page_asc, numfound_asc, facet_asc = search_asc.search()

        # 执行降序排序搜索
        search_desc = Search(query=f"_type:{ci_type.name}", sort=f"-{attr.name}")
        result_desc, counter_desc, total_desc, page_desc, numfound_desc, facet_desc = search_desc.search()

        # 打印搜索结果以便调试
        logger.info(f"升序排序搜索结果: numfound={numfound_asc}, total={total_asc}")
        logger.info(f"降序排序搜索结果: numfound={numfound_desc}, total={total_desc}")

        # 提取排序后的值
        sorted_values_asc = []
        for ci in result_asc:
            if attr.name in ci:
                try:
                    sorted_values_asc.append(int(ci[attr.name]))
                except (ValueError, TypeError):
                    pass

        sorted_values_desc = []
        for ci in result_desc:
            if attr.name in ci:
                try:
                    sorted_values_desc.append(int(ci[attr.name]))
                except (ValueError, TypeError):
                    pass

        logger.info(f"升序排序值: {sorted_values_asc}")
        logger.info(f"降序排序值: {sorted_values_desc}")

        # 验证排序结果
        # 检查升序排序是否正确
        for i in range(len(sorted_values_asc) - 1):
            assert sorted_values_asc[i] <= sorted_values_asc[i + 1]

        # 检查降序排序是否正确
        for i in range(len(sorted_values_desc) - 1):
            assert sorted_values_desc[i] >= sorted_values_desc[i + 1]

        logger.info("搜索排序功能测试通过")

    @with_request_context
    def test_search_with_field_filter(self, db_session, auth_user):
        """测试搜索字段过滤功能"""
        logger.info("开始测试搜索字段过滤功能")

        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 创建测试数据
        ci_type = init_ci_types(1)[0]
        logger.info(f"创建CI类型: {ci_type.name}, ID: {ci_type.id}")

        # 创建两个属性
        attr1 = init_attributes(1, value_type=ValueTypeEnum.TEXT)[0]
        attr2 = init_attributes(1, value_type=ValueTypeEnum.TEXT)[0]
        logger.info(f"创建属性1: {attr1.name}, ID: {attr1.id}")
        logger.info(f"创建属性2: {attr2.name}, ID: {attr2.id}")

        # 关联属性到CI类型
        from api.lib.cmdb.ci_type import CITypeAttributeManager
        CITypeAttributeManager.add(ci_type.id, [attr1.id, attr2.id])
        logger.info(f"属性 {attr1.name} 和 {attr2.name} 已关联到CI类型 {ci_type.name}")

        # 获取CI类型的唯一键属性
        from api.lib.cmdb.cache import AttributeCache
        unique_attr = AttributeCache.get(ci_type.unique_id)
        logger.info(f"CI类型唯一键属性: {unique_attr.name}")

        # 创建CI实例
        ci_manager = CIManager()
        ci_data = {
            attr1.name: "filter_test_value_1",
            attr2.name: "filter_test_value_2",
            unique_attr.name: 6001  # 为唯一键提供值
        }
        ci_id = ci_manager.add(ci_type.name, **ci_data)[0]
        logger.info(f"创建CI实例: ID={ci_id}")

        # 执行搜索，只返回attr1字段
        search = Search(query=f"_type:{ci_type.name}", fl=[attr1.name])
        result, counter, total, page, numfound, facet = search.search()

        # 打印搜索结果以便调试
        logger.info(f"字段过滤搜索结果: numfound={numfound}, total={total}")

        # 验证搜索结果
        assert numfound >= 1
        assert total >= 1

        # 验证返回的CI实例
        found = False
        for ci in result:
            if str(ci.get("_id")) == str(ci_id):
                found = True
                # 验证attr1字段存在
                assert attr1.name in ci
                assert ci.get(attr1.name) == "filter_test_value_1"
                # 验证attr2字段不存在或为None
                assert attr2.name not in ci or ci.get(attr2.name) is None
                break

        assert found, f"未找到创建的CI实例 ID={ci_id}"
        logger.info("搜索字段过滤功能测试通过")

    @with_request_context
    def test_search_with_facet_field(self, db_session, auth_user):
        """测试搜索facet_field参数功能"""
        logger.info("开始测试搜索facet_field参数功能")
        db_session  # 使用db_session以避免未使用警告

        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 创建测试数据
        ci_type = init_ci_types(1)[0]
        logger.info(f"创建CI类型: {ci_type.name}, ID: {ci_type.id}")

        # 创建两个属性：一个文本型，一个数值型
        text_attr = init_attributes(1, value_type=ValueTypeEnum.TEXT)[0]
        int_attr = init_attributes(1, value_type=ValueTypeEnum.INT)[0]
        logger.info(f"创建文本属性: {text_attr.name}, ID: {text_attr.id}")
        logger.info(f"创建数值属性: {int_attr.name}, ID: {int_attr.id}")

        # 关联属性到CI类型
        CITypeAttributeManager.add(ci_type.id, [text_attr.id, int_attr.id])
        logger.info(f"属性 {text_attr.name} 和 {int_attr.name} 已关联到CI类型 {ci_type.name}")

        # 获取CI类型的唯一键属性
        unique_attr = AttributeCache.get(ci_type.unique_id)
        logger.info(f"CI类型唯一键属性: {unique_attr.name}")

        # 创建多个CI实例，使用不同的属性值
        ci_manager = CIManager()
        ci_ids = []

        # 创建具有相同文本值但不同数值的CI实例
        text_values = ["category_A", "category_A", "category_B", "category_B", "category_C"]
        int_values = [10, 20, 30, 40, 50]

        for i in range(5):
            ci_data = {
                text_attr.name: text_values[i],
                int_attr.name: int_values[i],
                unique_attr.name: 7001 + i  # 为唯一键提供值
            }
            ci_id = ci_manager.add(ci_type.name, **ci_data)[0]
            ci_ids.append(ci_id)
            logger.info(f"创建CI实例 {i+1}: ID={ci_id}, {text_attr.name}={text_values[i]}, {int_attr.name}={int_values[i]}")

        # 执行带有facet_field参数的搜索 - 按文本属性分组
        search_text = Search(
            query=f"_type:{ci_type.name}",
            facet_field=[text_attr.name]
        )
        _, _, total_text, _, numfound_text, facet_text = search_text.search()

        # 打印搜索结果以便调试
        logger.info(f"文本属性facet搜索结果: numfound={numfound_text}, total={total_text}")
        logger.info(f"文本属性facet结果: {facet_text}")

        # 验证文本属性的facet结果
        assert text_attr.name in facet_text

        # 构建预期的facet结果
        expected_facet_text = {
            "category_A": 2,  # 2个CI实例有category_A值
            "category_B": 2,  # 2个CI实例有category_B值
            "category_C": 1   # 1个CI实例有category_C值
        }

        # 验证facet结果是否符合预期
        for item in facet_text[text_attr.name]:
            value, count, field_name = item
            assert field_name == text_attr.name
            assert value in expected_facet_text
            assert count == expected_facet_text[value]

        # 执行带有facet_field参数的搜索 - 按数值属性分组
        search_int = Search(
            query=f"_type:{ci_type.name}",
            facet_field=[int_attr.name]
        )
        _, _, total_int, _, numfound_int, facet_int = search_int.search()

        # 打印搜索结果以便调试
        logger.info(f"数值属性facet搜索结果: numfound={numfound_int}, total={total_int}")
        logger.info(f"数值属性facet结果: {facet_int}")

        # 验证数值属性的facet结果
        assert int_attr.name in facet_int

        # 构建预期的facet结果
        expected_facet_int = {
            10: 1,  # 1个CI实例有值10
            20: 1,  # 1个CI实例有值20
            30: 1,  # 1个CI实例有值30
            40: 1,  # 1个CI实例有值40
            50: 1   # 1个CI实例有值50
        }

        # 验证facet结果是否符合预期
        for item in facet_int[int_attr.name]:
            value, count, field_name = item
            assert field_name == int_attr.name
            assert int(value) in expected_facet_int
            assert count == expected_facet_int[int(value)]

        # 执行带有多个facet_field参数的搜索
        search_multi = Search(
            query=f"_type:{ci_type.name}",
            facet_field=[text_attr.name, int_attr.name]
        )
        _, _, total_multi, _, numfound_multi, facet_multi = search_multi.search()

        # 打印搜索结果以便调试
        logger.info(f"多属性facet搜索结果: numfound={numfound_multi}, total={total_multi}")
        logger.info(f"多属性facet结果: {facet_multi}")

        # 验证多属性的facet结果
        assert text_attr.name in facet_multi
        assert int_attr.name in facet_multi

        # 验证文本属性的facet结果
        for item in facet_multi[text_attr.name]:
            value, count, field_name = item
            assert field_name == text_attr.name
            assert value in expected_facet_text
            assert count == expected_facet_text[value]

        # 验证数值属性的facet结果
        for item in facet_multi[int_attr.name]:
            value, count, field_name = item
            assert field_name == int_attr.name
            assert int(value) in expected_facet_int
            assert count == expected_facet_int[int(value)]

        # 测试带有查询条件的facet
        search_filtered = Search(
            query=f"_type:{ci_type.name},+{text_attr.name}:category_A",
            facet_field=[int_attr.name]
        )
        _, _, total_filtered, _, numfound_filtered, facet_filtered = search_filtered.search()

        # 打印搜索结果以便调试
        logger.info(f"带条件的facet搜索结果: numfound={numfound_filtered}, total={total_filtered}")
        logger.info(f"带条件的facet结果: {facet_filtered}")

        # 验证带条件的facet结果
        assert int_attr.name in facet_filtered

        # 构建预期的facet结果 - 只有category_A的两个值
        expected_facet_filtered = {
            10: 1,  # category_A的第一个实例
            20: 1   # category_A的第二个实例
        }

        # 验证facet结果是否符合预期
        for item in facet_filtered[int_attr.name]:
            value, count, field_name = item
            assert field_name == int_attr.name
            assert int(value) in expected_facet_filtered
            assert count == expected_facet_filtered[int(value)]

        logger.info("搜索facet_field参数功能测试通过")

class TestSearchAggregation:
    """Search类聚合功能测试"""

    @with_request_context
    def test_facet_build_with_single_field_count(self, db_session, auth_user):
        """测试_facet_build方法的单字段COUNT聚合功能"""
        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 创建测试数据
        ci_type = init_ci_types(1)[0]

        # 创建一个数值型属性
        int_attr = init_attributes(1, value_type=ValueTypeEnum.INT)[0]

        # 关联属性到CI类型
        CITypeAttributeManager.add(ci_type.id, [int_attr.id])

        # 获取CI类型的唯一键属性
        unique_attr = AttributeCache.get(ci_type.unique_id)

        # 创建多个CI实例，使用不同的属性值
        ci_manager = CIManager()
        int_values = [10, 20, 30, 40, 50]

        for i in range(5):
            ci_data = {
                int_attr.name: int_values[i],
                unique_attr.name: 7001 + i  # 为唯一键提供值
            }
            ci_manager.add(ci_type.name, **ci_data)

        # 执行带有单个facet_field参数和COUNT聚合的搜索
        search_count = Search(
            query=f"_type:{ci_type.name}",
            facet_field=[int_attr.name],
            aggregation=AggregationTypeEnum.COUNT
        )
        _, _, _, _, _, facet_count = search_count.search()

        # 验证COUNT聚合的facet结果
        assert "count" in facet_count
        assert facet_count["count"] == 5  # 应该返回总计数

    @with_request_context
    def test_facet_build_with_single_field_sum(self, db_session, auth_user):
        """测试_facet_build方法的单字段SUM聚合功能"""
        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 创建测试数据
        ci_type = init_ci_types(1)[0]

        # 创建一个数值型属性
        int_attr = init_attributes(1, value_type=ValueTypeEnum.INT)[0]

        # 关联属性到CI类型
        CITypeAttributeManager.add(ci_type.id, [int_attr.id])

        # 获取CI类型的唯一键属性
        unique_attr = AttributeCache.get(ci_type.unique_id)

        # 创建多个CI实例，使用不同的属性值
        ci_manager = CIManager()
        int_values = [10, 20, 30, 40, 50]

        for i in range(5):
            ci_data = {
                int_attr.name: int_values[i],
                unique_attr.name: 8001 + i  # 为唯一键提供值
            }
            ci_manager.add(ci_type.name, **ci_data)

        # 执行带有单个facet_field参数和SUM聚合的搜索
        search_sum = Search(
            query=f"_type:{ci_type.name}",
            facet_field=[int_attr.name],
            aggregation=AggregationTypeEnum.SUM
        )
        _, _, _, _, _, facet_sum = search_sum.search()

        # 验证SUM聚合的facet结果
        assert "sum" in facet_sum
        assert facet_sum["sum"] == 150  # 10+20+30+40+50=150

    @with_request_context
    def test_facet_build_with_single_field_avg(self, db_session, auth_user):
        """测试_facet_build方法的单字段AVG聚合功能"""
        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 创建测试数据
        ci_type = init_ci_types(1)[0]

        # 创建一个数值型属性
        int_attr = init_attributes(1, value_type=ValueTypeEnum.INT)[0]

        # 关联属性到CI类型
        CITypeAttributeManager.add(ci_type.id, [int_attr.id])

        # 获取CI类型的唯一键属性
        unique_attr = AttributeCache.get(ci_type.unique_id)

        # 创建多个CI实例，使用不同的属性值
        ci_manager = CIManager()
        int_values = [10, 20, 30, 40, 50]

        for i in range(5):
            ci_data = {
                int_attr.name: int_values[i],
                unique_attr.name: 9001 + i  # 为唯一键提供值
            }
            ci_manager.add(ci_type.name, **ci_data)

        # 执行带有单个facet_field参数和AVG聚合的搜索
        search_avg = Search(
            query=f"_type:{ci_type.name}",
            facet_field=[int_attr.name],
            aggregation=AggregationTypeEnum.AVG
        )
        _, _, _, _, _, facet_avg = search_avg.search()
        logger.info(f"AVG聚合结果: {facet_avg}")

        # 验证AVG聚合的facet结果
        assert "avg" in facet_avg
        assert facet_avg["avg"] == 30  # (10+20+30+40+50)/5=30

    @with_request_context
    def test_facet_build_with_single_field_max_min(self, db_session, auth_user):
        """测试_facet_build方法的单字段MAX和MIN聚合功能"""
        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 创建测试数据
        ci_type = init_ci_types(1)[0]

        # 创建一个数值型属性
        int_attr = init_attributes(1, value_type=ValueTypeEnum.INT)[0]

        # 关联属性到CI类型
        CITypeAttributeManager.add(ci_type.id, [int_attr.id])

        # 获取CI类型的唯一键属性
        unique_attr = AttributeCache.get(ci_type.unique_id)

        # 创建多个CI实例，使用不同的属性值
        ci_manager = CIManager()
        int_values = [10, 20, 30, 40, 50]

        for i in range(5):
            ci_data = {
                int_attr.name: int_values[i],
                unique_attr.name: 10001 + i  # 为唯一键提供值
            }
            ci_manager.add(ci_type.name, **ci_data)

        # 执行带有单个facet_field参数和MAX聚合的搜索
        search_max = Search(
            query=f"_type:{ci_type.name}",
            facet_field=[int_attr.name],
            aggregation=AggregationTypeEnum.MAX
        )
        _, _, _, _, _, facet_max = search_max.search()

        # 验证MAX聚合的facet结果
        assert "max" in facet_max
        assert facet_max["max"] == 50  # max(10,20,30,40,50)=50

        # 执行带有单个facet_field参数和MIN聚合的搜索
        search_min = Search(
            query=f"_type:{ci_type.name}",
            facet_field=[int_attr.name],
            aggregation=AggregationTypeEnum.MIN
        )
        _, _, _, _, _, facet_min = search_min.search()

        # 验证MIN聚合的facet结果
        assert "min" in facet_min
        assert facet_min["min"] == 10  # min(10,20,30,40,50)=10

    @with_request_context
    def test_facet_build_with_two_fields(self, db_session, auth_user):
        """测试_facet_build方法的两个字段聚合功能（一级分组）"""
        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 创建测试数据
        ci_type = init_ci_types(1)[0]

        # 创建两个属性：一个文本型，一个数值型
        text_attr = init_attributes(1, value_type=ValueTypeEnum.TEXT)[0]
        int_attr = init_attributes(1, value_type=ValueTypeEnum.INT)[0]

        # 关联属性到CI类型
        CITypeAttributeManager.add(ci_type.id, [text_attr.id, int_attr.id])

        # 获取CI类型的唯一键属性
        unique_attr = AttributeCache.get(ci_type.unique_id)

        # 创建多个CI实例，使用不同的属性值
        ci_manager = CIManager()

        # 创建具有相同文本值但不同数值的CI实例
        text_values = ["category_A", "category_A", "category_B", "category_B", "category_C"]
        int_values = [10, 20, 30, 40, 50]

        for i in range(5):
            ci_data = {
                text_attr.name: text_values[i],
                int_attr.name: int_values[i],
                unique_attr.name: 11001 + i  # 为唯一键提供值
            }
            ci_manager.add(ci_type.name, **ci_data)

        # 执行带有两个facet_field参数和SUM聚合的搜索
        # 第一个字段为分组字段，第二个字段为数值字段
        search_grouped_sum = Search(
            query=f"_type:{ci_type.name}",
            facet_field=[text_attr.name, int_attr.name],
            aggregation=AggregationTypeEnum.SUM
        )
        _, _, _, _, _, facet_grouped_sum = search_grouped_sum.search()
        logger.info(f"SUM聚合结果: {facet_grouped_sum}")
        # 验证按文本属性分组的SUM聚合的facet结果
        assert "sum" in facet_grouped_sum

        # 构建预期的facet结果
        expected_facet_grouped_sum = [
            ["category_A", 30],  # 10 + 20 = 30
            ["category_B", 70],  # 30 + 40 = 70
            ["category_C", 50]   # 50
        ]

        # 验证facet结果是否符合预期
        assert len(facet_grouped_sum["sum"]) == 3
        for group_result in facet_grouped_sum["sum"]:
            assert len(group_result) == 2
            group_value, sum_value = group_result

            # 查找预期结果中匹配的组
            expected_group = None
            for expected in expected_facet_grouped_sum:
                if expected[0] == group_value:
                    expected_group = expected
                    break

            assert expected_group is not None
            assert sum_value == expected_group[1]

        # 执行带有两个facet_field参数和AVG聚合的搜索
        search_grouped_avg = Search(
            query=f"_type:{ci_type.name}",
            facet_field=[text_attr.name, int_attr.name],
            aggregation=AggregationTypeEnum.AVG
        )
        _, _, _, _, _, facet_grouped_avg = search_grouped_avg.search()

        # 验证按文本属性分组的AVG聚合的facet结果
        assert "avg" in facet_grouped_avg

        # 构建预期的facet结果
        expected_facet_grouped_avg = [
            ["category_A", 15],  # (10 + 20) / 2 = 15
            ["category_B", 35],  # (30 + 40) / 2 = 35
            ["category_C", 50]   # 50 / 1 = 50
        ]

        # 验证facet结果是否符合预期
        assert len(facet_grouped_avg["avg"]) == 3
        for group_result in facet_grouped_avg["avg"]:
            assert len(group_result) == 2
            group_value, avg_value = group_result

            # 查找预期结果中匹配的组
            expected_group = None
            for expected in expected_facet_grouped_avg:
                if expected[0] == group_value:
                    expected_group = expected
                    break

            assert expected_group is not None
            assert avg_value == expected_group[1]

    @with_request_context
    def test_facet_build_with_three_fields(self, db_session, auth_user):
        """测试_facet_build方法的三个字段聚合功能（二级分组）"""
        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 创建测试数据
        ci_type = init_ci_types(1)[0]

        # 创建三个属性：两个文本型，一个数值型
        text_attr1 = init_attributes(1, value_type=ValueTypeEnum.TEXT)[0]
        text_attr2 = init_attributes(1, value_type=ValueTypeEnum.TEXT)[0]
        int_attr = init_attributes(1, value_type=ValueTypeEnum.INT)[0]

        # 关联属性到CI类型
        CITypeAttributeManager.add(ci_type.id, [text_attr1.id, text_attr2.id, int_attr.id])

        # 获取CI类型的唯一键属性
        unique_attr = AttributeCache.get(ci_type.unique_id)

        # 创建多个CI实例，使用不同的属性值
        ci_manager = CIManager()

        # 创建具有不同文本值和数值的CI实例
        # 第一级分组：OS类型（Linux/Windows）
        # 第二级分组：OS版本（CentOS/Ubuntu/Windows 10/Windows Server）
        # 数值：内存大小
        test_data = [
            ("Linux", "CentOS", 8),
            ("Linux", "CentOS", 16),
            ("Linux", "Ubuntu", 4),
            ("Linux", "Ubuntu", 8),
            ("Windows", "Windows 10", 16),
            ("Windows", "Windows 10", 32),
            ("Windows", "Windows Server", 64),
            ("Windows", "Windows Server", 128)
        ]

        for i, (os_type, os_version, memory) in enumerate(test_data):
            ci_data = {
                text_attr1.name: os_type,
                text_attr2.name: os_version,
                int_attr.name: memory,
                unique_attr.name: 12001 + i  # 为唯一键提供值
            }
            ci_manager.add(ci_type.name, **ci_data)

        # 执行带有三个facet_field参数和SUM聚合的搜索
        # 前两个字段为分组字段，第三个字段为数值字段
        search_grouped_sum = Search(
            query=f"_type:{ci_type.name}",
            facet_field=[text_attr1.name, text_attr2.name, int_attr.name],
            aggregation=AggregationTypeEnum.SUM
        )
        _, _, _, _, _, facet_grouped_sum = search_grouped_sum.search()

        # 验证二级分组的SUM聚合的facet结果
        assert "sum" in facet_grouped_sum

        # 构建预期的facet结果
        expected_facet_grouped_sum = [
            ["Linux", "CentOS", 24],      # 8 + 16 = 24
            ["Linux", "Ubuntu", 12],      # 4 + 8 = 12
            ["Windows", "Windows 10", 48],      # 16 + 32 = 48
            ["Windows", "Windows Server", 192]  # 64 + 128 = 192
        ]

        # 验证facet结果是否符合预期
        assert len(facet_grouped_sum["sum"]) == 4
        for group_result in facet_grouped_sum["sum"]:
            assert len(group_result) == 3
            group1_value, group2_value, sum_value = group_result

            # 查找预期结果中匹配的组
            expected_group = None
            for expected in expected_facet_grouped_sum:
                if expected[0] == group1_value and expected[1] == group2_value:
                    expected_group = expected
                    break

            assert expected_group is not None
            assert sum_value == expected_group[2]

        # 执行带有三个facet_field参数和AVG聚合的搜索
        search_grouped_avg = Search(
            query=f"_type:{ci_type.name}",
            facet_field=[text_attr1.name, text_attr2.name, int_attr.name],
            aggregation=AggregationTypeEnum.AVG
        )
        _, _, _, _, _, facet_grouped_avg = search_grouped_avg.search()

        # 验证二级分组的AVG聚合的facet结果
        assert "avg" in facet_grouped_avg

        # 构建预期的facet结果
        expected_facet_grouped_avg = [
            ["Linux", "CentOS", 12],      # (8 + 16) / 2 = 12
            ["Linux", "Ubuntu", 6],       # (4 + 8) / 2 = 6
            ["Windows", "Windows 10", 24],      # (16 + 32) / 2 = 24
            ["Windows", "Windows Server", 96]   # (64 + 128) / 2 = 96
        ]

        # 验证facet结果是否符合预期
        assert len(facet_grouped_avg["avg"]) == 4
        for group_result in facet_grouped_avg["avg"]:
            assert len(group_result) == 3
            group1_value, group2_value, avg_value = group_result

            # 查找预期结果中匹配的组
            expected_group = None
            for expected in expected_facet_grouped_avg:
                if expected[0] == group1_value and expected[1] == group2_value:
                    expected_group = expected
                    break

            assert expected_group is not None
            assert avg_value == expected_group[2]

        logger.info("_facet_build方法的三个字段聚合功能测试通过")

    @with_request_context
    def test_facet_build_with_max_min_aggregation(self, db_session, auth_user):
        """测试_facet_build方法的MAX和MIN聚合功能（分组）"""
        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 创建测试数据
        ci_type = init_ci_types(1)[0]

        # 创建两个属性：一个文本型，一个数值型
        text_attr = init_attributes(1, value_type=ValueTypeEnum.TEXT)[0]
        int_attr = init_attributes(1, value_type=ValueTypeEnum.INT)[0]

        # 关联属性到CI类型
        CITypeAttributeManager.add(ci_type.id, [text_attr.id, int_attr.id])

        # 获取CI类型的唯一键属性
        unique_attr = AttributeCache.get(ci_type.unique_id)

        # 创建多个CI实例，使用不同的属性值
        ci_manager = CIManager()

        # 创建具有相同文本值但不同数值的CI实例
        test_data = [
            ("category_A", 10),
            ("category_A", 20),
            ("category_A", 30),
            ("category_B", 5),
            ("category_B", 15),
            ("category_B", 25),
            ("category_C", 40),
            ("category_C", 50),
            ("category_C", 60)
        ]

        for i, (category, value) in enumerate(test_data):
            ci_data = {
                text_attr.name: category,
                int_attr.name: value,
                unique_attr.name: 13001 + i  # 为唯一键提供值
            }
            ci_manager.add(ci_type.name, **ci_data)

        # 执行带有两个facet_field参数和MAX聚合的搜索
        search_grouped_max = Search(
            query=f"_type:{ci_type.name}",
            facet_field=[text_attr.name, int_attr.name],
            aggregation=AggregationTypeEnum.MAX
        )
        _, _, _, _, _, facet_grouped_max = search_grouped_max.search()

        # 验证按文本属性分组的MAX聚合的facet结果
        assert "max" in facet_grouped_max

        # 构建预期的facet结果
        expected_facet_grouped_max = [
            ["category_A", 30],  # max(10, 20, 30) = 30
            ["category_B", 25],  # max(5, 15, 25) = 25
            ["category_C", 60]   # max(40, 50, 60) = 60
        ]

        # 验证facet结果是否符合预期
        assert len(facet_grouped_max["max"]) == 3
        for group_result in facet_grouped_max["max"]:
            assert len(group_result) == 2
            group_value, max_value = group_result

            # 查找预期结果中匹配的组
            expected_group = None
            for expected in expected_facet_grouped_max:
                if expected[0] == group_value:
                    expected_group = expected
                    break

            assert expected_group is not None
            assert max_value == expected_group[1]

        # 执行带有两个facet_field参数和MIN聚合的搜索
        search_grouped_min = Search(
            query=f"_type:{ci_type.name}",
            facet_field=[text_attr.name, int_attr.name],
            aggregation=AggregationTypeEnum.MIN
        )
        _, _, _, _, _, facet_grouped_min = search_grouped_min.search()

        # 验证按文本属性分组的MIN聚合的facet结果
        assert "min" in facet_grouped_min

        # 构建预期的facet结果
        expected_facet_grouped_min = [
            ["category_A", 10],  # min(10, 20, 30) = 10
            ["category_B", 5],   # min(5, 15, 25) = 5
            ["category_C", 40]   # min(40, 50, 60) = 40
        ]

        # 验证facet结果是否符合预期
        assert len(facet_grouped_min["min"]) == 3
        for group_result in facet_grouped_min["min"]:
            assert len(group_result) == 2
            group_value, min_value = group_result

            # 查找预期结果中匹配的组
            expected_group = None
            for expected in expected_facet_grouped_min:
                if expected[0] == group_value:
                    expected_group = expected
                    break

            assert expected_group is not None
            assert min_value == expected_group[1]

        logger.info("_facet_build方法的MAX和MIN聚合功能测试通过")

    @with_request_context
    def test_facet_build_with_float_values(self, db_session, auth_user):
        """测试_facet_build方法对浮点数值的聚合功能"""
        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 创建测试数据
        ci_type = init_ci_types(1)[0]

        # 创建一个浮点型属性
        float_attr = init_attributes(1, value_type=ValueTypeEnum.FLOAT)[0]

        # 关联属性到CI类型
        CITypeAttributeManager.add(ci_type.id, [float_attr.id])

        # 获取CI类型的唯一键属性
        unique_attr = AttributeCache.get(ci_type.unique_id)

        # 创建多个CI实例，使用不同的浮点数值
        ci_manager = CIManager()
        float_values = [1.5, 2.5, 3.5, 4.5, 5.5]

        for i in range(5):
            ci_data = {
                float_attr.name: float_values[i],
                unique_attr.name: 14001 + i  # 为唯一键提供值
            }
            ci_manager.add(ci_type.name, **ci_data)

        # 执行带有单个facet_field参数和SUM聚合的搜索
        search_sum = Search(
            query=f"_type:{ci_type.name}",
            facet_field=[float_attr.name],
            aggregation=AggregationTypeEnum.SUM
        )
        _, _, _, _, _, facet_sum = search_sum.search()

        # 验证SUM聚合的facet结果
        assert "sum" in facet_sum
        assert abs(facet_sum["sum"] - 17.5) < 0.001  # 1.5+2.5+3.5+4.5+5.5=17.5

        # 执行带有单个facet_field参数和AVG聚合的搜索
        search_avg = Search(
            query=f"_type:{ci_type.name}",
            facet_field=[float_attr.name],
            aggregation=AggregationTypeEnum.AVG
        )
        _, _, _, _, _, facet_avg = search_avg.search()

        # 验证AVG聚合的facet结果
        assert "avg" in facet_avg
        assert abs(facet_avg["avg"] - 3.5) < 0.001  # (1.5+2.5+3.5+4.5+5.5)/5=3.5

        logger.info("_facet_build方法对浮点数值的聚合功能测试通过")

    @with_request_context
    def test_facet_build_with_filter_condition(self, db_session, auth_user):
        """测试带有过滤条件的_facet_build方法聚合功能"""
        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 创建测试数据
        ci_type = init_ci_types(1)[0]

        # 创建两个属性：一个文本型，一个数值型
        text_attr = init_attributes(1, value_type=ValueTypeEnum.TEXT)[0]
        int_attr = init_attributes(1, value_type=ValueTypeEnum.INT)[0]

        # 关联属性到CI类型
        CITypeAttributeManager.add(ci_type.id, [text_attr.id, int_attr.id])

        # 获取CI类型的唯一键属性
        unique_attr = AttributeCache.get(ci_type.unique_id)

        # 创建多个CI实例，使用不同的属性值
        ci_manager = CIManager()

        # 创建具有不同文本值和数值的CI实例
        test_data = [
            ("category_A", 10),
            ("category_A", 20),
            ("category_B", 30),
            ("category_B", 40),
            ("category_C", 50),
            ("category_C", 60)
        ]

        for i, (category, value) in enumerate(test_data):
            ci_data = {
                text_attr.name: category,
                int_attr.name: value,
                unique_attr.name: 15001 + i  # 为唯一键提供值
            }
            ci_manager.add(ci_type.name, **ci_data)

        # 执行带有过滤条件和SUM聚合的搜索
        # 只选择category_A和category_B的记录
        search_filtered_sum = Search(
            query=f"_type:{ci_type.name},+{text_attr.name}:(category_A;category_B)",
            facet_field=[text_attr.name, int_attr.name],
            aggregation=AggregationTypeEnum.SUM
        )
        _, _, _, _, _, facet_filtered_sum = search_filtered_sum.search()

        # 验证带过滤条件的SUM聚合的facet结果
        assert "sum" in facet_filtered_sum

        # 构建预期的facet结果
        expected_facet_filtered_sum = [
            ["category_A", 30],  # 10 + 20 = 30
            ["category_B", 70]   # 30 + 40 = 70
        ]

        # 验证facet结果是否符合预期
        assert len(facet_filtered_sum["sum"]) == 2
        for group_result in facet_filtered_sum["sum"]:
            assert len(group_result) == 2
            group_value, sum_value = group_result

            # 查找预期结果中匹配的组
            expected_group = None
            for expected in expected_facet_filtered_sum:
                if expected[0] == group_value:
                    expected_group = expected
                    break

            assert expected_group is not None
            assert sum_value == expected_group[1]

        # 执行带有过滤条件和AVG聚合的搜索
        # 只选择数值大于30的记录
        search_filtered_avg = Search(
            query=f"_type:{ci_type.name},+{int_attr.name}:>30",
            facet_field=[text_attr.name, int_attr.name],
            aggregation=AggregationTypeEnum.AVG
        )
        _, _, _, _, _, facet_filtered_avg = search_filtered_avg.search()

        # 验证带过滤条件的AVG聚合的facet结果
        assert "avg" in facet_filtered_avg

        # 构建预期的facet结果
        expected_facet_filtered_avg = [
            ["category_B", 40],  # (40) / 1 = 40 (只有一个值大于30)
            ["category_C", 55]   # (50 + 60) / 2 = 55
        ]

        # 验证facet结果是否符合预期
        assert len(facet_filtered_avg["avg"]) == 2
        for group_result in facet_filtered_avg["avg"]:
            assert len(group_result) == 2
            group_value, avg_value = group_result

            # 查找预期结果中匹配的组
            expected_group = None
            for expected in expected_facet_filtered_avg:
                if expected[0] == group_value:
                    expected_group = expected
                    break

            assert expected_group is not None
            assert avg_value == expected_group[1]

        logger.info("带有过滤条件的_facet_build方法聚合功能测试通过")

    @with_request_context
    def test_facet_build_with_empty_result(self, db_session, auth_user):
        """测试_facet_build方法在没有匹配结果时的行为"""
        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 创建测试数据
        ci_type = init_ci_types(1)[0]

        # 创建一个数值型属性
        int_attr = init_attributes(1, value_type=ValueTypeEnum.INT)[0]

        # 关联属性到CI类型
        CITypeAttributeManager.add(ci_type.id, [int_attr.id])

        # 获取CI类型的唯一键属性
        unique_attr = AttributeCache.get(ci_type.unique_id)

        # 创建一个CI实例
        ci_manager = CIManager()
        ci_data = {
            int_attr.name: 100,
            unique_attr.name: 16001  # 为唯一键提供值
        }
        ci_manager.add(ci_type.name, **ci_data)

        # 执行带有不匹配任何记录的查询条件和SUM聚合的搜索
        search_empty_sum = Search(
            query=f"_type:{ci_type.name},+{int_attr.name}:>1000",
            facet_field=[int_attr.name],
            aggregation=AggregationTypeEnum.SUM
        )
        _, _, _, _, _, facet_empty_sum = search_empty_sum.search()

        # 验证没有匹配结果时的SUM聚合结果
        assert "sum" in facet_empty_sum
        assert facet_empty_sum["sum"] == 0  # 没有匹配的记录，应该返回0

        # 执行带有不匹配任何记录的查询条件和AVG聚合的搜索
        search_empty_avg = Search(
            query=f"_type:{ci_type.name},+{int_attr.name}:>1000",
            facet_field=[int_attr.name],
            aggregation=AggregationTypeEnum.AVG
        )
        _, _, _, _, _, facet_empty_avg = search_empty_avg.search()

        # 验证没有匹配结果时的AVG聚合结果
        assert "avg" in facet_empty_avg
        assert facet_empty_avg["avg"] == 0  # 没有匹配的记录，应该返回0

        logger.info("_facet_build方法在没有匹配结果时的行为测试通过")

    @with_request_context
    def test_facet_build_with_invalid_aggregation(self, db_session, auth_user):
        """测试_facet_build方法处理无效聚合类型的情况"""
        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 创建测试数据
        ci_type = init_ci_types(1)[0]

        # 创建一个数值型属性
        int_attr = init_attributes(1, value_type=ValueTypeEnum.INT)[0]

        # 关联属性到CI类型
        CITypeAttributeManager.add(ci_type.id, [int_attr.id])

        # 获取CI类型的唯一键属性
        unique_attr = AttributeCache.get(ci_type.unique_id)

        # 创建一个CI实例
        ci_manager = CIManager()
        ci_data = {
            int_attr.name: 100,
            unique_attr.name: 17001  # 为唯一键提供值
        }
        ci_manager.add(ci_type.name, **ci_data)

        # 执行带有无效聚合类型的搜索
        # 注意：这里假设Search类会处理无效的聚合类型，并默认使用COUNT
        search_invalid = Search(
            query=f"_type:{ci_type.name}",
            facet_field=[int_attr.name],
            aggregation="INVALID_TYPE"  # 无效的聚合类型
        )
        _, _, _, _, _, facet_invalid = search_invalid.search()

        # 验证无效聚合类型时的结果
        # 应该默认使用COUNT聚合或返回错误信息
        assert isinstance(facet_invalid, dict)

        logger.info("_facet_build方法处理无效聚合类型的情况测试通过")