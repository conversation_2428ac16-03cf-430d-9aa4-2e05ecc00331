"""
attribute 相关测试用例
"""
import logging
import pytest
from flask.testing import FlaskClient
from api.models.cmdb import (
    CIType, Attribute, CITypeAttribute, CITypeAttributeGroup,
    CITypeAttributeGroupItem
)
from tests.fixtures.database import db_session
from tests.fixtures.app import app, client
from tests.utils.helpers import (
    init_attributes, init_ci_types, init_attribute_groups
)


import logging

logger = logging.getLogger(__name__)


def test_create_attribute(client: FlaskClient, db_session):
    url = "/api/v0.1/attributes"
    payload = {"name": "region", "alias": "区域", "value_type": "2"}

    resp = client.post(url, json=payload)

    # check resp status code and content
    assert resp.status_code == 200
    assert resp.json["attr_id"]

    # check there is a attribute in database
    attr_id = resp.json["attr_id"]
    attr_ins = Attribute.get_by_id(attr_id)
    assert attr_ins.id == attr_id
    assert attr_ins.name == "region"
    assert attr_ins.alias == "区域"


def test_update_attribute(client: FlaskClient, db_session):
    attr_ins = init_attributes(1)[0]

    url = "/api/v0.1/attributes/" + str(attr_ins.id)
    payload = {
        "alias": "update",
        "value_type": attr_ins.value_type,
    }

    resp = client.put(url, json=payload)

    # check resp status code and content
    assert resp.status_code == 200
    assert resp.json["attr_id"] == attr_ins.id

    # check attribute updated in database
    attr_ins = Attribute.get_by_id(attr_ins.id)
    assert attr_ins.alias == "update"


def test_delete_attribute(client: FlaskClient, db_session):
    attr_ins = init_attributes(1)[0]
    url = "/api/v0.1/attributes/" + str(attr_ins.id)
    client.login()
    resp = client.delete(url)

    assert resp.status_code == 200
    # attr should be soft delete
    attr_ins = Attribute.get_by_id(attr_ins.id)
    logger.debug(f"attr_ins: {attr_ins}")  # 使用 debug 级别记录日志
    assert attr_ins is None
    # assert attr_ins.deleted is True
    # assert attr_ins.deleted_at