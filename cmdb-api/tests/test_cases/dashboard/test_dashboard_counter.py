"""
DashboardCounter测试用例

测试cmdb-api/api/lib/cmdb/dashboard_counter.py中的DashboardCounter类
包括update、sum_counter、attribute_counter和relation_counter方法
"""
import logging
import pytest
from flask_login import current_user

from api.lib.cmdb.dashboard_counter import DashboardCounter
from api.lib.cmdb.cache import AttributeCache
from api.lib.cmdb.const import ValueTypeEnum, ConstraintEnum
from api.lib.cmdb.ci import CIManager, CIRelationManager
from api.lib.cmdb.ci_type import CITypeAttributeManager, CITypeRelationManager
from api.lib.cmdb.relation_type import RelationTypeManager
from api.models.cmdb import RelationType, CITypeRelation
from tests.utils.helpers import init_ci_types, init_attributes, with_request_context

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


class TestDashboardCounter:
    """DashboardCounter类测试"""

    @with_request_context
    def test_sum_counter_basic(self, db_session, auth_user):
        """测试sum_counter方法的基本功能"""
        logger.info("开始测试sum_counter方法的基本功能")

        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 创建测试数据
        ci_type = init_ci_types(1)[0]
        logger.info(f"创建CI类型: {ci_type.name}, ID: {ci_type.id}")

        # 获取CI类型的唯一键属性
        unique_attr = AttributeCache.get(ci_type.unique_id)
        logger.info(f"CI类型唯一键属性: {unique_attr.name}")

        # 创建多个CI实例
        ci_manager = CIManager()
        ci_ids = []
        for i in range(5):  # 创建5个CI实例
            ci_data = {
                unique_attr.name: 10001 + i  # 为唯一键提供值
            }
            ci_id = ci_manager.add(ci_type.name, **ci_data)[0]
            ci_ids.append(ci_id)
            logger.info(f"创建CI实例 {i+1}: ID={ci_id}")

        # 构建custom参数
        custom = {
            'type_id': ci_type.id,
            'category': 0,
            'options': {
                'type_ids': [ci_type.id]
            }
        }

        # 调用sum_counter方法
        result = DashboardCounter.sum_counter(custom)
        logger.info(f"sum_counter结果: {result}")

        # 验证结果
        assert result is not None
        assert isinstance(result, int)
        assert result == 5  # 应该返回5个CI实例

        logger.info("sum_counter方法的基本功能测试通过")

    @with_request_context
    def test_attribute_counter_basic(self, db_session, auth_user):
        """测试attribute_counter方法的基本功能"""
        logger.info("开始测试attribute_counter方法的基本功能")

        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 创建测试数据
        ci_type = init_ci_types(1)[0]
        logger.info(f"创建CI类型: {ci_type.name}, ID: {ci_type.id}")

        # 创建属性 - 一个文本型属性用于统计
        text_attr = init_attributes(1, value_type=ValueTypeEnum.TEXT)[0]
        logger.info(f"创建文本属性: {text_attr.name}, ID: {text_attr.id}")

        # 创建数值型属性用于聚合测试
        int_attr = init_attributes(1, value_type=ValueTypeEnum.INT)[0]
        logger.info(f"创建整数属性: {int_attr.name}, ID: {int_attr.id}")

        float_attr = init_attributes(1, value_type=ValueTypeEnum.FLOAT)[0]
        logger.info(f"创建浮点数属性: {float_attr.name}, ID: {float_attr.id}")

        # 关联属性到CI类型
        CITypeAttributeManager.add(ci_type.id, [text_attr.id, int_attr.id, float_attr.id])
        logger.info(f"属性已关联到CI类型 {ci_type.name}")

        # 获取CI类型的唯一键属性
        unique_attr = AttributeCache.get(ci_type.unique_id)
        logger.info(f"CI类型唯一键属性: {unique_attr.name}")

        # 创建多个CI实例，使用不同的属性值
        ci_manager = CIManager()

        # 测试数据：文本值、整数值和浮点数值
        test_data = [
            {"text": "value_A", "int": 100, "float": 10.5},
            {"text": "value_A", "int": 200, "float": 20.5},
            {"text": "value_B", "int": 150, "float": 15.5},
            {"text": "value_B", "int": 250, "float": 25.5},
            {"text": "value_C", "int": 300, "float": 30.5}
        ]

        for i, data in enumerate(test_data):
            ci_data = {
                text_attr.name: data["text"],
                int_attr.name: data["int"],
                float_attr.name: data["float"],
                unique_attr.name: 1001 + i  # 为唯一键提供值
            }
            ci_id = ci_manager.add(ci_type.name, **ci_data)[0]
            logger.info(f"创建CI实例 {i+1}: ID={ci_id}, {text_attr.name}={data['text']}, {int_attr.name}={data['int']}, {float_attr.name}={data['float']}")

        # 1. 测试基本的COUNT聚合（默认）
        logger.info("测试COUNT聚合（默认）")
        count_custom = {
            'type_id': ci_type.id,
            'attr_id': text_attr.id,
            'category': 1,
            'options': {
                'type_ids': [ci_type.id],
                'attr_ids': [text_attr.id]
            }
        }

        # 调用attribute_counter方法
        count_result = DashboardCounter.attribute_counter(count_custom)
        logger.info(f"COUNT聚合结果: {count_result}")

        # 验证COUNT聚合结果
        assert count_result is not None

        # 检查返回值类型 - 可能是整数（总数）或字典（分组统计）
        if isinstance(count_result, dict):
            # 如果是字典，验证分组统计结果
            assert "value_A" in count_result
            assert "value_B" in count_result
            assert "value_C" in count_result
            assert count_result["value_A"] == 2  # value_A出现了2次
            assert count_result["value_B"] == 2  # value_B出现了2次
            assert count_result["value_C"] == 1  # value_C出现了1次
        else:
            # 如果是整数，验证总数
            assert isinstance(count_result, int)
            assert count_result == 5  # 总共5个CI实例

        # 2. 测试SUM聚合
        logger.info("测试SUM聚合")
        sum_custom = {
            'type_id': ci_type.id,
            'attr_id': int_attr.id,
            'category': 1,
            'options': {
                'type_ids': [ci_type.id],
                'attr_ids': [text_attr.id, int_attr.id],
                'aggregation': 'sum'
            }
        }

        # 调用attribute_counter方法
        sum_result = DashboardCounter.attribute_counter(sum_custom)
        logger.info(f"SUM聚合结果: {sum_result}")

        # 验证SUM聚合结果
        assert sum_result is not None

        # 检查返回值类型 - 可能是整数（总和）或字典（分组统计）
        if isinstance(sum_result, dict):
            # 如果是字典，验证分组统计结果
            assert "value_A" in sum_result
            assert "value_B" in sum_result
            assert "value_C" in sum_result
            assert sum_result["value_A"] == 300  # 100 + 200
            assert sum_result["value_B"] == 400  # 150 + 250
            assert sum_result["value_C"] == 300  # 300
        else:
            # 如果是整数，验证总和
            assert isinstance(sum_result, int) or isinstance(sum_result, float)
            assert sum_result == 1000  # 100 + 200 + 150 + 250 + 300

        # 3. 测试AVG聚合
        logger.info("测试AVG聚合")
        avg_custom = {
            'type_id': ci_type.id,
            'attr_id': float_attr.id,
            'category': 1,
            'options': {
                'type_ids': [ci_type.id],
                'attr_ids': [text_attr.id, float_attr.id],
                'aggregation': 'avg'
            }
        }

        # 调用attribute_counter方法
        avg_result = DashboardCounter.attribute_counter(avg_custom)
        logger.info(f"AVG聚合结果: {avg_result}")

        # 验证AVG聚合结果
        assert avg_result is not None

        # 检查返回值类型 - 可能是浮点数（平均值）或字典（分组统计）
        if isinstance(avg_result, dict):
            # 如果是字典，验证分组统计结果
            assert "value_A" in avg_result
            assert "value_B" in avg_result
            assert "value_C" in avg_result
            assert abs(avg_result["value_A"] - 15.5) < 0.1  # (10.5 + 20.5) / 2
            assert abs(avg_result["value_B"] - 20.5) < 0.1  # (15.5 + 25.5) / 2
            assert abs(avg_result["value_C"] - 30.5) < 0.1  # 30.5
        else:
            # 如果是浮点数，验证总体平均值
            assert isinstance(avg_result, float) or isinstance(avg_result, int)
            assert abs(avg_result - 20.5) < 0.1  # (10.5 + 20.5 + 15.5 + 25.5 + 30.5) / 5

        # 4. 测试MAX聚合
        logger.info("测试MAX聚合")
        max_custom = {
            'type_id': ci_type.id,
            'attr_id': int_attr.id,
            'category': 1,
            'options': {
                'type_ids': [ci_type.id],
                'attr_ids': [text_attr.id, int_attr.id],
                'aggregation': 'max'
            }
        }

        # 调用attribute_counter方法
        max_result = DashboardCounter.attribute_counter(max_custom)
        logger.info(f"MAX聚合结果: {max_result}")

        # 验证MAX聚合结果
        assert max_result is not None

        # 检查返回值类型 - 可能是整数（最大值）或字典（分组统计）
        if isinstance(max_result, dict):
            # 如果是字典，验证分组统计结果
            assert "value_A" in max_result
            assert "value_B" in max_result
            assert "value_C" in max_result
            assert max_result["value_A"] == 200  # max(100, 200)
            assert max_result["value_B"] == 250  # max(150, 250)
            assert max_result["value_C"] == 300  # 300
        else:
            # 如果是整数，验证总体最大值
            assert isinstance(max_result, int) or isinstance(max_result, float)
            assert max_result == 300  # max(100, 200, 150, 250, 300)

        # 5. 测试MIN聚合
        logger.info("测试MIN聚合")
        min_custom = {
            'type_id': ci_type.id,
            'attr_id': int_attr.id,
            'category': 1,
            'options': {
                'type_ids': [ci_type.id],
                'attr_ids': [text_attr.id, int_attr.id],
                'aggregation': 'min'
            }
        }

        # 调用attribute_counter方法
        min_result = DashboardCounter.attribute_counter(min_custom)
        logger.info(f"MIN聚合结果: {min_result}")

        # 验证MIN聚合结果
        assert min_result is not None

        # 检查返回值类型 - 可能是整数（最小值）或字典（分组统计）
        if isinstance(min_result, dict):
            # 如果是字典，验证分组统计结果
            assert "value_A" in min_result
            assert "value_B" in min_result
            assert "value_C" in min_result
            assert min_result["value_A"] == 100  # min(100, 200)
            assert min_result["value_B"] == 150  # min(150, 250)
            assert min_result["value_C"] == 300  # 300
        else:
            # 如果是整数，验证总体最小值
            assert isinstance(min_result, int) or isinstance(min_result, float)
            assert min_result == 100  # min(100, 200, 150, 250, 300)

        # 6. 测试多维度聚合（两个维度）
        logger.info("测试多维度聚合（两个维度）")
        two_dim_custom = {
            'type_id': ci_type.id,
            'category': 1,
            'options': {
                'type_ids': [ci_type.id],
                'attr_ids': [text_attr.id, int_attr.id],
                'aggregation': 'count'
            }
        }

        # 调用attribute_counter方法
        two_dim_result = DashboardCounter.attribute_counter(two_dim_custom)
        logger.info(f"两个维度聚合结果: {two_dim_result}")

        # 验证两个维度聚合结果
        assert two_dim_result is not None

        # 检查返回值类型 - 可能是整数（总数）或嵌套字典（多维度分组统计）
        if isinstance(two_dim_result, dict) and "value_A" in two_dim_result and isinstance(two_dim_result["value_A"], dict):
            # 如果是嵌套字典，验证多维度分组统计结果
            assert "value_A" in two_dim_result
            assert "value_B" in two_dim_result
            assert "value_C" in two_dim_result
            assert isinstance(two_dim_result["value_A"], dict)
            assert 100 in two_dim_result["value_A"] or "100" in two_dim_result["value_A"]
            assert 200 in two_dim_result["value_A"] or "200" in two_dim_result["value_A"]
        elif isinstance(two_dim_result, dict):
            # 如果是普通字典，验证一维分组统计结果
            assert len(two_dim_result) > 0
            logger.info(f"一维分组统计结果: {two_dim_result}")
        else:
            # 如果是整数，验证总数
            assert isinstance(two_dim_result, int)
            assert two_dim_result == 5  # 总共5个CI实例

        logger.info("attribute_counter方法的基本功能测试通过")

    @with_request_context
    def test_relation_counter_basic(self, db_session, auth_user):
        """测试relation_counter方法的基本功能"""
        logger.info("开始测试relation_counter方法的基本功能")

        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 创建两个CI类型
        ci_types = init_ci_types(2)
        parent_type = ci_types[0]
        child_type = ci_types[1]
        logger.info(f"创建父CI类型: {parent_type.name}, ID: {parent_type.id}")
        logger.info(f"创建子CI类型: {child_type.name}, ID: {child_type.id}")

        # 创建关系类型
        relation_type = RelationTypeManager.add("Contains")
        logger.info(f"创建关系类型: Contains, ID: {relation_type.id}")

        # 创建CI类型间的关系定义
        type_relation = CITypeRelationManager.add(
            parent_type.id,
            child_type.id,
            relation_type.id,
            ConstraintEnum.One2Many
        )
        logger.info(f"创建CI类型关系: {parent_type.name} -> {child_type.name}, ID: {type_relation}")

        # 获取CI类型的唯一键属性
        parent_unique_attr = AttributeCache.get(parent_type.unique_id)
        child_unique_attr = AttributeCache.get(child_type.unique_id)
        logger.info(f"父CI类型唯一键属性: {parent_unique_attr.name}")
        logger.info(f"子CI类型唯一键属性: {child_unique_attr.name}")

        # 创建父CI实例
        ci_manager = CIManager()
        parent_ci_ids = []
        for i in range(3):
            ci_data = {
                parent_unique_attr.name: 6001 + i  # 为唯一键提供值
            }
            parent_ci_id = ci_manager.add(parent_type.name, **ci_data)[0]
            parent_ci_ids.append(parent_ci_id)
            logger.info(f"创建父CI实例 {i+1}: ID={parent_ci_id}")

        # 创建子CI实例并建立关系
        child_ci_ids = []
        # 父CI 1有2个子CI
        for i in range(2):
            ci_data = {
                child_unique_attr.name: 7001 + i  # 为唯一键提供值
            }
            child_ci_id = ci_manager.add(child_type.name, **ci_data)[0]
            child_ci_ids.append(child_ci_id)
            # 创建关系
            CIRelationManager.add(parent_ci_ids[0], child_ci_id, relation_type_id=relation_type.id)
            logger.info(f"创建子CI实例: ID={child_ci_id}, 关联到父CI: {parent_ci_ids[0]}")

        # 调用relation_counter方法
        result = DashboardCounter.relation_counter(parent_type.id, 1, None, [child_type.id])
        logger.info(f"relation_counter结果: {result}")

        # 验证结果
        assert result is not None
        assert isinstance(result, dict)
        assert "summary" in result
        assert "detail" in result

        # 验证summary
        assert len(result["summary"]) > 0  # 至少包含一个父CI的唯一键值

        logger.info("relation_counter方法的基本功能测试通过")

    @with_request_context
    def test_update_method(self, db_session, auth_user):
        """测试update方法"""
        logger.info("开始测试update方法")

        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 创建测试数据
        ci_type = init_ci_types(1)[0]
        logger.info(f"创建CI类型: {ci_type.name}, ID: {ci_type.id}")

        # 获取CI类型的唯一键属性
        unique_attr = AttributeCache.get(ci_type.unique_id)
        logger.info(f"CI类型唯一键属性: {unique_attr.name}")

        # 创建多个CI实例
        ci_manager = CIManager()
        for i in range(5):  # 创建5个CI实例
            ci_data = {
                unique_attr.name: 50001 + i  # 为唯一键提供值
            }
            ci_id = ci_manager.add(ci_type.name, **ci_data)[0]
            logger.info(f"创建CI实例 {i+1}: ID={ci_id}")

        # 构建custom参数 - category=0 (sum_counter)
        custom_sum = {
            'id': 1,
            'type_id': ci_type.id,
            'category': 0,
            'options': {
                'type_ids': [ci_type.id]
            }
        }

        # 调用update方法 - 不写入缓存
        result_no_flush = DashboardCounter.update(custom_sum, flush=False)
        logger.info(f"update方法结果(flush=False): {result_no_flush}")

        # 验证结果
        assert result_no_flush is not None
        assert isinstance(result_no_flush, int)
        assert result_no_flush == 5  # 应该返回5个CI实例

        # 验证缓存中没有结果
        cache_result = DashboardCounter.get_cache()
        assert 1 not in cache_result

        # 调用update方法 - 写入缓存
        result_flush = DashboardCounter.update(custom_sum, flush=True)
        logger.info(f"update方法结果(flush=True): {result_flush}")

        # 验证结果
        assert result_flush is not None
        assert isinstance(result_flush, int)
        assert result_flush == 5  # 应该返回5个CI实例

        # 验证缓存中有结果
        cache_result = DashboardCounter.get_cache()
        assert 1 in cache_result
        assert cache_result[1] == 5

        logger.info("update方法测试通过")

    @with_request_context
    def test_process_facet_result(self, db_session, auth_user):
        """测试_process_facet_result方法"""
        logger.info("开始测试_process_facet_result方法")

        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 测试单值结果
        single_value_facet = {"count": 42}
        result = DashboardCounter._process_facet_result(single_value_facet, "count")
        logger.info(f"单值结果处理: {result}")
        assert result == 42

        # 测试二维数据结构
        two_dim_facet = {
            "count": [
                ["区域1", 10],
                ["区域2", 20],
                ["区域3", 30]
            ]
        }
        result = DashboardCounter._process_facet_result(two_dim_facet, "count")
        logger.info(f"二维数据结构处理: {result}")
        assert isinstance(result, dict)
        assert len(result) == 3
        assert result["区域1"] == 10
        assert result["区域2"] == 20
        assert result["区域3"] == 30

        # 测试三维数据结构
        three_dim_facet = {
            "count": [
                ["区域1", "云类型1", 5],
                ["区域1", "云类型2", 5],
                ["区域2", "云类型1", 10],
                ["区域2", "云类型2", 10],
                ["区域3", "云类型1", 15],
                ["区域3", "云类型2", 15]
            ]
        }
        result = DashboardCounter._process_facet_result(three_dim_facet, "count")
        logger.info(f"三维数据结构处理: {result}")
        assert isinstance(result, dict)
        assert len(result) == 3
        assert "区域1" in result
        assert "区域2" in result
        assert "区域3" in result
        assert isinstance(result["区域1"], dict)
        assert len(result["区域1"]) == 2
        assert result["区域1"]["云类型1"] == 5
        assert result["区域1"]["云类型2"] == 5
        assert result["区域2"]["云类型1"] == 10
        assert result["区域2"]["云类型2"] == 10
        assert result["区域3"]["云类型1"] == 15
        assert result["区域3"]["云类型2"] == 15

        # 测试非聚合类型键
        non_agg_facet = {"other_key": 100}
        result = DashboardCounter._process_facet_result(non_agg_facet, "count")
        logger.info(f"非聚合类型键处理: {result}")
        assert result == non_agg_facet

        # 测试空facet
        empty_facet = {}
        result = DashboardCounter._process_facet_result(empty_facet, "count")
        logger.info(f"空facet处理: {result}")
        assert result == 0

        # 测试非字典facet
        non_dict_facet = 123
        result = DashboardCounter._process_facet_result(non_dict_facet, "count")
        logger.info(f"非字典facet处理: {result}")
        assert result == non_dict_facet

        logger.info("_process_facet_result方法测试通过")
