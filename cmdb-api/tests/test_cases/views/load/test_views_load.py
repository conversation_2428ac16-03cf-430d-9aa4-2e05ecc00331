# -*- coding: utf-8 -*-
import logging
import pytest
from flask.testing import FlaskClient
from api.models.cmdb import (
    CIType, Attribute, CITypeAttribute, CITypeAttributeGroup,
    CITypeAttributeGroupItem
)
from tests.fixtures.database import db_session
from tests.fixtures.app import app, client, request_context
from api.lib.cmdb.load.const import LoadValueTypeEnum, PeriodTypeEnum
from api.models.cmdb import LoadAttribute, LoadCITypeAttribute
from tests.utils.helpers import init_ci
from api.lib.cmdb.load.load_attr import LoadAttrManager, LoadRelationsManager, LoadDataManager
import json
import time
import logging
from api.lib.cmdb.load.const import HistoryStatusEnum

logger = logging.getLogger(__name__)

class TestLoadFieldView:
    """负载字段管理视图测试"""

    def test_create_attr(self, client: FlaskClient, db_session):
        """测试创建字段"""
        url = "/api/v0.1/load/attributes"
        payload = {
            "name": "test_attr",
            "value_type": LoadValueTypeEnum.TEXT,
            "alias": "测试字段"
        }
        client.login()
        resp = client.post(url, json=payload)
        assert resp.status_code == 200
        assert resp.json["name"] == "test_attr"
        assert resp.json["value_type"] == LoadValueTypeEnum.TEXT
        assert resp.json["alias"] == "测试字段"

        # 验证数据库记录
        attr = LoadAttribute.query.filter_by(name="test_attr").first()
        assert attr is not None
        assert attr.name == "test_attr"

    def test_create_duplicate_attr(self, client: FlaskClient, db_session):
        """测试创建重复字段"""
        url = "/api/v0.1/load/attributes"
        payload = {
            "name": "duplicate_attr",
            "value_type": LoadValueTypeEnum.TEXT,
            "alias": "重复字段"
        }

        # 第一次创建
        client.login()
        resp = client.post(url, json=payload)
        assert resp.status_code == 200

        # 第二次创建同名字段
        resp = client.post(url, json=payload)
        assert resp.status_code == 400
        assert "已存在" in resp.json["message"]

    def test_get_attr_list(self, client: FlaskClient, db_session):
        """测试获取字段列表"""
        # 先创建测试数据
        url = "/api/v0.1/load/attributes"
        client.login()
        for i in range(3):
            client.post(url, json={
                "name": f"list_attr_{i}",
                "value_type": LoadValueTypeEnum.TEXT,
                "alias": f"列表字段{i}"
            })

        # 测试获取列表
        resp = client.get(url)
        assert resp.status_code == 200
        assert resp.json["total"] >= 3
        assert "attrs" in resp.json

        # 测试分页
        resp = client.get(url + "?page=1&page_size=2")
        assert resp.status_code == 200
        assert len(resp.json["attrs"]) == 2

        # 测试按名称搜索
        resp = client.get(url + "?name=list_attr_1")
        assert resp.status_code == 200
        assert len(resp.json["attrs"]) == 1
        assert resp.json["attrs"][0]["name"] == "list_attr_1"

    def test_update_attr(self, client: FlaskClient, db_session):
        """测试更新字段"""
        # 先创建一个字段
        client.login()
        create_resp = client.post("/api/v0.1/load/attributes", json={
            "name": "update_attr",
            "value_type": LoadValueTypeEnum.TEXT,
            "alias": "原始别名"
        })
        attr_id = create_resp.json["id"]

        # 测试更新
        url = f"/api/v0.1/load/attributes/{attr_id}"
        resp = client.put(url, json={"alias": "新别名"})
        assert resp.status_code == 200
        assert resp.json["alias"] == "新别名"

        # 测试更新不存在的字段
        resp = client.put("/api/v0.1/load/attributes/999999", json={"alias": "test"})
        assert resp.status_code == 400

    def test_delete_attr(self, client: FlaskClient, db_session):
        """测试删除字段"""
        # 先创建一个字段
        client.login()
        create_resp = client.post("/api/v0.1/load/attributes", json={
            "name": "delete_attr",
            "value_type": LoadValueTypeEnum.TEXT,
            "alias": "待删除字段"
        })
        attr_id = create_resp.json["id"]

        # 测试删除
        url = f"/api/v0.1/load/attributes/{attr_id}"
        resp = client.delete(url)
        assert resp.status_code == 200

        # 验证已删除
        resp = client.get(url)
        assert resp.status_code == 404


class TestLoadCITypeFieldView:
    """CI类型负载字段关联管理视图测试"""

    def test_add_type_attr(self, client: FlaskClient, db_session, auth_user):
        """测试添加CI类型字段关联"""
        # 初始化CI类型
        init_ci(1, auth_user)
        client.login()
        # 创建测试字段
        attr_resp = client.post("/api/v0.1/load/attributes", json={
            "name": "type_attr",
            "value_type": LoadValueTypeEnum.TEXT,
            "alias": "类型字段"
        })
        attr_id = attr_resp.json["id"]

        # 测试添加关联
        url = "/api/v0.1/load/relations"
        payload = {
            "type_id": 1,
            "attr_configs": [{
                "attr_id": attr_id,
                "is_required": True,
                "order": 1
            }]
        }
        client.login()
        resp = client.post(url, json=payload)
        assert resp.status_code == 200

        # 验证关联已创建
        relation = LoadCITypeAttribute.query.filter_by(
            type_id=1,
            load_attr_id=attr_id
        ).first()
        assert relation is not None
        assert relation.is_required is True
        assert relation.order == 1

    def test_batch_update_type_attrs(self, client: FlaskClient, db_session, auth_user):
        """测试批量更新CI类型字段关联"""
        # 初始化CI类型
        init_ci(1, auth_user)

        # 创建两个测试字段
        attrs = []
        client.login()
        for i in range(2):
            resp = client.post("/api/v0.1/load/attributes", json={
                "name": f"batch_attr_{i}",
                "value_type": LoadValueTypeEnum.TEXT,
                "alias": f"批量字段{i}"
            })
            attrs.append(resp.json)

        # 测试批量更新
        url = "/api/v0.1/load/relations"
        payload = {
            "type_id": 1,
            "attr_configs": [
                {"attr_id": attrs[0]["id"], "is_required": True, "order": 1},
                {"attr_id": attrs[1]["id"], "is_required": False, "order": 2}
            ]
        }
        resp = client.post(url, json=payload)
        assert resp.status_code == 200

        # 验证更新结果
        resp = client.get("/api/v0.1/load/relations/1")
        assert resp.status_code == 200
        assert len(resp.json["attrs"]) == 2

    def test_delete_type_attr(self, client: FlaskClient, db_session, auth_user):
        """测试删除CI类型字段关联"""
        # 初始化CI类型
        init_ci(1, auth_user)
        client.login()
        # 创建测试字段并关联
        attr_resp = client.post("/api/v0.1/load/attributes", json={
            "name": "delete_type_attr",
            "value_type": LoadValueTypeEnum.TEXT,
            "alias": "待删除关联字段"
        })
        attr_id = attr_resp.json["id"]

        # 添加关联
        client.post("/api/v0.1/load/relations", json={
            "type_id": 1,
            "attr_configs": [{
                "attr_id": attr_id,
                "is_required": True
            }]
        })

        # 测试删除关联
        url = "/api/v0.1/load/relations/1"
        client.login()
        resp = client.delete(url, query_string={"attr_id": attr_id})
        assert resp.status_code == 200

        # 验证关联已删除
        relation = LoadCITypeAttribute.query.filter_by(
            type_id=1,
            load_attr_id=attr_id,
            deleted=False
        ).first()
        assert relation is None


class TestLoadDataView:
    """测试负载数据管理视图"""

    @pytest.fixture(scope='function')
    def prepare_data(self, db_session, request_context, celery_worker, auth_user):
        """准备测试数据"""
        # 初始化CI类型和实例
        ci = init_ci(2, auth_user)  # 创建2个CI实例
        from flask import session
        logger.info(f"准备测试数据，session: {dict(session)}")
        type_id = ci[0]['_type']
        unique_values = [c[c['unique']] for c in ci]
        
        # 创建测试属性
        cpu_attr = LoadAttrManager.add_attr(
            name="cpu_usage",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="CPU使用率"
        )
        mem_attr = LoadAttrManager.add_attr(
            name="memory_usage",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="内存使用率",
        )
        cost_attr = LoadAttrManager.add_attr(
            name="monthly_cost",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="月度成本",
            is_monthly=True
        )
        # 添加LIST类型属性
        tags_attr = LoadAttrManager.add_attr(
            name="tags",
            value_type=LoadValueTypeEnum.LIST,
            alias="标签列表"
        )
        
        # 添加属性到CI类型
        for attr in [cpu_attr, mem_attr, cost_attr, tags_attr]:
            LoadRelationsManager.add_type_attr(type_id=type_id, attr_id=attr.id)
        
        # 导入一些初始数据
        initial_data = []
        for unique_value in unique_values:
            # 添加日常指标数据
            initial_data.extend([
                {
                    "unique_value": unique_value,
                    "2024-01-01": {
                        "cpu_usage": 75.5,
                        "memory_usage": 80.0,
                        "tags": "web,app,cache"
                    },
                    "2024-01-02": {
                        "cpu_usage": 85.5,
                        "memory_usage": 90.0,
                        "tags": "db,redis"
                    }
                }
            ])
            # 添加月度成本数据
            initial_data.append({
                "unique_value": unique_value,
                "2024-01": {
                    "monthly_cost": 1000.0
                }
            })
        
        # 使用LoadDataManager直接导入数据
        result = LoadDataManager.batch_import(
            type_id=type_id,
            data=initial_data
        )
        return {
            'type_id': type_id,
            'ci': ci,
            'attrs': {
                'cpu': cpu_attr,
                'memory': mem_attr,
                'cost': cost_attr,
                'tags': tags_attr
            }
        }

    def test_query_by_ci_ids(self, client: FlaskClient, prepare_data):
        """测试通过CI IDs查询数据"""
        
        data = prepare_data
        ci_ids = [c['_id'] for c in data['ci']]
        attr_ids = [data['attrs']['cpu'].id, data['attrs']['memory'].id]
        unique_values = [c[c['unique']] for c in data['ci']]
        
        
        # 构建查询URL
        query_params = {
            'type_id': data['type_id'],
            'ci_ids': ','.join(map(str, ci_ids)),
            'attribute_ids': ','.join(map(str, attr_ids)),
            'start_period': '2024-01-01',
            'end_period': '2024-01-02',
            'page': 1,
            'page_size': 10
        }
        
        response = client.get('/api/v0.1/load/data', query_string=query_params)
        assert response.status_code == 200
        
        result = json.loads(response.data)
        # 验证分页信息
        assert 'total' in result
        assert 'page' in result
        assert 'page_size' in result
        assert 'data' in result
        
        # 验证返回的数据结构
        result_data = result['data']
        for ci_id in unique_values:
            assert str(ci_id) in result_data
            ci_data = result_data[str(ci_id)]
            # 验证日期数据
            for date in ['2024-01-01', '2024-01-02']:
                assert date in ci_data
                day_data = ci_data[date]
                assert 'cpu_usage' in day_data
                assert 'memory_usage' in day_data

    def test_query_by_unique_values(self, client: FlaskClient, prepare_data):
        """测试通过unique_values查询数据"""
        data = prepare_data
        unique_key = data['ci'][0]['unique']
        unique_values = [str(c[unique_key]) for c in data['ci']]
        
        query_params = {
            'unique_values': ','.join(unique_values),
            'type_id': data['type_id'],
            'attribute_ids': str(data['attrs']['cpu'].id),
            'start_period': '2024-01-01',
            'end_period': '2024-01-02',
            'page': 1,
            'page_size': 10
        }
        
        response = client.get('/api/v0.1/load/data', query_string=query_params)
        assert response.status_code == 200
        
        result = json.loads(response.data)
        # 验证分页信息
        assert 'total' in result
        assert 'page' in result
        assert 'page_size' in result
        assert 'data' in result
        
        result_data = result['data']
        for unique_value in unique_values:
            assert str(unique_value) in result_data

    def test_query_without_ci_identifiers(self, client: FlaskClient, prepare_data):
        """测试在未指定ci_ids和unique_values的情况下查询数据"""
        data = prepare_data
        
        # 只使用type_id和attribute_ids查询
        query_params = {
            'type_id': data['type_id'],
            'attribute_ids': str(data['attrs']['cpu'].id),
            'start_period': '2024-01-01',
            'end_period': '2024-01-02',
            'page': 1,
            'page_size': 10
        }
        
        response = client.get('/api/v0.1/load/data', query_string=query_params)
        assert response.status_code == 200
        
        result = json.loads(response.data)
        # 验证分页信息
        assert 'total' in result
        assert 'page' in result
        assert 'page_size' in result
        assert 'data' in result
        
        # 验证返回的数据包含所有符合条件的CI数据
        result_data = result['data']
        assert len(result_data) > 0  # 应该至少包含测试数据中的CI

    def test_query_pagination(self, client: FlaskClient, prepare_data):
        """测试负载数据查询的分页功能"""
        data = prepare_data
        type_id = data['type_id']
        
        # 第一页
        query_params = {
            'type_id': type_id,
            'attribute_ids': str(data['attrs']['cpu'].id),
            'start_period': '2024-01-01',  # 指定时间范围
            'end_period': '2024-01-02',
            'page': 1,
            'page_size': 1
        }
        
        response = client.get('/api/v0.1/load/data', query_string=query_params)
        assert response.status_code == 200
        
        result = json.loads(response.data)
        assert result['page'] == 1
        assert result['page_size'] == 1
        assert len(result['data']) <= 1
        
        # 第二页
        query_params['page'] = 2
        response = client.get('/api/v0.1/load/data', query_string=query_params)
        assert response.status_code == 200
        
        result2 = json.loads(response.data)
        assert result2['page'] == 2
        assert result2['page_size'] == 1
        
        # 验证两页数据不重复 - 使用period来验证
        if len(result['data']) > 0 and len(result2['data']) > 0:
            # 获取第一页的所有时间点
            page1_periods = set()
            for ci_data in result['data'].values():
                page1_periods.update(ci_data.keys())
            
            # 获取第二页的所有时间点
            page2_periods = set()
            for ci_data in result2['data'].values():
                page2_periods.update(ci_data.keys())
            
            # 确保两页的时间点没有交集
            assert not (page1_periods & page2_periods), \
                f"Found overlapping periods between pages: {page1_periods & page2_periods}"

    def test_query_monthly_data(self, client: FlaskClient, prepare_data):
        """测试查询月度数据"""
        data = prepare_data
        ci_ids = [c['_id'] for c in data['ci']]
        unique_key = data['ci'][0]['unique']
        unique_values = [str(c[unique_key]) for c in data['ci']]
        
        query_params = {
            'type_id': data['type_id'],
            'ci_ids': ','.join(map(str, ci_ids)),
            'attribute_ids': str(data['attrs']['cost'].id),
            'start_period': '2024-01',
            'end_period': '2024-01',
            'period_type': PeriodTypeEnum.MONTHLY
        }
        
        response = client.get('/api/v0.1/load/data', query_string=query_params)
        assert response.status_code == 200
        
        result = json.loads(response.data)
        for values in unique_values:
            assert str(values) in result['data']
            assert '2024-01' in result['data'][str(values)]
            assert 'monthly_cost' in result['data'][str(values)]['2024-01']

    def test_import_data(self, client: FlaskClient, prepare_data):
        """测试导入数据"""
        data = prepare_data
        type_id = data['type_id']
        unique_value = data['ci'][0][data['ci'][0]['unique']]
        attr_ids = [data['attrs']['cpu'].id, data['attrs']['memory'].id]
        
        # 测试用例1: 使用JSON字符串格式的upload_data
        import_data = [
            {
                "unique_value": unique_value,
                "2024-01-03": {
                    "cpu_usage": 95.5,
                    "memory_usage": 88.0
                }
            }
        ]
        
        response = client.post(
            f'/api/v0.1/load/data/{type_id}',
            data={
                'upload_data': json.dumps(import_data),
                'file_name': 'test_import.json',
                'batch_size': 100
            }
        )
        assert response.status_code == 200

        
        # 验证数据已正确导入
        response = client.get(
            f'/api/v0.1/load/data',
            query_string={
                'type_id': 1,
                'attribute_ids': ','.join(map(str, attr_ids)),
                'unique_values': unique_value,
                'start_period': '2024-01-03',
                'end_period': '2024-02-01'
            }
        )
        assert response.status_code == 200
        result = json.loads(response.data)
        assert str(unique_value) in result['data']
        assert result['data'][str(unique_value)]['2024-01-03']['cpu_usage'] == 95.5
        assert result['data'][str(unique_value)]['2024-01-03']['memory_usage'] == 88.0

    def test_import_invalid_data(self, client: FlaskClient, prepare_data):
        """测试导入无效数据"""
        data = prepare_data
        type_id = data['type_id']
        unique_value = data['ci'][0][data['ci'][0]['unique']]
        
        invalid_data = [
            {
                "unique_value": unique_value,
                "2024-01-03A": {
                    "cpu_usage": 95.5
                },
                "2024-02A": {
                    "monthly_cost": 1200.0
                }
            },
            {
                "unique_value": "non-existent",
                "2024-01-03": {
                    "cpu_usage": 95.5
                },
                "2024-02": {
                    "monthly_cost": 1200.0
                }
            }
        ]

        response = client.post(
            f'/api/v0.1/load/data/{type_id}',
            data={
                'upload_data': json.dumps(invalid_data),
                'file_name': 'test_import.json',
                'batch_size': 100
            }
        )
        
        logger.info(f"导入无效数据响应: {response}")
        assert response.status_code == 200
        history_id = response.json['history_id']
        response = client.get(f'/api/v0.1/load/histories/{history_id}')
        assert response.status_code == 200
        history = response.json
        assert history['status'] == HistoryStatusEnum.FAILED
        assert history['file_name'] == 'test_import.json'
        assert history['total_count'] == 2
        assert history['success_count'] == 0
        assert history['error_count'] == 2

    def test_query_with_invalid_params(self, client: FlaskClient, prepare_data):
        """测试使用无效参数查询"""
        # 测试无效的period_type
        query_params = {
            'ci_ids': '1',
            'period_type': 'invalid'
        }
        response = client.get('/api/v0.1/load/data', query_string=query_params)
        assert response.status_code == 400
        
        # 测试同时提供ci_ids和unique_values
        query_params = {
            'ci_ids': '1',
            'unique_values': 'test',
            'attribute_ids': '1'
        }
        response = client.get('/api/v0.1/load/data', query_string=query_params)
        assert response.status_code == 404
        
        # 测试使用unique_values但未提供type_id
        query_params = {
            'unique_values': 'test',
            'attribute_ids': '1'
        }
        response = client.get('/api/v0.1/load/data', query_string=query_params)
        assert response.status_code == 404

    def test_import_empty_data(self, client, prepare_data):
        """测试导入空数据"""
        data = prepare_data
        type_id = data['type_id']
        
        response = client.post(
            f'/api/v0.1/load/data/{type_id}',
            data={'data': json.dumps([])}
        )
        assert response.status_code == 400
        

    def test_import_data_format(self, client: FlaskClient, prepare_data):
        """测试导入数据格式验证"""
        data = prepare_data
        type_id = data['type_id']
        
        # 测试非列表格式
        response = client.post(
            f'/api/v0.1/load/data/{type_id}',
            data={'data': json.dumps({"not": "a list"})}
        )
        assert response.status_code == 400

    def test_import_list_data(self, client: FlaskClient, prepare_data):
        """测试导入LIST类型数据"""
        data = prepare_data
        type_id = data['type_id']
        unique_value = data['ci'][0][data['ci'][0]['unique']]
        
        # 测试各种LIST类型数据格式
        import_data = [
            {
                "unique_value": unique_value,
                "2024-01-03": {
                    "tags": "prod,core,web"  # 逗号分隔的字符串
                },
                "2024-01-04": {
                    "tags": ["api", "db"]  # 直接使用列表
                }
            }
        ]
        
        response = client.post(
            f'/api/v0.1/load/data/{type_id}',
            data={
                'upload_data': json.dumps(import_data),
                'file_name': 'test_import.json',
                'batch_size': 100
            }
        )

        assert response.status_code == 200
        history_id = response.json['history_id']
        response = client.get(f'/api/v0.1/load/histories/{history_id}')
        assert response.status_code == 200
        history = response.json
        assert history['status'] == HistoryStatusEnum.COMPLETED
        assert history['total_count'] == 2
        assert history['success_count'] == 2
        assert history['error_count'] == 0

        # 验证导入的数据
        query_params = {
            'unique_values': str(unique_value),
            'type_id': type_id,
            'attribute_ids': str(data['attrs']['tags'].id),
            'start_period': '2024-01-03',
            'end_period': '2024-01-04'
        }
        
        response = client.get('/api/v0.1/load/data', query_string=query_params)
        assert response.status_code == 200
        
        result = json.loads(response.data)
        result_data = result['data'][str(unique_value)]
        assert result_data['2024-01-03']['tags'] == ["prod", "core", "web"]
        assert result_data['2024-01-04']['tags'] == ["api", "db"]

    def test_import_invalid_list_data(self, client: FlaskClient, prepare_data):
        """测试导入无效的LIST类型数据"""
        data = prepare_data
        type_id = data['type_id']
        unique_value = data['ci'][0][data['ci'][0]['unique']]
        
        # 测试无效数据
        invalid_data = [
            {
                "unique_value": unique_value,
                "2024-01-03": {
                    "tags": None  # 空值
                },
                "2024-01-04": {
                    "tags": ""  # 空字符串
                },
                "2024-01-05": {
                    "tags": "valid,,invalid"  # 包含空值的列表
                }
            }
        ]
        
        response = client.post(
            f'/api/v0.1/load/data/{type_id}',
            data={
                'upload_data': json.dumps(invalid_data),
                'file_name': 'test_import.json',
                'batch_size': 100
            }
        )
        assert response.status_code == 200
        history_id = response.json['history_id']
        response = client.get(f'/api/v0.1/load/histories/{history_id}')
        assert response.status_code == 200
        history = response.json
        assert history['status'] == HistoryStatusEnum.FAILED
        assert history['total_count'] == 1
        assert history['success_count'] == 0
        assert history['error_count'] == 1


class TestTimeSlotFeature:
    """时间段功能测试类"""

    @pytest.fixture(scope='function')
    def prepare_time_slot_data(self, db_session, request_context, celery_worker, auth_user):
        """准备时间段测试数据"""
        # 初始化CI类型和实例
        ci = init_ci(2, auth_user)  # 创建2个CI实例
        type_id = ci[0]['_type']
        unique_values = [c[c['unique']] for c in ci]
        
        # 创建测试属性（日度）
        cpu_attr = LoadAttrManager.add_attr(
            name="cpu_usage_time_slot",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="CPU使用率(时段)"
        )
        mem_attr = LoadAttrManager.add_attr(
            name="memory_usage_time_slot",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="内存使用率(时段)",
        )
        # 创建月度属性（不应该有时间段）
        cost_attr = LoadAttrManager.add_attr(
            name="monthly_cost_time_slot",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="月度成本(时段)",
            is_monthly=True
        )
        
        # 添加属性到CI类型
        for attr in [cpu_attr, mem_attr, cost_attr]:
            LoadRelationsManager.add_type_attr(type_id=type_id, attr_id=attr.id)
        
        return {
            'type_id': type_id,
            'ci': ci,
            'attrs': {
                'cpu': cpu_attr,
                'memory': mem_attr,
                'cost': cost_attr
            }
        }

    def test_time_slot_info_endpoint(self, client: FlaskClient, db_session, request_context):
        """测试时间段信息查询接口"""
        response = client.get('/api/v0.1/load/time_slots')
        assert response.status_code == 200
        
        result = json.loads(response.data)
        assert 'time_slots' in result
        assert 'default_slot' in result
        assert 'description' in result
        
        # 验证时间段信息
        time_slots = result['time_slots']
        assert len(time_slots) == 2
        assert any(slot['value'] == '00' and slot['label'] == '闲时' for slot in time_slots)
        assert any(slot['value'] == '08' and slot['label'] == '忙时' for slot in time_slots)
        assert result['default_slot'] == '08'

    def test_import_data_with_time_slot(self, client: FlaskClient, prepare_time_slot_data):
        """测试带时间段的数据导入"""
        data = prepare_time_slot_data
        type_id = data['type_id']
        unique_value = data['ci'][0][data['ci'][0]['unique']]
        cpu_attr_name = data['attrs']['cpu'].name
        mem_attr_name = data['attrs']['memory'].name
        
        # 测试导入忙时数据
        import_data = [
            {
                "unique_value": unique_value,
                "2024-01-01": {
                    cpu_attr_name: 85.5,
                    mem_attr_name: 90.0
                },
                "2024-01-02": {
                    cpu_attr_name: 75.5,
                    mem_attr_name: 80.0
                }
            }
        ]
        
        response = client.post(
            f'/api/v0.1/load/data/{type_id}',
            data={
                'upload_data': json.dumps(import_data),
                'time_slot': '08',  # 忙时
                'file_name': 'test_busy_time.json',
                'batch_size': 100
            }
        )
        assert response.status_code == 200
        
        result = response.json
        assert result['total'] == 1
        assert 'history_id' in result
        assert result.get('time_slot') == '08'
        assert result.get('time_slot_description') == '忙时(08:00-18:00)'
        
        # 验证导入结果
        history_id = result['history_id']
        import time
        time.sleep(2)  # 等待异步任务完成
        
        response = client.get(f'/api/v0.1/load/histories/{history_id}')
        assert response.status_code == 200
        history = response.json
        assert history['status'] == HistoryStatusEnum.COMPLETED

    def test_import_data_with_idle_time_slot(self, client: FlaskClient, prepare_time_slot_data):
        """测试导入闲时数据"""
        data = prepare_time_slot_data
        type_id = data['type_id']
        unique_value = data['ci'][0][data['ci'][0]['unique']]
        cpu_attr_name = data['attrs']['cpu'].name
        mem_attr_name = data['attrs']['memory'].name
        
        # 测试导入闲时数据
        import_data = [
            {
                "unique_value": unique_value,
                "2024-01-01": {
                    cpu_attr_name: 45.5,
                    mem_attr_name: 50.0
                }
            }
        ]
        
        response = client.post(
            f'/api/v0.1/load/data/{type_id}',
            data={
                'upload_data': json.dumps(import_data),
                'time_slot': '00',  # 闲时
                'file_name': 'test_idle_time.json'
            }
        )
        assert response.status_code == 200
        
        result = response.json
        assert result.get('time_slot') == '00'
        assert result.get('time_slot_description') == '闲时(00:00-08:00)'

    def test_import_data_with_invalid_time_slot(self, client: FlaskClient, prepare_time_slot_data):
        """测试使用无效时间段导入数据"""
        data = prepare_time_slot_data
        type_id = data['type_id']
        unique_value = data['ci'][0][data['ci'][0]['unique']]
        cpu_attr_name = data['attrs']['cpu'].name
        
        import_data = [
            {
                "unique_value": unique_value,
                "2024-01-01": {
                    cpu_attr_name: 85.5
                }
            }
        ]
        
        response = client.post(
            f'/api/v0.1/load/data/{type_id}',
            data={
                'upload_data': json.dumps(import_data),
                'time_slot': '99',  # 无效时间段
                'file_name': 'test_invalid_time_slot.json'
            }
        )
        assert response.status_code == 400
        assert '无效的时间段' in response.json['message']

    def test_query_data_with_time_slot(self, client: FlaskClient, prepare_time_slot_data):
        """测试带时间段的数据查询"""
        data = prepare_time_slot_data
        type_id = data['type_id']
        unique_value = data['ci'][0][data['ci'][0]['unique']]
        attr_ids = [data['attrs']['cpu'].id, data['attrs']['memory'].id]
        
        # 先导入忙时数据
        cpu_attr_name = data['attrs']['cpu'].name
        mem_attr_name = data['attrs']['memory'].name
        
        import_data = [
            {
                "unique_value": unique_value,
                "2024-01-01": {
                    cpu_attr_name: 85.5,
                    mem_attr_name: 90.0
                }
            }
        ]
        
        response = client.post(
            f'/api/v0.1/load/data/{type_id}',
            data={
                'upload_data': json.dumps(import_data),
                'time_slot': '08',
                'file_name': 'test_query_setup.json'
            }
        )
        assert response.status_code == 200
        
        # 等待导入完成
        import time
        time.sleep(2)
        
        # 查询忙时数据
        query_params = {
            'type_id': type_id,
            'unique_values': str(unique_value),
            'attribute_ids': ','.join(map(str, attr_ids)),
            'start_period': '2024-01-01',
            'end_period': '2024-01-01',
            'time_slot': '08'
        }
        
        response = client.get('/api/v0.1/load/data', query_string=query_params)
        assert response.status_code == 200
        
        result = json.loads(response.data)
        assert 'time_slot' in result
        assert result['time_slot'] == '08'
        assert result['time_slot_description'] == '忙时(08:00-18:00)'
        assert 'data' in result
        
        # 验证数据内容
        data_result = result['data']
        assert str(unique_value) in data_result
        assert '2024-01-01' in data_result[str(unique_value)]

    def test_query_data_with_invalid_time_slot(self, client: FlaskClient, db_session, request_context, auth_user):
        """测试使用无效时间段查询数据"""
        # 初始化基础数据
        ci = init_ci(1, auth_user)
        type_id = ci[0]['_type']
        
        # 创建基础属性
        attr = LoadAttrManager.add_attr(
            name="test_invalid_slot",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="测试无效时间段"
        )
        LoadRelationsManager.add_type_attr(type_id=type_id, attr_id=attr.id)
        
        query_params = {
            'type_id': type_id,
            'attribute_ids': str(attr.id),
            'start_period': '2024-01-01',
            'end_period': '2024-01-01',
            'time_slot': 'invalid'
        }
        
        response = client.get('/api/v0.1/load/data', query_string=query_params)
        assert response.status_code == 400
        assert '无效的时间段' in response.json['message']

    def test_import_monthly_data_ignores_time_slot(self, client: FlaskClient, prepare_time_slot_data):
        """测试月度数据导入忽略时间段参数"""
        data = prepare_time_slot_data
        type_id = data['type_id']
        unique_value = data['ci'][0][data['ci'][0]['unique']]
        
        # 导入月度数据，即使提供了时间段参数也应该被忽略
        cost_attr_name = data['attrs']['cost'].name
        
        import_data = [
            {
                "unique_value": unique_value,
                "2024-01": {
                    cost_attr_name: 1000.0
                }
            }
        ]
        
        response = client.post(
            f'/api/v0.1/load/data/{type_id}',
            data={
                'upload_data': json.dumps(import_data),
                'time_slot': '08',  # 这个参数对月度数据应该无效
                'file_name': 'test_monthly_with_slot.json'
            }
        )
        assert response.status_code == 200
        
        # 等待导入完成
        import time
        time.sleep(2)
        
        # 验证数据正确导入（月度数据不受时间段影响）
        query_params = {
            'type_id': type_id,
            'unique_values': str(unique_value),
            'attribute_ids': str(data['attrs']['cost'].id),
            'start_period': '2024-01',
            'end_period': '2024-01',
            'period_type': PeriodTypeEnum.MONTHLY
        }
        
        response = client.get('/api/v0.1/load/data', query_string=query_params)
        assert response.status_code == 200
        
        result = json.loads(response.data)
        data_result = result['data']
        assert str(unique_value) in data_result
        assert '2024-01' in data_result[str(unique_value)]
        assert data_result[str(unique_value)]['2024-01'][cost_attr_name] == 1000.0

    def test_backward_compatibility(self, client: FlaskClient, prepare_time_slot_data):
        """测试向后兼容性 - 不传时间段参数"""
        data = prepare_time_slot_data
        type_id = data['type_id']
        unique_value = data['ci'][0][data['ci'][0]['unique']]
        
        # 不传时间段参数的导入（保持原有行为）
        cpu_attr_name = data['attrs']['cpu'].name
        
        import_data = [
            {
                "unique_value": unique_value,
                "2024-01-01": {
                    cpu_attr_name: 75.5
                }
            }
        ]
        
        response = client.post(
            f'/api/v0.1/load/data/{type_id}',
            data={
                'upload_data': json.dumps(import_data),
                # 不传 time_slot 参数
                'file_name': 'test_backward_compatibility.json'
            }
        )
        assert response.status_code == 200
        
        result = response.json
        assert result['total'] == 1
        assert 'history_id' in result
        # 不应该包含时间段信息
        assert 'time_slot' not in result or result.get('time_slot') is None

        # 不传时间段参数的查询（保持原有行为）
        query_params = {
            'type_id': type_id,
            'unique_values': str(unique_value),
            'attribute_ids': str(data['attrs']['cpu'].id),
            'start_period': '2024-01-01',
            'end_period': '2024-01-01'
            # 不传 time_slot 参数
        }
        
        response = client.get('/api/v0.1/load/data', query_string=query_params)
        assert response.status_code == 200
        
        result = json.loads(response.data)
        assert 'data' in result
        # 不应该包含时间段信息
        assert 'time_slot' in result
