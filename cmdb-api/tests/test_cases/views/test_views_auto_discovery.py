import pytest
import json
from unittest.mock import patch
from flask.testing import FlaskClient

from api.models.cmdb import AutoDiscoveryRule, AutoDiscoveryCIType, CIType
from api.lib.cmdb.const import AutoDiscoveryType
from api.lib.cmdb.auto_discovery.const import PRIVILEGED_USERS as REAL_PRIVILEGED_USERS
from api.lib.cmdb.auto_discovery.auto_discovery import AutoDiscoveryRuleCRUD, AutoDiscoveryCITypeCRUD
from api.lib.utils import AESCrypto
from tests.utils.helpers import init_ci_types

from tests.fixtures.app import app, client, request_context

# 与 lib/test_auto_discovery.py 中保持一致的基础 Prefect 插件脚本数据

BASIC_PLUGIN_SCRIPT = {
    "flow_name": "test_flow",
    "entrypoint": "example_cvm.py:my_discovery_flow",
    "path_to_flow": "/app/src/flow/cmdb/",
    "flow_script": "example_cvm.py",
    "flow_function": "my_discovery_flow",
    "work_pool_name": "default-agent-pool",
    "tags": ["testing"]
}

# 与 lib/test_auto_discovery.py 中保持一致的基础选项数据
BASIC_OPTION = {
    "icon": {
        "name": "caise-chajian",
        "color": ""
    }
}

@pytest.fixture
def basic_prefect_rule_data():
    """基础 Prefect 规则数据fixture"""
    return {
        "name": "test_prefect_sync_rule",
        "type": AutoDiscoveryType.PREFECT,
        "is_plugin": True,
        "option": BASIC_OPTION,
        "plugin_script": json.dumps(BASIC_PLUGIN_SCRIPT)
    }


class TestPrefectSyncView:
    """测试 PrefectSyncView API 接口"""

    def test_prefect_sync_success(self, client: FlaskClient, db_session, auth_user, basic_prefect_rule_data):
        """测试成功获取 Prefect 同步配置"""
        ci_type = init_ci_types(1)[0]
        auth_user()
        # 1. 创建 Prefect 规则
        rule = AutoDiscoveryRuleCRUD().add(**basic_prefect_rule_data)
        assert rule.type == AutoDiscoveryType.PREFECT


        # 2. 创建关联的 AutoDiscoveryCIType (ADT)
        adt_data = {
            "type_id": ci_type.id,
            "adr_id": rule.id,
            "attributes" : {"instance_id": ci_type.unique_key.name}
        }
        adt = AutoDiscoveryCITypeCRUD().add(**adt_data)
        assert adt.adr_id == rule.id
        assert adt.type_id == ci_type.id

        # 3. 调用 PrefectSyncView 接口
        resp = client.get(f"/api/v0.1/adt/prefect/sync/{adt.id}")

        # 4. 断言结果
        assert resp.status_code == 200
        data = resp.json
        
        # 检查返回的数据是否是完整的 ADT 配置
        assert isinstance(data, dict)
        assert data["adt"]
        assert data["adr"]


    def test_prefect_sync_not_found(self, client: FlaskClient, db_session, auth_user):
        """测试访问不存在的 ADT ID"""
        auth_user()
        resp = client.get("/api/v0.1/adt/prefect/sync/999999")
        assert resp.status_code == 404