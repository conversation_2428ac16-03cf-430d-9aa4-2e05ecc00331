"""
CI Type 相关测试用例
"""
import logging
import pytest
from flask.testing import FlaskClient
from api.models.cmdb import (
    CIType, Attribute, CITypeAttribute, CITypeAttributeGroup,
    CITypeAttributeGroupItem
)
from tests.fixtures.database import db_session
from tests.fixtures.app import app, client
from tests.utils.helpers import (
    init_attributes, init_ci_types, init_attribute_groups
)

logger = logging.getLogger(__name__)

class TestCIType:
    """CI Type 测试类"""
    
    def test_create_ci_type(self, client: FlaskClient, db_session):
        """测试创建CI类型"""
        logger.info("开始测试创建CI类型")
        
        # 创建必需的属性字段
        logger.info("创建属性字段")
        attrs = init_attributes(num=1, value_type="2", unique=True)  # 使用helper函数创建属性
        hostname_attr = attrs[0]
        logger.info(f"创建属性字段成功: {hostname_attr.name}")
        
        # 准备测试数据
        ci_type_data = {
            "name": "test_server",
            "alias": "测试服务器",
            "unique_key": hostname_attr.id,
        }
        
        # 发送创建请求
        response = client.post('/api/v0.1/ci_types', json=ci_type_data)
        logger.info(f"创建CI类型响应: {response.json}")
        
        # 验证响应
        assert response.status_code == 200
        assert response.json["type_id"]
        
        # 验证数据库
        type_id = response.json["type_id"]
        ci_type_ins = CIType.get_by_id(type_id)
        assert ci_type_ins.id == type_id
        assert ci_type_ins.name == "test_server"
        assert ci_type_ins.alias == "测试服务器"
        assert ci_type_ins.unique_id == hostname_attr.id
        
        logger.info("CI类型创建测试完成")

    def test_update_ci_type(self, client: FlaskClient, db_session):
        """测试更新CI类型"""
        logger.info("开始测试更新CI类型")
        
        # 创建一个CI类型用于测试
        ci_type = init_ci_types(1)[0]
        logger.info(f"创建测试CI类型成功: {ci_type.name}")
        
        # 准备更新数据
        update_data = {
            "name": "updated_server",
        }
        
        # 发送更新请求
        response = client.put(f'/api/v0.1/ci_types/{ci_type.id}', json=update_data)
        logger.info(f"更新CI类型响应: {response.json}")
        
        # 验证响应
        assert response.status_code == 200
        assert response.json["type_id"] == ci_type.id
        
        # 验证数据库更新
        updated_ci_type = CIType.get_by_id(ci_type.id)
        assert updated_ci_type.name == "updated_server"
        
        logger.info("CI类型更新测试完成")

    def test_delete_ci_type(self, client: FlaskClient, db_session):
        """测试删除CI类型"""
        logger.info("开始测试删除CI类型")
        
        # 创建一个CI类型用于测试
        ci_type = init_ci_types(1)[0]
        logger.info(f"创建测试CI类型成功: {ci_type.name}")
        
        # 发送删除请求
        response = client.delete(f'/api/v0.1/ci_types/{ci_type.id}')
        logger.info(f"删除CI类型响应状态码: {response.status_code}")
        
        # 验证响应
        assert response.status_code == 200
        
        # 验证数据库删除
        deleted_ci_type = CIType.get_by_id(ci_type.id)
        assert deleted_ci_type is None
        
        logger.info("CI类型删除测试完成")

    def test_bind_attributes_ci_type(self, client: FlaskClient, db_session):
        """测试绑定属性到CI类型"""
        logger.info("开始测试绑定属性到CI类型")
        
        # 创建测试数据
        attrs = init_attributes(3)  # 创建3个属性
        ci_type = init_ci_types(1)[0]  # 创建1个CI类型
        logger.info(f"创建测试数据成功: CI类型={ci_type.name}, 属性数量={len(attrs)}")
        
        # 准备绑定数据
        bind_data = {
            "attr_id": [str(x.id) for x in attrs]
        }
        
        # 发送绑定请求
        response = client.post(f'/api/v0.1/ci_types/{ci_type.id}/attributes', json=bind_data)
        logger.info(f"绑定属性响应: {response.json}")
        
        # 验证响应
        assert response.status_code == 200
        assert len(response.json["attributes"]) == len(attrs)
        
        # 验证数据库绑定关系
        ci_type_attribute_ids = [
            x.attr_id for x in CITypeAttribute.query.filter_by(type_id=ci_type.id).all()
        ]
        for attr in attrs:
            assert attr.id in ci_type_attribute_ids
            
        logger.info("属性绑定测试完成")

    def test_get_attributes_ci_type(self, client: FlaskClient, db_session):
        """测试获取CI类型的属性列表"""
        logger.info("开始测试获取CI类型属性")
        
        # 创建测试数据
        ci_type = init_ci_types(1)[0]  # 这会创建一个CI类型和一个关联的属性
        logger.info(f"创建测试CI类型成功: {ci_type.name}")
        
        # 发送获取请求
        client.login()
        response = client.get(f'/api/v0.1/ci_types/{ci_type.name}/attributes')
        logger.info(f"获取属性响应: {response.json}")
        
        # 验证响应
        assert response.status_code == 200
        assert len(response.json["attributes"]) == 1  # init_ci_types会创建一个关联的属性
        
        logger.info("获取CI类型属性测试完成")

    def test_update_attributes_ci_type(self, client: FlaskClient, db_session):
        """测试更新CI类型的属性配置"""
        logger.info("开始测试更新CI类型属性配置")
        
        # 创建测试数据
        ci_type = init_ci_types(1)[0]  # 这会创建一个CI类型和一个关联的属性
        # 获取已关联的属性
        ci_type_attr = CITypeAttribute.query.filter_by(type_id=ci_type.id).first()
        attr = Attribute.get_by_id(ci_type_attr.attr_id)
        logger.info(f"创建测试数据成功: CI类型={ci_type.name}, 属性={attr.name}")
        
        # 准备更新数据
        update_data = {
            "attributes": [{
                "attr_id": attr.id,
                "default_show": False,
                "is_required": True
            }]
        }
        
        # 发送更新请求
        response = client.put(f'/api/v0.1/ci_types/{ci_type.id}/attributes', json=update_data)
        logger.info(f"更新属性配置响应: {response.status_code}")
        
        # 验证响应
        assert response.status_code == 200
        
        # 验证数据库更新
        ci_type_attr = CITypeAttribute.query.filter_by(type_id=ci_type.id, attr_id=attr.id).first()
        assert ci_type_attr is not None
        assert ci_type_attr.is_required is True
        assert ci_type_attr.default_show is False
        
        logger.info("CI类型属性配置更新测试完成")

    def test_create_attribute_group_ci_type(self, client: FlaskClient, db_session):
        """测试创建CI类型的属性组"""
        logger.info("开始测试创建CI类型属性组")
        
        # 创建测试数据
        ci_type = init_ci_types(1)[0]
        logger.info(f"创建测试CI类型成功: {ci_type.name}")
        
        # 准备创建数据
        group_data = {
            "name": "基本信息",
            "order": 100,
        }
        
        # 发送创建请求
        response = client.post(f'/api/v0.1/ci_types/{ci_type.id}/attribute_groups', json=group_data)
        logger.info(f"创建属性组响应: {response.json}")
        
        # 验证响应
        assert response.status_code == 200
        assert response.json["group_id"]
        
        # 验证数据库
        group = CITypeAttributeGroup.query.filter_by(type_id=ci_type.id).first()
        assert group is not None
        assert group.id == response.json["group_id"]
        assert group.name == "基本信息"
        assert group.order == 100
        
        logger.info("CI类型属性组创建测试完成")

    def test_update_attribute_group_ci_type(self, client: FlaskClient, db_session):
        """测试更新CI类型的属性组"""
        logger.info("开始测试更新CI类型属性组")
        
        # 创建测试数据
        attribute_group = init_attribute_groups(1)[0]
        attrs = init_attributes(3)  # 创建一些属性用于测试
        logger.info(f"创建测试数据成功: 属性组={attribute_group.name}")
        
        # 准备更新数据
        update_data = {
            "name": attribute_group.name,
            "attributes": [x.id for x in Attribute.query.all()]
        }
        
        # 发送更新请求
        response = client.put(
            f'/api/v0.1/ci_types/attribute_groups/{attribute_group.id}',
            json=update_data
        )
        logger.info(f"更新属性组响应: {response.json}")
        
        # 验证响应
        assert response.status_code == 200
        assert response.json["group_id"]
        
        # 验证数据库更新
        group_items = CITypeAttributeGroupItem.query.filter_by(
            group_id=attribute_group.id
        ).all()
        
        # 验证所有属性都被添加到组中
        for attr in Attribute.query.all():
            assert attr.id in [x.attr_id for x in group_items]
            
        logger.info("CI类型属性组更新测试完成")

    def test_delete_attribute_group_ci_type(self, client: FlaskClient, db_session):
        """测试删除CI类型的属性组"""
        logger.info("开始测试删除CI类型属性组")
        
        # 创建测试数据
        attribute_group = init_attribute_groups(1)[0]
        logger.info(f"创建测试数据成功: 属性组={attribute_group.name}")
        
        # 发送删除请求
        response = client.delete(f'/api/v0.1/ci_types/attribute_groups/{attribute_group.id}')
        logger.info(f"删除属性组响应状态码: {response.status_code}")
        
        # 验证响应
        assert response.status_code == 200
        
        # 验证数据库删除
        group = CITypeAttributeGroup.query.filter_by(id=attribute_group.id).first()
        assert group.deleted is True
        assert group.deleted_at is not None
        
        logger.info("CI类型属性组删除测试完成")

class TestCI:
    def test_ci_search_only_type_query(self, client: FlaskClient, db_session):

        rv = client.get('/api/v0.1/ci/s?q=_type:server', json={})
        # 详细记录响应
        logger.info(f"响应状态码: {rv.status_code}")
        logger.info(f"响应头: {dict(rv.headers)}")
        if rv.json:
            logger.info(f"响应数据类型: {type(rv.json)}")
            logger.info(f"响应数据结构: {rv.json.keys() if isinstance(rv.json, dict) else 'Not a dict'}")
            if 'data' in rv.json:
                logger.info(f"返回记录数: {len(rv.json['data'])}")
                logger.info(f"第一条记录示例: {rv.json['data'][0] if rv.json['data'] else 'No data'}")
            if 'error' in rv.json:
                logger.error(f"错误信息: {rv.json['error']}")
        assert rv.status_code == 200