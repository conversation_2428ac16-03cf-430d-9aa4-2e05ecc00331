# -*- coding:utf-8 -*-
"""测试PrefectManager的同步接口方法 - 集成测试"""

import pytest
import asyncio
import time
import uuid
import logging

from api.lib.cmdb.auto_discovery.prefect_manager import PrefectManager
from api.lib.cmdb.auto_discovery.prefect_manager import PrefectException


logger = logging.getLogger(__name__)

@pytest.fixture
def test_config():
    """提供测试配置"""
    unique_id = str(uuid.uuid4())[:8]  # 生成唯一标识符，避免测试冲突



    # /home/<USER>/oct-octopus/src/flow/cmdb/cvm.py
    return {
        "flow_name": f"test_flow_{unique_id}",
        "deployment_name": f"test_deployment_{unique_id}",
        "path_to_flow": "/app/src/flow/cmdb/",
        "flow_script": "example_cvm.py",
        "flow_function": "my_discovery_flow",
        "entrypoint": "example_cvm.py:my_discovery_flow",
        "work_pool_name": "default-agent-pool",  # 确保此工作池存在于您的Prefect服务中
        "tags": ["testing", "integration-test"],
        "parameters": {},
        "schedule": "* * * * *"
    }


@pytest.fixture
def deployment_id(test_config):
    """创建一个临时部署用于测试，并在测试后清理"""
    # 创建部署
    deployment_id = PrefectManager.create_deployment(
        flow_name=test_config["flow_name"],
        deployment_name=test_config["deployment_name"],
        entrypoint=test_config["entrypoint"],
        path_to_flow=test_config["path_to_flow"],
        work_pool_name=test_config["work_pool_name"],
        tags=test_config["tags"],
        parameters=test_config["parameters"]
    )

    # 确保部署已创建
    assert deployment_id is not None
    logger.info(f"为测试创建临时部署: {deployment_id}")

    # 返回部署ID供测试使用
    yield deployment_id

    # 测试完成后清理部署
    try:
        PrefectManager.delete_deployment(deployment_id)
        logger.info(f"测试后清理临时部署: {deployment_id}")
    except Exception as e:
        logger.error(f"清理部署时出错: {e}")


def test_create_deployment(test_config):
    """测试创建Deployment"""
    # 调用被测试方法
    deployment_id = None
    try:
        deployment_id = PrefectManager.create_deployment(
            flow_name=test_config["flow_name"],
            deployment_name=test_config["deployment_name"],
            entrypoint=test_config["entrypoint"],
            path_to_flow=test_config["path_to_flow"],
            work_pool_name=test_config["work_pool_name"],
            tags=test_config["tags"],
            parameters=test_config["parameters"],
            schedule=test_config["schedule"]
        )
        print(f"创建的部署ID: {deployment_id}")
        # 更严格的验证
        assert deployment_id is not None, "部署ID不应为空"
        assert isinstance(deployment_id, str), "部署ID应为字符串"
        assert len(deployment_id) > 8, "部署ID长度应大于8个字符"

        # 使用read_deployments验证部署是否存在
        deployments = PrefectManager.read_deployments(
            deployment_filter={"id": {"any_": [deployment_id]}}
        )

        # 检查是否找到部署
        assert len(deployments) > 0, "无法通过read_deployments找到新创建的部署"
        deployment = deployments[0]

        # 验证部署属性
        assert deployment["id"] == deployment_id, "部署ID不匹配"
        assert deployment["name"] == test_config["deployment_name"], "部署名称不匹配"

        logger.info(f"创建的部署ID: {deployment_id}")
        logger.info(f"部署信息: {deployment}")

    except Exception as e:
        # 捕获所有异常，而不仅仅是PrefectException
        pytest.fail(f"创建部署失败: {type(e).__name__}: {str(e)}")

    finally:
        # 确保清理，即使测试失败
        if deployment_id:
            try:
                PrefectManager.delete_deployment(deployment_id)
                print(f"清理了测试部署: {deployment_id}")
            except Exception as e:
                print(f"清理部署时出错: {type(e).__name__}: {str(e)}")


def test_update_deployment(deployment_id):
    """测试更新Deployment参数"""
    try:
        # 准备更新参数
        update_params = {
            # 更新cron调度
            'cron': "*/10 * * * *",  # 每10分钟执行一次
            # 更新激活状态
            'is_active': True,
            # 更新运行参数
            'parameters': {
                'test_param': 'updated_value',
                'adt_id': 12345,
                'type_id': 67890
            }
        }

        # 调用被测试方法
        result = PrefectManager.update_deployment(
            deployment_id=deployment_id,
            parameters=update_params
        )

        # 验证方法返回值
        assert result is True
        logger.info(f"更新部署参数成功: {result}")

        # 验证更新是否生效 - 获取部署信息
        deployments = PrefectManager.read_deployments(
            deployment_filter={"id": {"any_": [deployment_id]}}
        )

        # 检查是否找到部署
        assert len(deployments) > 0, "无法通过read_deployments找到更新后的部署"
        deployment = deployments[0]

        # 验证参数是否已更新
        assert deployment["parameters"].get("test_param") == "updated_value", "参数未正确更新"
        assert deployment["parameters"].get("adt_id") == 12345, "adt_id参数未正确更新"
        assert deployment["parameters"].get("type_id") == 67890, "type_id参数未正确更新"

        logger.info(f"更新后的部署信息: {deployment}")
    except PrefectException as e:
        pytest.fail(f"更新部署参数失败: {str(e)}")


def test_get_flow_run_status(deployment_id):
    """测试获取Flow Run状态"""
    try:
        # 首先触发一次flow运行
        flow_run_id = PrefectManager.trigger_flow_run(
            deployment_id=deployment_id,
            parameters={"test_param": "for_status_test"}
        )

        logger.info(f"触发的Flow Run ID: {flow_run_id}")
        assert flow_run_id is not None, "无法触发flow运行"

        # 等待一段时间让Flow Run有时间启动
        time.sleep(3)

        # 现在获取flow run状态
        status = PrefectManager.get_flow_run_status(deployment_id)

        # 验证方法返回值
        assert status is not None, "无法获取Flow Run状态"
        assert isinstance(status, dict), "Flow Run状态应为字典"
        assert "status" in status, "Flow Run状态字典应包含status字段"

        logger.info(f"Flow Run状态: {status}")

        # 还可以获取特定flow run的结果，这里只判断了是否有返回，没有考虑是否成功的问题
        run_state, run_result = PrefectManager.get_flow_run_result(flow_run_id)
        logger.info(f"特定Flow Run状态: {run_state}, 结果: {run_result}")

    except PrefectException as e:
        pytest.fail(f"获取Flow Run状态失败: {str(e)}")


def test_trigger_flow_run(deployment_id):
    """测试触发Flow Run"""
    try:
        # 调用被测试方法
        flow_run_id = PrefectManager.trigger_flow_run(
            deployment_id=deployment_id,
            parameters={"test_param": "test_value"}
        )

        # 验证方法返回值
        assert flow_run_id is not None
        assert isinstance(flow_run_id, str)
        print(f"触发的Flow Run ID: {flow_run_id}")

        # 等待一段时间让Flow Run有时间启动
        time.sleep(5)

        # 获取Flow Run状态
        status, result = PrefectManager.get_flow_run_result(flow_run_id)
        print(f"Flow Run状态: {status}, 结果: {result}")
    except PrefectException as e:
        pytest.fail(f"触发Flow Run失败: {str(e)}")


def test_pause_and_resume_deployment(deployment_id):
    """测试暂停和恢复Deployment调度"""
    try:
        # 测试暂停
        pause_result = PrefectManager.pause_deployment(deployment_id)
        assert pause_result is True
        print("部署调度已暂停")

        # 等待一段时间
        time.sleep(2)

        # 测试恢复
        resume_result = PrefectManager.resume_deployment(deployment_id)
        assert resume_result is True
        print("部署调度已恢复")
    except PrefectException as e:
        pytest.fail(f"暂停或恢复部署调度失败: {str(e)}")


def test_delete_deployment(test_config):
    """测试删除Deployment"""
    try:
        # 创建一个临时部署
        temp_deployment_id = PrefectManager.create_deployment(
            flow_name=f"{test_config['flow_name']}_delete_test",
            deployment_name=f"{test_config['deployment_name']}_delete_test",
            entrypoint=test_config["entrypoint"],
            path_to_flow=test_config["path_to_flow"],
            work_pool_name=test_config["work_pool_name"],
            tags=test_config["tags"],
            parameters=test_config["parameters"]
        )

        assert temp_deployment_id is not None
        print(f"为删除测试创建临时部署: {temp_deployment_id}")

        # 等待一段时间
        time.sleep(2)

        # 调用被测试方法
        result = PrefectManager.delete_deployment(temp_deployment_id)

        # 验证方法返回值
        assert result is True
        print(f"部署已删除: {temp_deployment_id}")
    except PrefectException as e:
        pytest.fail(f"删除部署失败: {str(e)}")


def test_get_flow_metadata(deployment_id):
    """测试获取Flow元数据"""
    try:
        # 调用被测试方法
        state, metadata = PrefectManager.get_flow_metadata(deployment_id)

        # 验证返回的状态
        assert state is not None, "状态不应为空"
        assert isinstance(state, str), "状态应为字符串"

        # 验证返回的元数据
        assert metadata is not None, "元数据不应为空"
        assert isinstance(metadata, dict), "元数据应为字典"
        assert "unique_key" in metadata, "元数据应包含unique_key字段"
        assert "attributes" in metadata, "元数据应包含attributes字段"
        assert isinstance(metadata["attributes"], list), "attributes应为列表"

        # 验证attributes的结构
        if metadata["attributes"]:
            first_attr = metadata["attributes"][0]
            assert isinstance(first_attr, (list, tuple)), "attribute项应为列表或元组"
            assert len(first_attr) in (2, 3), "attribute项应包含2或3个元素"

        logger.info(f"获取到的Flow元数据: {metadata}")

    except PrefectException as e:
        pytest.fail(f"获取Flow元数据失败: {str(e)}")


def test_get_flow_metadata_timeout(deployment_id):
    """测试获取Flow元数据超时情况"""
    # 直接使用非常短的超时时间，不需要预先触发Flow Run
    # deployment_id装饰器已经创建了部署
    from api.lib.cmdb.auto_discovery.prefect_manager import PrefectException

    # 捕获PrefectException异常
    with pytest.raises(PrefectException) as excinfo:
        # 使用极短的超时时间调用get_flow_metadata
        PrefectManager.get_flow_metadata(deployment_id, max_wait_time=1)

    # 验证异常信息
    error_message = str(excinfo.value)
    logger.info(f"捕获到异常: {error_message}")

    # 检查错误信息中是否包含超时相关字样
    error_lower = error_message.lower()
    timeout_keywords = ["超时", "timeout", "等待", "wait"]

    # 只要包含任一超时相关关键词即可通过
    assert any(keyword in error_lower for keyword in timeout_keywords), \
        f"错误信息应包含超时相关字样，实际错误: {error_message}"

    logger.info("成功捕获到超时异常")