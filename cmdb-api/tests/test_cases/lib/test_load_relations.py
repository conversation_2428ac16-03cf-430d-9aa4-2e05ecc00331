"""
关系管理器测试用例
"""
import logging
import pytest
from werkzeug.exceptions import BadRequest
from api.lib.cmdb.load.load_attr import LoadAttrManager, LoadRelationsManager
from api.lib.cmdb.load.const import LoadValueTypeEnum
from api.lib.cmdb.resp_format import ErrFormat
from api.models.cmdb import LoadCITypeAttribute
from tests.utils.helpers import init_ci_types

logger = logging.getLogger(__name__)

class TestLoadRelationsManager:
    """关系管理器测试类"""
    
    def test_add_type_attr(self, db_session):
        """测试添加CI类型字段关联"""
        logger.info("开始测试添加CI类型字段关联")
        
        # 创建测试数据
        ci_type = init_ci_types(1)[0]
        attr = LoadAttrManager.add_attr(
            name="type_test_attr",
            value_type=LoadValueTypeEnum.TEXT,
            alias="测试字段",
        )
        logger.info(f"创建测试数据成功: CI类型ID={ci_type.id}, 属性ID={attr.id}")
        
        # 测试正常添加关联
        relation = LoadRelationsManager.add_type_attr(
            type_id=ci_type.id,
            attr_id=attr.id,
            is_required=True,
            order=1
        )
        assert relation.type_id == ci_type.id
        assert relation.load_attr_id == attr.id
        assert relation.is_required is True
        assert relation.order == 1
        logger.info("正常添加关联测试通过")
        
        # 测试重复添加
        with pytest.raises(BadRequest) as exc:
            LoadRelationsManager.add_type_attr(
                type_id=ci_type.id,
                attr_id=attr.id
            )
        assert ErrFormat.load_attr_referenced.format(attr.id) == exc.value.description
        logger.info("重复添加测试通过")
        
        # 测试添加不存在的字段
        with pytest.raises(BadRequest) as exc:
            LoadRelationsManager.add_type_attr(
                type_id=ci_type.id,
                attr_id=999999
            )
        assert ErrFormat.load_attr_not_found.format(999999) == exc.value.description
        logger.info("添加不存在字段测试通过")
        
        logger.info("添加CI类型字段关联测试完成")
    
    def test_update_type_attr(self, db_session):
        """测试更新CI类型字段关联"""
        logger.info("开始测试更新CI类型字段关联")
        
        # 创建测试数据
        ci_type = init_ci_types(1)[0]
        attr = LoadAttrManager.add_attr(
            name="update_type_attr",
            value_type=LoadValueTypeEnum.TEXT,
            alias="测试字段",
        )
        relation = LoadRelationsManager.add_type_attr(
            type_id=ci_type.id,
            attr_id=attr.id,
            is_required=False
        )
        logger.info(f"创建测试数据成功: CI类型ID={ci_type.id}, 属性ID={attr.id}")
        
        # 测试正常更新
        updated = LoadRelationsManager.update_type_attr(
            type_id=ci_type.id,
            attr_id=attr.id,
            is_required=True,
            order=2
        )
        assert updated.is_required is True
        assert updated.order == 2
        logger.info("正常更新测试通过")
        
        # 测试更新不存在的关联
        with pytest.raises(BadRequest) as exc:
            LoadRelationsManager.update_type_attr(
                type_id=ci_type.id,
                attr_id=999999
            )
        assert ErrFormat.load_attr_not_found.format(999999) == exc.value.description
        logger.info("更新不存在关联测试通过")
        
        logger.info("更新CI类型字段关联测试完成")
    
    def test_delete_type_attr(self, db_session):
        """测试删除CI类型字段关联"""
        logger.info("开始测试删除CI类型字段关联")
        
        # 创建测试数据
        ci_type = init_ci_types(1)[0]
        attr = LoadAttrManager.add_attr(
            name="delete_type_attr",
            value_type=LoadValueTypeEnum.TEXT,
            alias="测试字段",
        )
        relation = LoadRelationsManager.add_type_attr(
            type_id=ci_type.id,
            attr_id=attr.id
        )
        logger.info(f"创建测试数据成功: CI类型ID={ci_type.id}, 属性ID={attr.id}")
        
        # 测试正常删除
        assert LoadRelationsManager.delete_type_attr(ci_type.id, attr.id) is True
        logger.info("正常删除测试通过")
        
        # 测试删除不存在的关联
        with pytest.raises(BadRequest) as exc:
            LoadRelationsManager.delete_type_attr(ci_type.id, attr.id)
        assert ErrFormat.load_attr_not_found.format(attr.id) == exc.value.description
        logger.info("删除不存在关联测试通过")
        
        logger.info("删除CI类型字段关联测试完成")
    
    def test_batch_update_type_attrs(self, db_session):
        """测试批量更新CI类型字段关联"""
        logger.info("开始测试批量更新CI类型字段关联")
        
        # 创建测试数据
        ci_type = init_ci_types(1)[0]
        attr1 = LoadAttrManager.add_attr(
            name="batch_attr1",
            value_type=LoadValueTypeEnum.TEXT,
            alias="测试字段1",
        )
        attr2 = LoadAttrManager.add_attr(
            name="batch_attr2",
            value_type=LoadValueTypeEnum.TEXT,
            alias="测试字段2",
        )
        logger.info(f"创建测试数据成功: CI类型ID={ci_type.id}, 属性1ID={attr1.id}, 属性2ID={attr2.id}")
        
        # 测试批量更新
        attr_configs = [
            {"type_id": ci_type.id, "attr_id": attr1.id, "is_required": True, "order": 1},
            {"type_id": ci_type.id, "attr_id": attr2.id, "is_required": False, "order": 2}
        ]
        assert LoadRelationsManager.batch_update_type_attrs(ci_type.id, attr_configs) is True
        logger.info("批量更新执行成功")
        
        # 验证更新结果
        attrs = LoadRelationsManager.get_type_attrs(ci_type.id)
        assert len(attrs) == 2
        logger.info("验证属性数量正确")
        
        # 验证字段1的配置
        attr1_relation = next(f for f in attrs if f.id == attr1.id)
        assert attr1_relation.is_required is True
        assert attr1_relation.order == 1
        logger.info("验证属性1配置正确")
        
        # 验证字段2的配置
        attr2_relation = next(f for f in attrs if f.id == attr2.id)
        assert attr2_relation.is_required is False
        assert attr2_relation.order == 2
        logger.info("验证属性2配置正确")
        
        logger.info("批量更新CI类型字段关联测试完成") 