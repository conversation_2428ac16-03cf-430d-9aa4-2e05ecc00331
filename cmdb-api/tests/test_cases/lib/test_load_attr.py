"""
属性管理器测试用例
"""
import logging
import pytest
from api.lib.cmdb.load.load_attr import LoadAttrManager
from api.lib.cmdb.load.const import LoadValueTypeEnum
from werkzeug.exceptions import BadRequest
from flask import current_app, g
from api.models.cmdb import LoadAttribute

logger = logging.getLogger(__name__)

class TestLoadAttrManager:
    """属性管理器测试类"""
    
    def test_add_attr(self, db_session, request_context):
        """测试添加字段"""
        logger.info("开始测试添加字段")
        
        # 测试正常添加
        attr = LoadAttrManager.add_attr(
            name="test_attr",
            value_type=LoadValueTypeEnum.TEXT,
            alias="测试字段",
        )
        assert attr.name == "test_attr"
        assert attr.value_type == LoadValueTypeEnum.TEXT
        assert attr.alias == "测试字段"
        
        # 测试重复添加应该抛出异常
        with pytest.raises(BadRequest) as exc:
            LoadAttrManager.add_attr(
                name="test_attr",
                value_type=LoadValueTypeEnum.TEXT,
                alias="测试字段2",
            )
        assert "已存在" in str(exc.value)  # 验证错误信息包含"已存在"
        
        logger.info("添加字段测试完成")
    
    def test_get_attr(self, db_session):
        """测试获取字段"""
        logger.info("开始测试获取字段")
        
        # 先添加一个字段
        attr = LoadAttrManager.add_attr(
            name="get_test_attr",
            value_type=LoadValueTypeEnum.TEXT,
            alias="测试字段",
        )
        
        # 通过ID获取
        result = LoadAttrManager.get_attr(attr.id)
        assert result.id == attr.id
        assert result.name == "get_test_attr"
        
        # 通过名称获取
        result = LoadAttrManager.get_attr("get_test_attr")
        assert result.id == attr.id
        assert result.name == "get_test_attr"
        
        # 获取不存在的字段
        result = LoadAttrManager.get_attr("non_existent")
        assert result is None
        
        logger.info("获取字段测试完成")
    
    def test_update_attr(self, db_session):
        """测试更新字段"""
        logger.info("开始测试更新字段")
        
        # 先添加一个字段
        attr = LoadAttrManager.add_attr(
            name="update_test_attr",
            value_type=LoadValueTypeEnum.TEXT,
            alias="原始别名"
        )
        
        # 测试正常更新
        updated = LoadAttrManager.update_attr(
            attr.id,
            alias="新别名",
        )
        assert updated.alias == "新别名"
        
        # 测试更新不存在的字段 - 验证字段不存在
        non_existent = LoadAttrManager.get_attr(999999)
        assert non_existent is None
        
        logger.info("更新字段测试完成")
    
    def test_delete_attr(self, db_session):
        """测试删除字段"""
        logger.info("开始测试删除字段")
        
        # 先添加一个字段
        attr = LoadAttrManager.add_attr(
            name="delete_test_attr",
            value_type=LoadValueTypeEnum.TEXT,
            alias="新别名",
        )
        
        # 测试正常删除
        assert LoadAttrManager.delete_attr(attr.id) is True
        
        # 验证已删除
        result = LoadAttrManager.get_attr(attr.id)
        assert result is None
        
        # 测试删除不存在的字段 - 验证字段不存在
        non_existent = LoadAttrManager.get_attr(999999)
        assert non_existent is None
        
        logger.info("删除字段测试完成")
    
    def test_add_monthly_attr(self, db_session, request_context):
        """测试添加月度字段"""
        logger.info("开始测试添加月度字段")
        
        # 测试添加月度字段
        attr = LoadAttrManager.add_attr(
            name="monthly_cost",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="月度成本",
            is_monthly=True
        )
        
        # 验证字段属性
        assert attr.name == "monthly_cost"
        assert attr.value_type == LoadValueTypeEnum.FLOAT
        assert attr.alias == "月度成本"
        assert attr.is_monthly is True
        
        # 验证数据库中的字段
        db_attr = LoadAttribute.query.filter_by(id=attr.id).first()
        assert db_attr is not None
        assert db_attr.is_monthly is True
        
        logger.info("添加月度字段测试完成") 