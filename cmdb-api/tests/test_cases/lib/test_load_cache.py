import pytest
import datetime
from unittest.mock import patch, MagicMock

from api.lib.cmdb.cache import LoadDataQueryCache
from api.lib.cmdb.load.load_attr import LoadDataManager
from api.lib.cmdb.load.const import LoadValueTypeEnum, PeriodTypeEnum
from api.lib.cmdb.load.load_attr import LoadAttrManager, LoadRelationsManager
from api.models.cmdb import LoadDataImportHistory
from api.lib.cmdb.load.const import HistoryStatusEnum
from tests.utils.helpers import init_ci


class TestLoadDataQueryCache:
    """负载数据查询缓存测试类 - 专门测试缓存键的正确性"""

    def test_cache_key_generation_with_time_slot(self):
        """测试缓存键生成是否包含时间段参数"""
        # 测试包含时间段的缓存键
        key_with_slot = LoadDataQueryCache._gen_cache_key(
            type_id=1,
            ci_ids=[1, 2, 3],
            attribute_ids=[10, 20],
            start_period="2024-01-01",
            end_period="2024-01-31",
            period_type=PeriodTypeEnum.DAILY,
            time_slot="08"
        )
        
        # 测试不包含时间段的缓存键
        key_without_slot = LoadDataQueryCache._gen_cache_key(
            type_id=1,
            ci_ids=[1, 2, 3],
            attribute_ids=[10, 20],
            start_period="2024-01-01",
            end_period="2024-01-31",
            period_type=PeriodTypeEnum.DAILY,
            time_slot=None
        )
        
        # 验证两个键不同
        assert key_with_slot != key_without_slot
        assert "slot_08" in key_with_slot
        assert "slot_none" in key_without_slot
        
    def test_cache_key_different_time_slots(self):
        """测试不同时间段生成不同的缓存键"""
        base_params = {
            'type_id': 1,
            'ci_ids': [1, 2],
            'attribute_ids': [10],
            'start_period': "2024-01-01",
            'end_period': "2024-01-31",
            'period_type': PeriodTypeEnum.DAILY
        }
        
        busy_key = LoadDataQueryCache._gen_cache_key(**base_params, time_slot="08")
        idle_key = LoadDataQueryCache._gen_cache_key(**base_params, time_slot="00")
        no_slot_key = LoadDataQueryCache._gen_cache_key(**base_params, time_slot=None)
        
        # 验证三个键都不相同
        assert busy_key != idle_key
        assert busy_key != no_slot_key
        assert idle_key != no_slot_key
        
        # 验证键包含正确的时间段标识
        assert "slot_08" in busy_key
        assert "slot_00" in idle_key
        assert "slot_none" in no_slot_key

    @patch('api.lib.cmdb.cache.cache')
    def test_cache_isolation_between_time_slots(self, mock_cache):
        """测试不同时间段的缓存隔离"""
        # 模拟缓存行为
        cache_store = {}
        
        def mock_get(key):
            return cache_store.get(key)
        
        def mock_set(key, value, timeout=None):
            cache_store[key] = value
        
        mock_cache.get.side_effect = mock_get
        mock_cache.set.side_effect = mock_set
        
        base_params = {
            'type_id': 1,
            'ci_ids': [1, 2],
            'attribute_ids': [10],
            'start_period': "2024-01-01",
            'end_period': "2024-01-31",
            'period_type': PeriodTypeEnum.DAILY
        }
        
        # 为忙时设置缓存
        busy_data = [('ci1', 'unique1', '2024-01-01'), ('ci2', 'unique2', '2024-01-01')]
        LoadDataQueryCache.set(**base_params, data=busy_data, time_slot="08")
        
        # 为闲时设置缓存
        idle_data = [('ci1', 'unique1', '2024-01-02'), ('ci2', 'unique2', '2024-01-02')]
        LoadDataQueryCache.set(**base_params, data=idle_data, time_slot="00")
        
        # 验证缓存隔离
        cached_busy = LoadDataQueryCache.get(**base_params, time_slot="08")
        cached_idle = LoadDataQueryCache.get(**base_params, time_slot="00")
        cached_none = LoadDataQueryCache.get(**base_params, time_slot=None)
        
        assert cached_busy == busy_data
        assert cached_idle == idle_data
        assert cached_none is None  # 没有为None设置缓存
        
        # 验证设置了3个不同的缓存键
        assert len(cache_store) == 2

    def test_cache_key_consistency(self):
        """测试相同参数生成一致的缓存键"""
        params = {
            'type_id': 1,
            'ci_ids': [3, 1, 2],  # 故意乱序
            'attribute_ids': [20, 10],  # 故意乱序
            'start_period': "2024-01-01",
            'end_period': "2024-01-31",
            'period_type': PeriodTypeEnum.DAILY,
            'time_slot': "08"
        }
        
        # 多次生成键应该一致
        key1 = LoadDataQueryCache._gen_cache_key(**params)
        key2 = LoadDataQueryCache._gen_cache_key(**params)
        
        assert key1 == key2
        
        # 验证数组参数被正确排序
        assert "ci_1_2_3" in key1  # ci_ids应该被排序
        assert "attr_10_20" in key1  # attribute_ids应该被排序


class TestLoadDataManagerCacheIntegration:
    """负载数据管理器缓存集成测试类"""

    def test_query_data_cache_isolation_with_time_slots(self, db_session, app_context, request_context, auth_user, celery_worker):
        """测试查询数据时时间段缓存隔离的端到端测试"""
        ci = init_ci(1, auth_user)
        unique_value = ci[0][ci[0]['unique']]
        
        # 创建测试属性
        attr = LoadAttrManager.add_attr(
            name="cache_test_cpu",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="缓存测试CPU"
        )
        
        LoadRelationsManager.add_type_attr(
            type_id=ci[0]['_type'],
            attr_id=attr.id
        )
        
        # 分别导入忙时和闲时数据
        busy_data = [{
            "unique_value": unique_value,
            "2024-01-01": {"cache_test_cpu": 85.5}
        }]
        
        idle_data = [{
            "unique_value": unique_value,
            "2024-01-01": {"cache_test_cpu": 45.5}
        }]
        
        # 导入数据
        LoadDataManager.batch_import(
            type_id=ci[0]['_type'], data=busy_data, time_slot="08"
        )
        LoadDataManager.batch_import(
            type_id=ci[0]['_type'], data=idle_data, time_slot="00"
        )
        
        import time
        time.sleep(3)  # 等待异步任务完成
        
        # 使用patch监控缓存调用
        with patch('api.lib.cmdb.cache.LoadDataQueryCache.get') as mock_get, \
             patch('api.lib.cmdb.cache.LoadDataQueryCache.set') as mock_set:
            
            # 设置缓存行为
            cache_store = {}
            
            def mock_cache_get(*args, **kwargs):
                key = LoadDataQueryCache._gen_cache_key(*args, **kwargs)
                return cache_store.get(key)
            
            def mock_cache_set(*args, **kwargs):
                data = kwargs.pop('data')
                key = LoadDataQueryCache._gen_cache_key(*args, **kwargs)
                cache_store[key] = data
            
            mock_get.side_effect = mock_cache_get
            mock_set.side_effect = mock_cache_set
            
            # 第一次查询忙时数据 - 应该缓存miss并设置缓存
            result1 = LoadDataManager.query_data(
                type_id=ci[0]['_type'],
                ci_ids=[ci[0]['_id']],
                attribute_ids=[attr.id],
                start_period="2024-01-01",
                end_period="2024-01-01",
                time_slot="08"
            )
            
            # 第二次查询忙时数据 - 应该缓存命中
            result2 = LoadDataManager.query_data(
                type_id=ci[0]['_type'],
                ci_ids=[ci[0]['_id']],
                attribute_ids=[attr.id],
                start_period="2024-01-01",
                end_period="2024-01-01",
                time_slot="08"
            )
            
            # 查询闲时数据 - 应该缓存miss并设置缓存
            result3 = LoadDataManager.query_data(
                type_id=ci[0]['_type'],
                ci_ids=[ci[0]['_id']],
                attribute_ids=[attr.id],
                start_period="2024-01-01",
                end_period="2024-01-01",
                time_slot="00"
            )
            
            # 验证缓存调用
            assert mock_get.call_count == 3  # 3次查询都应该尝试获取缓存
            assert mock_set.call_count == 2  # 忙时和闲时各设置一次缓存
            
            # 验证查询结果正确
            assert result1['data'][str(unique_value)]["2024-01-01"]["cache_test_cpu"] == 85.5
            assert result2['data'][str(unique_value)]["2024-01-01"]["cache_test_cpu"] == 85.5
            assert result3['data'][str(unique_value)]["2024-01-01"]["cache_test_cpu"] == 45.5
            
            # 验证时间段信息
            assert result1.get("time_slot") == "08"
            assert result2.get("time_slot") == "08"
            assert result3.get("time_slot") == "00"

    def test_different_time_slot_different_cache(self, db_session, app_context, request_context, auth_user, celery_worker):
        """测试不同时间段使用不同缓存的关键测试"""
        ci = init_ci(1, auth_user)
        unique_value = ci[0][ci[0]['unique']]
        
        # 创建测试属性
        attr = LoadAttrManager.add_attr(
            name="time_slot_cache_test",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="时间段缓存测试"
        )
        
        LoadRelationsManager.add_type_attr(
            type_id=ci[0]['_type'],
            attr_id=attr.id
        )
        
        # 分别导入不同时间段的数据，但使用相同的日期
        busy_data = [{
            "unique_value": unique_value,
            "2024-01-01": {"time_slot_cache_test": 85.5}
        }]
        
        idle_data = [{
            "unique_value": unique_value,
            "2024-01-01": {"time_slot_cache_test": 45.5}  # 同一天但不同时间段的不同值
        }]
        
        # 导入数据
        LoadDataManager.batch_import(
            type_id=ci[0]['_type'], data=busy_data, time_slot="08"
        )
        LoadDataManager.batch_import(
            type_id=ci[0]['_type'], data=idle_data, time_slot="00"
        )
        
        import time
        time.sleep(3)
        
        # 这是关键测试：查询同样的CI、同样的属性、同样的日期，但不同时间段
        base_query_params = {
            'type_id': ci[0]['_type'],
            'ci_ids': [ci[0]['_id']],
            'attribute_ids': [attr.id],
            'start_period': "2024-01-01",
            'end_period': "2024-01-01"
        }
        
        # 查询忙时数据
        busy_result = LoadDataManager.query_data(**base_query_params, time_slot="08")
        
        # 查询闲时数据  
        idle_result = LoadDataManager.query_data(**base_query_params, time_slot="00")
        
        # 关键验证：相同查询条件下，不同时间段应该返回不同的数据
        busy_value = busy_result['data'][str(unique_value)]["2024-01-01"]["time_slot_cache_test"]
        idle_value = idle_result['data'][str(unique_value)]["2024-01-01"]["time_slot_cache_test"]
        
        assert busy_value == 85.5, f"忙时应该返回85.5，但返回了{busy_value}"
        assert idle_value == 45.5, f"闲时应该返回45.5，但返回了{idle_value}"
        assert busy_value != idle_value, "不同时间段应该返回不同的值"
        
        # 验证时间段信息
        assert busy_result.get("time_slot") == "08"
        assert idle_result.get("time_slot") == "00" 