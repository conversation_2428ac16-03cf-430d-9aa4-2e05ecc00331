# -*- coding:utf-8 -*-
"""AutoDiscoveryCITypeCRUD 测试用例"""

import pytest
import json
import uuid
from unittest.mock import patch, MagicMock
from flask import abort, current_app
from api.lib.cmdb.auto_discovery.auto_discovery import AutoDiscoveryCITypeCRUD
from api.lib.cmdb.const import AutoDiscoveryType
from api.lib.cmdb.resp_format import ErrFormat
from api.models.cmdb import AutoDiscoveryRule, CIType, AutoDiscoveryCI

@pytest.fixture
def mock_adr():
    """模拟 AutoDiscoveryRule"""
    with patch('api.models.cmdb.AutoDiscoveryRule.get_by_id') as mock:
        yield mock

@pytest.fixture
def mock_ci_type():
    """模拟 CIType"""
    ci_type = MagicMock()
    ci_type.id = 1
    ci_type.unique_id = "instance_id"
    ci_type.alias = "test_type"
    
    with patch('api.lib.cmdb.cache.CITypeCache.get') as mock:
        mock.return_value = ci_type
        yield mock

@pytest.fixture
def mock_attribute():
    """模拟 Attribute"""
    attr = MagicMock()
    attr.id = "instance_id"
    attr.name = "instance_id"
    
    with patch('api.lib.cmdb.cache.AttributeCache.get') as mock:
        mock.return_value = attr
        yield mock

@pytest.fixture
def basic_adt_data():
    """基础的 ADT 测试数据"""
    return {
        "type_id": 1,
        "adr_id": 1,
        "enabled": True,
        "cron": "0 0 * * *",
        "attributes": {
            "instance_id": "instance_id",
            "hostname": "name"
        },
        "extra_option": {
            "prefect_params": {
                "region": "cn-north-1"
            }
        }
    }

@pytest.fixture
def mock_adt_instance(mock_current_user):
    """创建模拟的 ADT 实例"""
    instance = MagicMock()
    instance.id = 1
    instance.type_id = 1
    instance.adr_id = 1
    instance.uid = mock_current_user.uid
    instance.agent_id = None
    instance.query_expr = None
    instance.extra_option = {"prefect_deployment_id": str(uuid.uuid4())}
    return instance

@pytest.fixture
def mock_adr_instance():
    """创建模拟的 ADR 实例"""
    return MagicMock(
        id=1,
        type=AutoDiscoveryType.PREFECT,
        plugin_script=json.dumps({
            "flow_name": "test_flow",
            "entrypoint": "test.py:flow",
            "path_to_flow": "/test/flows",
            "work_pool_name": "default-agent-pool"
        })
    )

@pytest.fixture
def mock_prefect_manager():
    """模拟 PrefectManager"""
    with patch('api.lib.cmdb.auto_discovery.auto_discovery.PrefectManager') as mock:
        # 创建一个模拟的 flow_id (UUID)
        flow_id = uuid.UUID('12345678-1234-5678-1234-************')
        deployment_id = str(uuid.UUID('*************-8765-4321-************'))
        
        # 设置 create_deployment 方法的行为
        def mock_create_deployment(flow_name=None, deployment_name=None, **kwargs):
            if not flow_name or not deployment_name:
                return None
            return deployment_id
            
        mock.create_deployment = MagicMock(side_effect=mock_create_deployment)
        mock.update_deployment.return_value = True
        mock.delete_deployment.return_value = True
        
        # 模拟获取 flow 的行为
        mock.get_flow_by_name = MagicMock(return_value=flow_id)
        
        yield mock

# @pytest.fixture
# def app():
#     """创建测试应用上下文"""
#     from api.app import create_app
#     app = create_app()
#     app.config['TESTING'] = True
#     return app

# @pytest.fixture
# def app_context(app):
#     """提供应用上下文"""
#     with app.app_context():
#         yield app

# @pytest.fixture
# def request_context(app):
#     """提供请求上下文"""
#     with app.test_request_context():
#         yield app

@pytest.fixture
def mock_current_user():
    """模拟当前用户"""
    with patch('flask_login.current_user') as mock_user:
        mock_user.uid = 1
        mock_user.username = "test_user"
        mock_user.nickname = "Test User"
        yield mock_user

class TestAutoDiscoveryCITypeCRUD:
    """测试 AutoDiscoveryCITypeCRUD 类的核心方法"""

    def test_can_add_success(self, auth_user, app_context, request_context, mock_current_user, 
                            mock_adr, mock_ci_type, mock_attribute, mock_prefect_manager, basic_adt_data,
                            mock_adr_instance):
        """测试成功添加 ADT"""
        auth_user()
        mock_adr.return_value = mock_adr_instance
        crud = AutoDiscoveryCITypeCRUD()
        
        result = crud._can_add(**basic_adt_data)
        
        assert result["type_id"] == basic_adt_data["type_id"]
        assert result["adr_id"] == basic_adt_data["adr_id"]
        assert result["uid"] == mock_current_user.uid
        assert "prefect_deployment_id" in result["extra_option"]
        assert isinstance(result["extra_option"]["prefect_deployment_id"], str)

    def test_can_add_missing_unique_key(self, app_context, request_context, mock_current_user, 
                                      mock_adr, mock_ci_type, mock_attribute, basic_adt_data):
        """测试添加时缺少唯一标识"""
        invalid_data = dict(basic_adt_data)
        invalid_data["attributes"] = {"hostname": "name"}
        crud = AutoDiscoveryCITypeCRUD()
        
        with pytest.raises(Exception) as exc_info:
            crud._can_add(**invalid_data)
        assert str(ErrFormat.ad_not_unique_key.format("instance_id")) in str(exc_info.value)

    def test_can_update_success(self, auth_user, app_context, request_context, mock_current_user,
                              mock_adr, mock_ci_type, mock_attribute, mock_prefect_manager, mock_adt_instance,
                              mock_adr_instance):
        """测试成功更新 ADT"""
        auth_user()
        crud = AutoDiscoveryCITypeCRUD()
        mock_adr.return_value = mock_adr_instance
        
        with patch.object(crud.cls, 'get_by_id', return_value=mock_adt_instance):
            result = crud._can_update(_id=1, type_id=1, adr_id=1)
            assert result == mock_adt_instance

    def test_can_update_unauthorized_secret(self, auth_user, app_context, request_context,
                                         mock_current_user, mock_adr, mock_ci_type, mock_attribute,
                                         mock_adt_instance, mock_adr_instance):
        """测试未授权更新 secret"""
        auth_user()
        crud = AutoDiscoveryCITypeCRUD()
        
        unauthorized_instance = MagicMock()
        unauthorized_instance.id = mock_adt_instance.id
        unauthorized_instance.uid = 999  # 设置不同的用户ID
        unauthorized_instance.adr_id = mock_adt_instance.adr_id
        unauthorized_instance.type_id = mock_adt_instance.type_id
        unauthorized_instance.agent_id = None
        unauthorized_instance.query_expr = None
        
        update_data = {
            "_id": 1,
            "extra_option": {"secret": "new_secret"}
        }
        
        mock_adr.return_value = mock_adr_instance
        
        with patch.object(crud.cls, 'get_by_id', return_value=unauthorized_instance):
            with pytest.raises(Exception) as exc_info:
                crud._can_update(**update_data)
            assert str(ErrFormat.adt_secret_no_permission) in str(exc_info.value)

    # def test_can_update_with_attributes(self, auth_user, app_context, request_context,
    #                                   mock_current_user, mock_adr, mock_ci_type, mock_attribute,
    #                                   mock_adt_instance, mock_adr_instance):
    #     """测试更新包含 attributes 的情况"""
    #     auth_user()
    #     crud = AutoDiscoveryCITypeCRUD()
        
    #     update_data = {
    #         "_id": 1,
    #         "attributes": {
    #             "new_instance_id": "instance_id",
    #             "hostname": "new_name"
    #         }
    #     }
        
    #     mock_adr.return_value = mock_adr_instance
        
    #     with patch.object(crud.cls, 'get_by_id', return_value=mock_adt_instance):
    #         result = crud._can_update(**update_data)
    #         assert result == mock_adt_instance

    def test_can_delete_success(self, app_context, request_context, mock_current_user,
                              mock_adr, mock_prefect_manager, mock_adt_instance, mock_adr_instance):
        """测试成功删除 ADT"""
        crud = AutoDiscoveryCITypeCRUD()
        mock_adr.return_value = mock_adr_instance
        
        with patch('api.lib.cmdb.auto_discovery.auto_discovery.AutoDiscoveryCICRUD') as mock_ci_crud:
            mock_ci_crud.get_by_adt_id.return_value = []
            with patch.object(crud.cls, 'get_by_id', return_value=mock_adt_instance):
                result = crud._can_delete(_id=1)
                assert result == mock_adt_instance

    def test_can_delete_with_ci(self, app_context, request_context, mock_current_user):
        """测试删除有关联 CI 的 ADT"""
        crud = AutoDiscoveryCITypeCRUD()
        
        with patch('api.lib.cmdb.auto_discovery.auto_discovery.AutoDiscoveryCICRUD') as mock_ci_crud:
            mock_ci_crud.get_by_adt_id.return_value = [MagicMock()]
            with pytest.raises(Exception) as exc_info:
                crud._can_delete(_id=1)
            assert str(ErrFormat.cannot_delete_adt) in str(exc_info.value)

    def test_can_delete_missing_instance(self, app_context, request_context, mock_current_user):
        """测试删除不存在的实例"""
        crud = AutoDiscoveryCITypeCRUD()
        
        with patch('api.lib.cmdb.auto_discovery.auto_discovery.AutoDiscoveryCICRUD') as mock_ci_crud:
            mock_ci_crud.get_by_adt_id.return_value = []
            with patch.object(crud.cls, 'get_by_id', return_value=None):
                with pytest.raises(Exception) as exc_info:
                    crud._can_delete(_id=999)
                assert str(ErrFormat.ad_not_found.format("id=999")) in str(exc_info.value)