"""
负载数据管理器测试用例
"""
import logging
import pytest
from datetime import datetime, date
from werkzeug.exceptions import BadRequest
from api.lib.cmdb.load.load_attr import LoadAttrManager, LoadRelationsManager, LoadDataManager, LoadHistoryManager
from api.lib.cmdb.load.const import LoadValueTypeEnum, PeriodTypeEnum, HistoryStatusEnum
from api.models.cmdb import LoadDataImportHistory
from tests.utils.helpers import init_ci
from flask import current_app

logger = logging.getLogger(__name__)

class TestLoadDataManager:
    """负载数据管理器测试类"""
    
    def test_batch_import_success(self, db_session, app_context, request_context, auth_user, celery_worker):
        """测试成功的批量导入场景"""
        logger.info("开始执行 test_batch_import_success 测试")
        ci = init_ci(1, auth_user)
        logger.info(f"初始化CI完成: {ci}")
        
        unique_value = ci[0][ci[0]['unique']]
        logger.info(f"获取unique_value: {unique_value}")
        
        # 创建测试属性
        attr = LoadAttrManager.add_attr(
            name="cpu_usage",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="CPU使用率",
        )
        logger.info(f"创建测试属性完成: {attr.id}")
        
        # 添加属性到CI类型
        LoadRelationsManager.add_type_attr(
            type_id=ci[0]['_type'],
            attr_id=attr.id,
            is_required=True
        )
        
        # 准备测试数据
        test_data = [
            {
                "unique_value": unique_value,
                "2024-01-01": {
                    "CPU使用率": 85.5
                },
                "2024-01-02": {
                    "CPU使用率": 92.3
                }
            }
        ]
        
        # 执行导入
        result = LoadDataManager.batch_import(
            type_id=ci[0]['_type'],
            data=test_data,
        )
        
        # 验证结果
        assert result["total"] == 1
        assert "history_id" in result
        assert result.get("batch_size") == 5000
        
        # 等待异步任务完成
        history = LoadDataImportHistory.get_by_id(result["history_id"])
        import time
        time.sleep(3)
        assert history is not None
        assert history.status == HistoryStatusEnum.COMPLETED
        
        # 验证导入的数据
        query_result = LoadDataManager.query_data(
            type_id=ci[0]['_type'],
            ci_ids=[ci[0]['_id']],
            attribute_ids=[attr.id],
            start_period="2024-01-01",
            end_period="2024-01-02"
        )
        query_result = query_result['data']
        assert str(unique_value) in query_result
        assert query_result[str(unique_value)]["2024-01-01"]["cpu_usage"] == 85.5
        assert query_result[str(unique_value)]["2024-01-02"]["cpu_usage"] == 92.3

    
    def test_batch_import_invalid_data(self, db_session, app_context, request_context, auth_user, celery_worker):
        """测试导入无效数据的场景"""
        ci = init_ci(1, auth_user)
        unique_value = ci[0][ci[0]['unique']]
        
        attr = LoadAttrManager.add_attr(
            name="memory_usage",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="内存使用率",
        )
        
        LoadRelationsManager.add_type_attr(
            type_id=1,
            attr_id=attr.id
        )
        
        # 准备包含无效数据的测试数据
        test_data = [
            {
                "unique_value": unique_value,
                "2024-01-01": {
                    "memory_usage": "invalid_number"
                },
                "invalid_date": {
                    "memory_usage": 75.5
                },
                "2024-01-03": {
                    "memory_usage": 88.5
                }
            }
        ]
        
        result = LoadDataManager.batch_import(1, test_data)
        
        # 验证结果
        assert result["total"] == 1
        assert "history_id" in result
        assert result.get("batch_size") == 5000
        
        # 等待异步任务完成
        history = LoadDataImportHistory.get_by_id(result["history_id"])
        assert history is not None
        assert history.status == HistoryStatusEnum.FAILED
        assert history.error_count > 0
    
    def test_batch_import_missing_required_fields(self, db_session, app_context, request_context, auth_user, celery_worker):
        """测试缺少必填字段的场景"""
        logger.info("开始执行 test_batch_import_missing_required_fields 测试")
        try:
            ci = init_ci(1, auth_user)
            logger.info(f"CI实例初始化完成: {ci}")
            
            if ci and len(ci) > 0:
                logger.info(f"CI类型信息: type_id={ci[0]['_type']}, name={ci[0].get('type_name')}")
                logger.info(f"CI实例信息: ci_id={ci[0]['_id']}, unique_value={ci[0][ci[0]['unique']]}")
            else:
                logger.error("CI实例初始化失败或返回为空")
                raise RuntimeError("CI实例初始化失败")

            # 创建测试属性
            logger.info("开始创建测试属性...")
            attr = LoadAttrManager.add_attr(
                name="disk_usage",
                value_type=LoadValueTypeEnum.FLOAT,
                alias="磁盘使用率",
            )
            logger.info(f"测试属性创建完成: id={attr.id}, name={attr.name}")
            
            # 添加属性到CI类型
            logger.info(f"开始添加属性到CI类型: type_id={ci[0]['_type']}, attr_id={attr.id}")
            LoadRelationsManager.add_type_attr(
                type_id=ci[0]['_type'],
                attr_id=attr.id,
            )
            logger.info("属性添加到CI类型完成")
            
            # 准备测试数据
            unique_value = ci[0][ci[0]['unique']]
            test_data = [
                {
                    "unique_value": unique_value,
                    "2024-01-01": {}
                },
                {
                    "2024-01-01": {
                        "disk_usage": 78.9
                    }
                }
            ]
            logger.info(f"准备测试数据: {test_data}")
            
            # 执行导入
            logger.info(f"开始执行数据导入: type_id={ci[0]['_type']}")
            result = LoadDataManager.batch_import(ci[0]['_type'], test_data)
            logger.info(f"数据导入结果: {result}")
            
            # 验证结果
            assert result["total"] == 2
            assert "history_id" in result
            assert result.get("batch_size") == 5000
            
            # 等待异步任务完成
            history = LoadDataImportHistory.get_by_id(result["history_id"])
            assert history is not None
            assert history.status == HistoryStatusEnum.FAILED
            assert history.error_count > 0
            
        except Exception as e:
            logger.error(f"测试执行过程中发生错误: {str(e)}")
            logger.error("错误详细信息:", exc_info=True)
            raise
        finally:
            logger.info("test_batch_import_missing_required_fields 测试结束")
    
    def test_batch_import_large_dataset(self, db_session, app_context, request_context, auth_user, celery_worker):
        """测试大数据量导入场景"""
        ci = init_ci(100, auth_user)
        
        attr = LoadAttrManager.add_attr(
            name="network_usage",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="网络使用率",
        )
        
        LoadRelationsManager.add_type_attr(
            type_id=1,
            attr_id=attr.id
        )
        
        # 生成大量测试数据
        test_data = [
            {
                "unique_value": ci[i][ci[i]['unique']],
                "2024-01-01": {
                    "network_usage": float(i % 100)
                }
            }
            for i in range(100)  # 生成100条数据
        ]
        
        result = LoadDataManager.batch_import(1, test_data)
        
        # 验证结果
        assert result["total"] == 100
        assert "history_id" in result
        assert result.get("batch_size") == 5000
        
        # 等待异步任务完成
        history = LoadDataImportHistory.get_by_id(result["history_id"])
        assert history is not None
        assert history.status == HistoryStatusEnum.COMPLETED
        assert history.success_count == 100
        assert history.error_count == 0
    
    def test_batch_import_multiple_attributes(self, db_session, app_context, request_context, auth_user, celery_worker):
        """测试多个属性同时导入的场景"""
        ci = init_ci(1, auth_user)
        unique_value = ci[0][ci[0]['unique']]
        
        # 创建多个测试属性
        cpu_attr = LoadAttrManager.add_attr(
            name="cpu_load",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="CPU负载",
        )
        
        mem_attr = LoadAttrManager.add_attr(
            name="mem_load",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="内存负载",
        )
        
        # 添加属性到CI类型
        LoadRelationsManager.add_type_attr(type_id=1, attr_id=cpu_attr.id)
        LoadRelationsManager.add_type_attr(type_id=1, attr_id=mem_attr.id)
        
        # 准备包含多个属性的测试数据
        test_data = [
            {
                "unique_value": unique_value,
                "2024-01-01": {
                    "cpu_load": 75.5,
                    "mem_load": 82.0
                },
                "2024-01-02": {
                    "cpu_load": 65.8,
                    "mem_load": 91.0
                }
            }
        ]
        
        result = LoadDataManager.batch_import(1, test_data)
        
        # 验证结果
        assert result["total"] == 1
        assert "history_id" in result
        assert result.get("batch_size") == 5000
        
        # 等待异步任务完成
        history = LoadDataImportHistory.get_by_id(result["history_id"])
        assert history is not None
        assert history.status == HistoryStatusEnum.COMPLETED
        assert history.error_count == 0
        
        # 验证导入的数据
        query_result = LoadDataManager.query_data(
            type_id=ci[0]['_type'],
            ci_ids=[ci[0]['_id']],
            attribute_ids=[cpu_attr.id, mem_attr.id],
            start_period="2024-01-01",
            end_period="2024-01-02"
        )
        query_result = query_result['data']
        unique_value = str(unique_value)
        assert str(unique_value) in query_result
        assert query_result[unique_value]["2024-01-01"]["cpu_load"] == 75.5
        assert query_result[unique_value]["2024-01-01"]["mem_load"] == 82.0
        assert query_result[unique_value]["2024-01-02"]["cpu_load"] == 65.8
        assert query_result[unique_value]["2024-01-02"]["mem_load"] == 91.0
    
    def test_query_data_by_ci_ids(self, db_session, app_context, request_context, auth_user):
        """测试通过CI IDs查询负载数据"""
        ci = init_ci(2, auth_user)  # 创建2个CI实例
        ci_ids = [c['_id'] for c in ci]
        
        # 创建测试属性
        cpu_attr = LoadAttrManager.add_attr(
            name="cpu_usage",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="CPU使用率",
        )
        mem_attr = LoadAttrManager.add_attr(
            name="memory_usage",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="内存使用率",
        )
        
        # 添加属性到CI类型
        LoadRelationsManager.add_type_attr(type_id=1, attr_id=cpu_attr.id)
        LoadRelationsManager.add_type_attr(type_id=1, attr_id=mem_attr.id)
        
        # 导入测试数据
        test_data = []
        for ci_item in ci:
            test_data.append({
                "unique_value": ci_item[ci_item['unique']],
                "2024-01-01": {
                    "cpu_usage": 75.5,
                    "memory_usage": 80.0
                },
                "2024-01-02": {
                    "cpu_usage": 85.5,
                    "memory_usage": 90.0
                }
            })
        
        import_result = LoadDataManager.batch_import(1, test_data)
        
        # 验证导入结果
        assert import_result["total"] == len(test_data)
        assert "history_id" in import_result
        assert import_result.get("batch_size") == 5000
        
        # 执行查询
        result = LoadDataManager.query_data(
            type_id=1,
            ci_ids=ci_ids,
            attribute_ids=[cpu_attr.id, mem_attr.id],
            start_period="2024-01-01",
            end_period="2024-01-02",
            period_type=PeriodTypeEnum.DAILY
        )
        
        result = result['data']
        # 验证结果
        assert len(result) == 2  # 应该有2个CI的数据
        for ci_item in ci:
            ci_unique_value = ci_item[ci_item['unique']]
            ci_unique_value = str(ci_unique_value)
            assert ci_unique_value in result, f"CI ID {ci_unique_value} not found in result"
            assert "2024-01-01" in result[ci_unique_value], f"Date 2024-01-01 not found for CI {ci_unique_value}"
            assert "2024-01-02" in result[ci_unique_value], f"Date 2024-01-02 not found for CI {ci_unique_value}"
            
            # 验证每个日期下的属性
            for date in ["2024-01-01", "2024-01-02"]:
                data = result[ci_unique_value][date]
                assert "cpu_usage" in data, f"cpu_usage not found in {date} for CI {ci_unique_value}"
                assert "memory_usage" in data, f"memory_usage not found in {date} for CI {ci_unique_value}"
    
    def test_query_data_by_unique_values(self, db_session, app_context, request_context, auth_user):
        """测试通过unique_values查询负载数据"""
        ci = init_ci(2, auth_user)
        unique_values = [c[c['unique']] for c in ci]
        
        # 创建测试属性
        cpu_attr = LoadAttrManager.add_attr(
            name="cpu_load",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="CPU负载",
        )
        
        # 添加属性到CI类型
        LoadRelationsManager.add_type_attr(type_id=1, attr_id=cpu_attr.id)
        
        # 导入测试数据
        test_data = []
        for unique_value in unique_values:
            test_data.append({
                "unique_value": unique_value,
                "2024-01-01": {
                    "cpu_load": 75.5
                },
                "2024-01-02": {
                    "cpu_load": 85.5
                }
            })
        
        LoadDataManager.batch_import(1, test_data)
        
        # 执行查询
        result = LoadDataManager.query_data(
            unique_values=unique_values,
            type_id=1,
            attribute_ids=[cpu_attr.id]
        )
        result = result['data']
        # 验证结果
        assert len(result) == 2
        for unique_value in unique_values:
            assert str(unique_value) in result
            assert "2024-01-01" in result[str(unique_value)]
            assert "cpu_load" in result[str(unique_value)]["2024-01-01"]
            assert result[str(unique_value)]["2024-01-01"]["cpu_load"] == 75.5
    
    def test_query_data_invalid_params(self, db_session, app_context, request_context, auth_user):
        """测试无效参数场景"""
        ci = init_ci(1, auth_user)  # 这会创建一个CI类型和对应的CI实例
        type_id = ci[0]['_type']
        
        # 测试使用unique_values但未提供type_id
        with pytest.raises(TypeError) as exc:
            LoadDataManager.query_data(
                unique_values=["test"],
                attribute_ids=[1]
            )
        assert "missing 1 required positional argument: 'type_id'" in str(exc.value)
    
    def test_query_data_empty_result(self, db_session, app_context, request_context, auth_user):
        """测试查询结果为空的场景"""
        ci = init_ci(1, auth_user)
        # 测试不存在的CI IDs
        result = LoadDataManager.query_data(
            type_id=1,
            ci_ids=[999999],
            attribute_ids=[1]
        )
        assert result['data'] == {}
        
        # 测试不存在的属性IDs
        result = LoadDataManager.query_data(
            type_id=1,
            ci_ids=[ci[0]['_id']],
            attribute_ids=[999999]
        )
        assert result['data'] == {}
    
    def test_query_data_date_filter(self, db_session, app_context, request_context, auth_user):
        """测试日期过滤功能"""
        ci = init_ci(1, auth_user)
        unique_value = ci[0][ci[0]['unique']]
        
        # 创建测试属性
        attr = LoadAttrManager.add_attr(
            name="test_metric",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="测试指标",
        )
        
        LoadRelationsManager.add_type_attr(type_id=1, attr_id=attr.id)
        
        # 导入跨多个日期的测试数据
        test_data = [
            {
                "unique_value": unique_value,
                "2024-01-01": {
                    "test_metric": 75.5
                },
                "2024-01-15": {
                    "test_metric": 85.5
                },
                "2024-01-31": {
                    "test_metric": 95.5
                }
            }
        ]
        
        LoadDataManager.batch_import(1, test_data)
        
        # 测试日期范围过滤
        result = LoadDataManager.query_data(
            type_id=1,
            ci_ids=[ci[0]['_id']],
            attribute_ids=[attr.id],
            start_period="2024-01-10",
            end_period="2024-01-20"
        )
        result = result['data']
        # 验证结果
        assert len(result) == 1
        ci_data = result[str(unique_value)]
        assert len(ci_data) == 1  # 应该只有1月15日的数据
        assert "2024-01-15" in ci_data
        assert ci_data["2024-01-15"]["test_metric"] == 85.5
    
    def test_batch_import_mixed_period_data(self, db_session, app_context, request_context, auth_user, celery_worker):
        """测试同时导入日期和月度数据"""
        ci = init_ci(1, auth_user)
        unique_value = ci[0][ci[0]['unique']]
        
        # 创建日度属性
        daily_attr = LoadAttrManager.add_attr(
            name="daily_metric",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="日度指标"
        )
        
        # 创建月度属性
        monthly_attr = LoadAttrManager.add_attr(
            name="monthly_metric",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="月度指标",
            is_monthly=True
        )
        
        # 添加属性到CI类型
        LoadRelationsManager.add_type_attr(type_id=ci[0]['_type'], attr_id=daily_attr.id)
        LoadRelationsManager.add_type_attr(type_id=ci[0]['_type'], attr_id=monthly_attr.id)
        
        # 准备混合周期的测试数据
        test_data = [
            {
                "unique_value": unique_value,
                "2024-01-15": {
                    "daily_metric": 85.5
                },
                "2024-01": {
                    "monthly_metric": 1000.0
                }
            }
        ]
        
        # 执行导入
        result = LoadDataManager.batch_import(
            type_id=ci[0]['_type'],
            data=test_data
        )
        # 验证结果
        assert result["total"] == 1
        assert "history_id" in result
        assert result.get("batch_size") == 5000
        
        # 等待异步任务完成
        history = LoadDataImportHistory.get_by_id(result["history_id"])
        assert history is not None
        assert history.status == HistoryStatusEnum.COMPLETED
        assert history.error_count == 0
        
        # 验证导入的数据
        daily_result = LoadDataManager.query_data(
            type_id=ci[0]['_type'],
            ci_ids=[ci[0]['_id']],
            attribute_ids=[daily_attr.id],
            start_period="2024-01-15",
            end_period="2024-01-15"
        )
        
        monthly_result = LoadDataManager.query_data(
            type_id=ci[0]['_type'],
            unique_values=[unique_value],
            attribute_ids=[monthly_attr.id],
            start_period="2024-01",
            end_period="2024-01",
            period_type=PeriodTypeEnum.MONTHLY
        )
        daily_result = daily_result['data']
        monthly_result = monthly_result['data']
        
        assert str(unique_value) in daily_result
        assert daily_result[str(unique_value)]["2024-01-15"]["daily_metric"] == 85.5
        assert str(unique_value) in monthly_result
        assert monthly_result[str(unique_value)]["2024-01"]["monthly_metric"] == 1000.0
    
    def test_batch_import_monthly_data(self, db_session, app_context, request_context, auth_user, celery_worker):
        """测试月度数据导入"""
        ci = init_ci(1, auth_user)
        unique_value = ci[0][ci[0]['unique']]
        
        # 创建月度属性
        attr = LoadAttrManager.add_attr(
            name="monthly_cost",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="月度成本",
            is_monthly=True
        )
        
        LoadRelationsManager.add_type_attr(
            type_id=ci[0]['_type'],
            attr_id=attr.id
        )
        
        # 准备月度数据
        test_data = [
            {
                "unique_value": unique_value,
                "2024-01": {
                    "monthly_cost": 1000.0
                },
                "2024-02": {
                    "monthly_cost": 1200.0
                }
            }
        ]
        
        # 执行导入
        result = LoadDataManager.batch_import(
            type_id=ci[0]['_type'],
            data=test_data
        )
        
        # 验证结果
        assert result["total"] == 1
        assert "history_id" in result
        assert result.get("batch_size") == 5000
        
        # 等待异步任务完成
        history = LoadDataImportHistory.get_by_id(result["history_id"])
        assert history is not None
        assert history.status == HistoryStatusEnum.COMPLETED
        assert history.error_count == 0
        
        # 验证导入的数据
        query_result = LoadDataManager.query_data(
            type_id=ci[0]['_type'],
            ci_ids=[ci[0]['_id']],
            attribute_ids=[attr.id],
            start_period="2024-01",
            end_period="2024-02",
            period_type=PeriodTypeEnum.MONTHLY
        )
        query_result = query_result['data']
        assert str(unique_value) in query_result
        assert query_result[str(unique_value)]["2024-01"]["monthly_cost"] == 1000.0
        assert query_result[str(unique_value)]["2024-02"]["monthly_cost"] == 1200.0
    
    def test_batch_import_update_existing(self, db_session, app_context, request_context, auth_user, celery_worker):
        """测试更新已存在的数据"""
        ci = init_ci(1, auth_user)
        unique_value = ci[0][ci[0]['unique']]
        
        # 创建测试属性
        attr = LoadAttrManager.add_attr(
            name="cpu_usage",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="CPU使用率"
        )
        
        LoadRelationsManager.add_type_attr(
            type_id=ci[0]['_type'],
            attr_id=attr.id
        )
        
        # 第一次导入
        initial_data = [
            {
                "unique_value": unique_value,
                "2024-01-01": {
                    "cpu_usage": 80.0
                }
            }
        ]
        
        result1 = LoadDataManager.batch_import(
            type_id=ci[0]['_type'],
            data=initial_data
        )
        
        assert result1["total"] == 1
        assert "history_id" in result1
        
        # 等待第一次导入完成
        history1 = LoadDataImportHistory.get_by_id(result1["history_id"])
        assert history1 is not None
        assert history1.status == HistoryStatusEnum.COMPLETED
        assert history1.error_count == 0
        
        # 第二次导入相同日期的数据
        update_data = [
            {
                "unique_value": unique_value,
                "2024-01-01": {
                    "cpu_usage": 85.0
                }
            }
        ]
        
        result2 = LoadDataManager.batch_import(
            type_id=ci[0]['_type'],
            data=update_data
        )
        
        assert result2["total"] == 1
        assert "history_id" in result2
        
        # 等待第二次导入完成
        history2 = LoadDataImportHistory.get_by_id(result2["history_id"])
        assert history2 is not None
        assert history2.status == HistoryStatusEnum.COMPLETED
        assert history2.error_count == 0
        
        # 验证数据已更新
        query_result = LoadDataManager.query_data(
            type_id=ci[0]['_type'],
            ci_ids=[ci[0]['_id']],
            attribute_ids=[attr.id],
            start_period="2024-01-01",
            end_period="2024-01-01"
        )
        query_result = query_result['data']
        
        assert query_result[str(unique_value)]["2024-01-01"]["cpu_usage"] == 85.0

    def test_query_data_with_empty_first_attr(self, db_session, app_context, request_context, auth_user, celery_worker):
        """测试第一个属性没有数据但第二个属性有数据的情况"""
        # 初始化CI
        ci = init_ci(1, auth_user)
        unique_value = ci[0][ci[0]['unique']]
        
        # 创建两个测试属性
        first_attr = LoadAttrManager.add_attr(
            name="first_attr",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="第一个属性",
        )
        
        second_attr = LoadAttrManager.add_attr(
            name="second_attr",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="第二个属性",
        )
        
        # 添加属性到CI类型
        LoadRelationsManager.add_type_attr(type_id=ci[0]['_type'], attr_id=first_attr.id)
        LoadRelationsManager.add_type_attr(type_id=ci[0]['_type'], attr_id=second_attr.id)
        
        # 只为第二个属性导入数据
        test_data = [
            {
                "unique_value": unique_value,
                "2024-01-01": {
                    "second_attr": 100.0
                },
                "2024-01-02": {
                    "second_attr": 200.0
                }
            }
        ]
        
        # 执行导入
        result = LoadDataManager.batch_import(
            type_id=ci[0]['_type'],
            data=test_data
        )
        
        # 验证导入结果
        assert result["total"] == 1
        assert "history_id" in result
        
        # 等待异步任务完成
        history = LoadDataImportHistory.get_by_id(result["history_id"])
        assert history is not None
        assert history.status == HistoryStatusEnum.COMPLETED
        assert history.error_count == 0
        
        # 查询两个属性的数据
        query_result = LoadDataManager.query_data(
            type_id=ci[0]['_type'],
            ci_ids=[ci[0]['_id']],
            attribute_ids=[first_attr.id, second_attr.id],
            start_period="2024-01-01",
            end_period="2024-01-02"
        )
        
        # 未修改代码应该返回空结果，修改后应正常返回第二个属性的数据
        assert "data" in query_result
        data = query_result["data"]
        
        # 当query_data已修改时，这些断言应该通过
        assert str(unique_value) in data
        assert "2024-01-01" in data[str(unique_value)]
        assert "2024-01-02" in data[str(unique_value)]
        assert "second_attr" in data[str(unique_value)]["2024-01-01"]
        assert data[str(unique_value)]["2024-01-01"]["second_attr"] == 100.0
        assert data[str(unique_value)]["2024-01-02"]["second_attr"] == 200.0
        assert "first_attr" not in data[str(unique_value)]["2024-01-01"]
        assert "first_attr" not in data[str(unique_value)]["2024-01-02"]

    def test_query_data_performance_with_large_dataset(self, db_session, app_context, request_context, auth_user, celery_worker):
        """测试大数据量下的查询性能"""
        import time
        
        # 创建多个CI
        num_cis = 100
        ci_list = init_ci(num_cis, auth_user)
        
        # 创建多个测试属性
        attrs = []
        attr_names = ["cpu_usage", "memory_usage", "disk_usage", "network_in", "network_out"]
        for attr_name in attr_names:
            attr = LoadAttrManager.add_attr(
                name=attr_name,
                value_type=LoadValueTypeEnum.FLOAT,
                alias=f"{attr_name}指标"
            )
            attrs.append(attr)
            # 添加属性到CI类型
            LoadRelationsManager.add_type_attr(type_id=1, attr_id=attr.id)
        
        # 生成测试数据：每个CI有多个属性和多个日期的数据
        dates = [f"2024-01-{day:02d}" for day in range(1, 30)]  # 30天的数据
        test_data = []
        
        for ci_item in ci_list:
            ci_data = {
                "unique_value": ci_item[ci_item['unique']]
            }
            
            # 为每个日期添加数据
            for date in dates:
                ci_data[date] = {}
                for idx, attr in enumerate(attrs):
                    # 生成不同的值，确保数据多样性
                    value = float(f"{idx + 1}.{int(ci_item['_id']) % 100}")
                    ci_data[date][attr.name] = value * 10
            
            test_data.append(ci_data)
        
        # 批量导入数据
        result = LoadDataManager.batch_import(1, test_data)
        
        # 等待异步任务完成
        history = LoadDataImportHistory.get_by_id(result["history_id"])
        wait_count = 0
        while history.status not in [HistoryStatusEnum.COMPLETED, HistoryStatusEnum.FAILED] and wait_count < 10:
            time.sleep(1)
            history = LoadDataImportHistory.get_by_id(result["history_id"])
            wait_count += 1
            
        assert history.status == HistoryStatusEnum.COMPLETED, f"导入失败: {history.error_message}"
        
        # 测试查询性能
        ci_ids = [ci['_id'] for ci in ci_list[:10]]  # 取前10个CI
        attr_ids = [attr.id for attr in attrs]
        
        # 记录查询开始时间
        start_time = time.time()
        
        # 执行查询
        query_result = LoadDataManager.query_data(
            type_id=1,
            ci_ids=ci_ids,
            attribute_ids=attr_ids,
            start_period="2024-01-05",
            end_period="2024-01-15"
        )
        
        # 记录查询结束时间
        end_time = time.time()
        query_time = end_time - start_time
        
        # 日志记录查询时间
        current_app.logger.info(f"查询 {len(ci_ids)} 个CI的 {len(attr_ids)} 个属性，共 {len(dates)} 天的数据，耗时: {query_time:.4f} 秒")
        # 验证结果
        assert 'total' in query_result
        assert 'data' in query_result
        assert len(query_result['data']) <= len(ci_ids)  # 不应超过请求的CI数量
        
        # 检查一些数据点
        for ci_item in ci_list[:10]:
            unique_value = str(ci_item[ci_item['unique']])
            if unique_value in query_result['data']:
                # 检查日期范围
                dates_in_result = list(query_result['data'][unique_value].keys())
                # 确保日期在请求的范围内
                for date in dates_in_result:
                    assert "2024-01-05" <= date <= "2024-01-15"
                
                # 检查属性
                for date in dates_in_result:
                    data_point = query_result['data'][unique_value][date]
                    # 验证属性存在
                    for attr in attrs:
                        if attr.name in data_point:
                            # 验证值类型
                            assert isinstance(data_point[attr.name], float)

        # 新增：不指定ci_ids，分页遍历所有数据
        total_records = None
        page = 1
        page_size = 1000
        all_data_count = 0
        start_time_all = time.time()
        while True:
            page_result = LoadDataManager.query_data(
                type_id=1,
                attribute_ids=attr_ids,
                start_period="2024-01-05",
                end_period="2024-01-15",
                page=page,
                page_size=page_size
            )
            if total_records is None:
                total_records = page_result.get('total', 0)
            data = page_result.get('data', {})
            # 统计本页数据量
            all_data_count += sum(len(periods) for periods in data.values())
            if len(data) == 0 or (page * page_size) >= total_records:
                break
            page += 1
        end_time_all = time.time()
        total_time = end_time_all - start_time_all
        current_app.logger.info(f"[全量遍历] 查询所有CI的 {len(attr_ids)} 个属性，分页遍历耗时: {total_time:.4f} 秒，总数据点: {all_data_count}")
        # 验证遍历数量合理
        assert all_data_count > 0


class TestTimeSlotDataManager:
    """时间段功能业务逻辑测试类"""

    def test_time_slot_validation(self, db_session, app_context):
        """测试时间段验证功能"""
        from api.lib.cmdb.load.utils import TimeSlotValidator
        from api.lib.cmdb.load.const import TimeSlotEnum
        
        # 测试有效时间段
        assert TimeSlotValidator.validate_and_normalize("00") == "00"
        assert TimeSlotValidator.validate_and_normalize("08") == "08"
        
        # 测试默认值
        assert TimeSlotValidator.validate_and_normalize(None) == TimeSlotEnum.DEFAULT_SLOT
        assert TimeSlotValidator.validate_and_normalize("") == TimeSlotEnum.DEFAULT_SLOT
        
        # 测试无效时间段
        with pytest.raises(Exception):
            TimeSlotValidator.validate_and_normalize("99")
        
        with pytest.raises(Exception):
            TimeSlotValidator.validate_and_normalize("invalid")

    def test_time_slot_enum_methods(self, db_session, app_context):
        """测试时间段枚举方法"""
        from api.lib.cmdb.load.const import TimeSlotEnum
        
        # 测试描述获取
        assert TimeSlotEnum.get_slot_description("00") == "闲时(00:00-08:00)"
        assert TimeSlotEnum.get_slot_description("08") == "忙时(08:00-18:00)"
        assert TimeSlotEnum.get_slot_description("99") == "未知时段"
        
        # 测试所有时间段信息
        all_slots = TimeSlotEnum.get_all_slots_info()
        assert len(all_slots) == 2
        assert any(slot['value'] == '00' and slot['label'] == '闲时' for slot in all_slots)
        assert any(slot['value'] == '08' and slot['label'] == '忙时' for slot in all_slots)
        
        # 测试验证方法
        assert TimeSlotEnum.validate_slot("00") is True
        assert TimeSlotEnum.validate_slot("08") is True
        assert TimeSlotEnum.validate_slot("99") is False

    def test_batch_import_with_time_slot(self, db_session, app_context, request_context, auth_user, celery_worker):
        """测试带时间段的批量导入"""
        ci = init_ci(1, auth_user)
        unique_value = ci[0][ci[0]['unique']]
        
        # 创建测试属性
        attr = LoadAttrManager.add_attr(
            name="cpu_usage_time_slot",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="CPU使用率(时段)"
        )
        
        LoadRelationsManager.add_type_attr(
            type_id=ci[0]['_type'],
            attr_id=attr.id
        )
        
        # 准备忙时数据
        test_data = [
            {
                "unique_value": unique_value,
                "2024-01-01": {
                    "cpu_usage_time_slot": 85.5
                },
                "2024-01-02": {
                    "cpu_usage_time_slot": 92.3
                }
            }
        ]
        
        # 执行忙时导入
        result = LoadDataManager.batch_import(
            type_id=ci[0]['_type'],
            data=test_data,
            time_slot="08"
        )
        
        # 验证结果
        assert result["total"] == 1
        assert "history_id" in result
        assert result.get("time_slot") == "08"
        assert result.get("time_slot_description") == "忙时(08:00-18:00)"
        
        # 等待异步任务完成
        history = LoadDataImportHistory.get_by_id(result["history_id"])
        import time
        time.sleep(3)
        assert history is not None
        assert history.status == HistoryStatusEnum.COMPLETED

    def test_batch_import_idle_time_slot(self, db_session, app_context, request_context, auth_user, celery_worker):
        """测试闲时数据导入"""
        ci = init_ci(1, auth_user)
        unique_value = ci[0][ci[0]['unique']]
        
        attr = LoadAttrManager.add_attr(
            name="memory_usage_idle",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="内存使用率(闲时)"
        )
        
        LoadRelationsManager.add_type_attr(
            type_id=ci[0]['_type'],
            attr_id=attr.id
        )
        
        # 准备闲时数据
        test_data = [
            {
                "unique_value": unique_value,
                "2024-01-01": {
                    "memory_usage_idle": 45.5
                }
            }
        ]
        
        # 执行闲时导入
        result = LoadDataManager.batch_import(
            type_id=ci[0]['_type'],
            data=test_data,
            time_slot="00"
        )
        
        assert result.get("time_slot") == "00"
        assert result.get("time_slot_description") == "闲时(00:00-08:00)"
        
        # 等待异步任务完成
        history = LoadDataImportHistory.get_by_id(result["history_id"])
        import time
        time.sleep(3)
        assert history.status == HistoryStatusEnum.COMPLETED

    def test_query_data_with_time_slot_filter(self, db_session, app_context, request_context, auth_user, celery_worker):
        """测试带时间段过滤的数据查询"""
        ci = init_ci(1, auth_user)
        unique_value = ci[0][ci[0]['unique']]
        
        # 创建测试属性
        attr = LoadAttrManager.add_attr(
            name="cpu_load_slot",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="CPU负载(时段)"
        )
        
        LoadRelationsManager.add_type_attr(
            type_id=ci[0]['_type'],
            attr_id=attr.id
        )
        
        # 分别导入忙时和闲时数据
        busy_data = [
            {
                "unique_value": unique_value,
                "2024-01-01": {
                    "cpu_load_slot": 85.5
                }
            }
        ]
        
        idle_data = [
            {
                "unique_value": unique_value,
                "2024-01-01": {
                    "cpu_load_slot": 45.5
                }
            }
        ]
        
        # 导入忙时数据
        LoadDataManager.batch_import(
            type_id=ci[0]['_type'],
            data=busy_data,
            time_slot="08"
        )
        
        # 导入闲时数据
        LoadDataManager.batch_import(
            type_id=ci[0]['_type'],
            data=idle_data,
            time_slot="00"
        )
        
        import time
        time.sleep(3)  # 等待异步任务完成
        
        # 查询忙时数据
        busy_result = LoadDataManager.query_data(
            type_id=ci[0]['_type'],
            ci_ids=[ci[0]['_id']],
            attribute_ids=[attr.id],
            start_period="2024-01-01",
            end_period="2024-01-01",
            time_slot="08"
        )
        
        # 查询闲时数据
        idle_result = LoadDataManager.query_data(
            type_id=ci[0]['_type'],
            ci_ids=[ci[0]['_id']],
            attribute_ids=[attr.id],
            start_period="2024-01-01",
            end_period="2024-01-01",
            time_slot="00"
        )
        
        # 验证结果包含时间段信息
        assert busy_result.get("time_slot") == "08"
        assert busy_result.get("time_slot_description") == "忙时(08:00-18:00)"
        assert idle_result.get("time_slot") == "00"
        assert idle_result.get("time_slot_description") == "闲时(00:00-08:00)"
        
        # 验证数据区分
        busy_data_result = busy_result['data']
        idle_data_result = idle_result['data']
        
        assert str(unique_value) in busy_data_result
        assert str(unique_value) in idle_data_result
        
        # 验证不同时间段的数据值不同
        assert busy_data_result[str(unique_value)]["2024-01-01"]["cpu_load_slot"] == 85.5
        assert idle_data_result[str(unique_value)]["2024-01-01"]["cpu_load_slot"] == 45.5

    def test_monthly_data_ignores_time_slot(self, db_session, app_context, request_context, auth_user, celery_worker):
        """测试月度数据忽略时间段"""
        ci = init_ci(1, auth_user)
        unique_value = ci[0][ci[0]['unique']]
        
        # 创建月度属性
        attr = LoadAttrManager.add_attr(
            name="monthly_cost_slot",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="月度成本(时段测试)",
            is_monthly=True
        )
        
        LoadRelationsManager.add_type_attr(
            type_id=ci[0]['_type'],
            attr_id=attr.id
        )
        
        # 准备月度数据，传入时间段参数
        test_data = [
            {
                "unique_value": unique_value,
                "2024-01": {
                    "monthly_cost_slot": 1000.0
                }
            }
        ]
        
        # 执行导入（月度数据应该忽略时间段参数）
        result = LoadDataManager.batch_import(
            type_id=ci[0]['_type'],
            data=test_data,
            time_slot="08"  # 这个参数对月度数据应该无效
        )
        
        # 等待异步任务完成
        history = LoadDataImportHistory.get_by_id(result["history_id"])
        import time
        time.sleep(3)
        assert history.status == HistoryStatusEnum.COMPLETED
        
        # 验证导入的数据（不应该包含时间段字段）
        query_result = LoadDataManager.query_data(
            type_id=ci[0]['_type'],
            ci_ids=[ci[0]['_id']],
            attribute_ids=[attr.id],
            start_period="2024-01",
            end_period="2024-01",
            period_type=PeriodTypeEnum.MONTHLY
        )
        
        query_result = query_result['data']
        assert str(unique_value) in query_result
        assert query_result[str(unique_value)]["2024-01"]["monthly_cost_slot"] == 1000.0

    def test_validate_data_with_time_slot(self, db_session, app_context, request_context, auth_user):
        """测试带时间段的数据验证"""
        ci = init_ci(1, auth_user)
        unique_value = ci[0][ci[0]['unique']]
        
        # 创建测试属性
        attr = LoadAttrManager.add_attr(
            name="test_metric_slot",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="测试指标(时段)"
        )
        
        LoadRelationsManager.add_type_attr(
            type_id=ci[0]['_type'],
            attr_id=attr.id
        )
        
        # 准备测试数据
        test_data = [
            {
                "unique_value": unique_value,
                "2024-01-01": {
                    "test_metric_slot": 85.5
                }
            }
        ]
        
        # 测试带时间段的数据验证
        valid_records, invalid_records = LoadDataManager.validate_data(
            type_id=ci[0]['_type'],
            data=test_data,
            time_slot="08"
        )
        
        # 验证结果
        assert len(invalid_records) == 0
        assert len(valid_records) == 1
        
        # 验证记录包含时间段字段
        record = valid_records[0]
        assert record['time_slot'] == "08"
        assert record['ci_id'] == ci[0]['_id']
        assert record['load_attr_id'] == attr.id
        assert record['value'] == 85.5
        assert record['collect_date'] is not None
        assert record['collect_month'] is None

    def test_validate_data_without_time_slot(self, db_session, app_context, request_context, auth_user):
        """测试不传时间段的数据验证（向后兼容性）"""
        ci = init_ci(1, auth_user)
        unique_value = ci[0][ci[0]['unique']]
        
        # 创建测试属性
        attr = LoadAttrManager.add_attr(
            name="test_metric_compat",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="测试指标(兼容性)"
        )
        
        LoadRelationsManager.add_type_attr(
            type_id=ci[0]['_type'],
            attr_id=attr.id
        )
        
        # 准备测试数据
        test_data = [
            {
                "unique_value": unique_value,
                "2024-01-01": {
                    "test_metric_compat": 75.5
                }
            }
        ]
        
        # 测试不传时间段的数据验证
        valid_records, invalid_records = LoadDataManager.validate_data(
            type_id=ci[0]['_type'],
            data=test_data
            # 不传 time_slot 参数
        )
        
        # 验证结果
        assert len(invalid_records) == 0
        assert len(valid_records) == 1
        
        # 验证记录包含默认时间段字段
        record = valid_records[0]
        assert record['time_slot'] == "08"  # 应该使用默认值
        assert record['ci_id'] == ci[0]['_id']
        assert record['load_attr_id'] == attr.id
        assert record['value'] == 75.5

    def test_mixed_daily_monthly_with_time_slot(self, db_session, app_context, request_context, auth_user, celery_worker):
        """测试混合日度和月度数据的时间段处理"""
        ci = init_ci(1, auth_user)
        unique_value = ci[0][ci[0]['unique']]
        
        # 创建日度和月度属性
        daily_attr = LoadAttrManager.add_attr(
            name="daily_cpu_slot",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="日度CPU(时段)"
        )
        
        monthly_attr = LoadAttrManager.add_attr(
            name="monthly_cost_mixed",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="月度成本(混合测试)",
            is_monthly=True
        )
        
        LoadRelationsManager.add_type_attr(type_id=ci[0]['_type'], attr_id=daily_attr.id)
        LoadRelationsManager.add_type_attr(type_id=ci[0]['_type'], attr_id=monthly_attr.id)
        
        # 准备混合数据
        test_data = [
            {
                "unique_value": unique_value,
                "2024-01-01": {
                    "daily_cpu_slot": 85.5
                },
                "2024-01": {
                    "monthly_cost_mixed": 1000.0
                }
            }
        ]
        
        # 执行导入
        result = LoadDataManager.batch_import(
            type_id=ci[0]['_type'],
            data=test_data,
            time_slot="00"  # 闲时
        )
        
        # 等待异步任务完成
        history = LoadDataImportHistory.get_by_id(result["history_id"])
        import time
        time.sleep(3)
        assert history.status == HistoryStatusEnum.COMPLETED
        
        # 验证日度数据包含时间段
        daily_result = LoadDataManager.query_data(
            type_id=ci[0]['_type'],
            ci_ids=[ci[0]['_id']],
            attribute_ids=[daily_attr.id],
            start_period="2024-01-01",
            end_period="2024-01-01",
            time_slot="00"
        )
        
        # 验证月度数据不受时间段影响
        monthly_result = LoadDataManager.query_data(
            type_id=ci[0]['_type'],
            ci_ids=[ci[0]['_id']],
            attribute_ids=[monthly_attr.id],
            start_period="2024-01",
            end_period="2024-01",
            period_type=PeriodTypeEnum.MONTHLY
        )
        
        daily_data = daily_result['data']
        monthly_data = monthly_result['data']
        
        assert str(unique_value) in daily_data
        assert str(unique_value) in monthly_data
        assert daily_data[str(unique_value)]["2024-01-01"]["daily_cpu_slot"] == 85.5
        assert monthly_data[str(unique_value)]["2024-01"]["monthly_cost_mixed"] == 1000.0

    def test_batch_import_invalid_time_slot(self, db_session, app_context, request_context, auth_user):
        """测试使用无效时间段的批量导入"""
        ci = init_ci(1, auth_user)
        unique_value = ci[0][ci[0]['unique']]
        
        attr = LoadAttrManager.add_attr(
            name="invalid_slot_test",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="无效时间段测试"
        )
        
        LoadRelationsManager.add_type_attr(
            type_id=ci[0]['_type'],
            attr_id=attr.id
        )
        
        test_data = [
            {
                "unique_value": unique_value,
                "2024-01-01": {
                    "invalid_slot_test": 85.5
                }
            }
        ]
        
        # 测试无效时间段
        with pytest.raises(Exception) as exc:
            LoadDataManager.batch_import(
                type_id=ci[0]['_type'],
                data=test_data,
                time_slot="invalid"
            )
        assert "无效的时间段" in str(exc.value)

    def test_query_data_invalid_time_slot(self, db_session, app_context, request_context, auth_user):
        """测试使用无效时间段的数据查询"""
        ci = init_ci(1, auth_user)
        
        attr = LoadAttrManager.add_attr(
            name="query_invalid_slot",
            value_type=LoadValueTypeEnum.FLOAT,
            alias="查询无效时间段测试"
        )
        
        LoadRelationsManager.add_type_attr(
            type_id=ci[0]['_type'],
            attr_id=attr.id
        )
        
        # 测试无效时间段查询
        with pytest.raises(Exception) as exc:
            LoadDataManager.query_data(
                type_id=ci[0]['_type'],
                ci_ids=[ci[0]['_id']],
                attribute_ids=[attr.id],
                time_slot="999"
            )
        assert "无效的时间段" in str(exc.value)
