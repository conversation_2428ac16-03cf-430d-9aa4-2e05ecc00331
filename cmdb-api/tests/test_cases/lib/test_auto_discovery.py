# -*- coding:utf-8 -*-
"""测试auto_discovery.py中的函数"""

import pytest
import logging
import json
from api.lib.cmdb.auto_discovery.auto_discovery import parse_prefect_metadata, check_prefect_metadata, AutoDiscoveryRuleCRUD
from api.lib.cmdb.auto_discovery.prefect_manager import PrefectManager
from api.lib.cmdb.const import AutoDiscoveryType
from api.lib.cmdb.resp_format import ErrFormat

logger = logging.getLogger(__name__)

# 测试数据常量
VALID_METADATA = {
    "status": "COMPLETED",
    "metadata": {
        "unique_key": "instance_id",
        "attributes": [
            ("instance_id", "String", "实例的唯一ID"),
            ("hostname", "String", "主机名"),
            ("ip_address", "String", "主IP地址"),
            ("os_version", "String", "操作系统版本")
        ]
    }
}

BASIC_PLUGIN_SCRIPT = {
    "flow_name": "test_flow",
    "entrypoint": "example_cvm.py:my_discovery_flow",
    "path_to_flow": "/app/src/flow/cmdb/",
    "flow_script": "example_cvm.py",
    "flow_function": "my_discovery_flow",
    "work_pool_name": "default-agent-pool",
    "tags": ["testing"]
}

BASIC_OPTION = {
    "icon": {
        "name": "caise-chajian",
        "color": ""
    }
}

@pytest.fixture
def basic_rule_data():
    """基础规则数据fixture"""
    return {
        "name": "test_prefect_rule",
        "type": AutoDiscoveryType.PREFECT,
        "is_plugin": True,
        "option": BASIC_OPTION,
        "plugin_script": json.dumps(BASIC_PLUGIN_SCRIPT)
    }

class TestPrefectMetadataParsing:
    """测试Prefect元数据解析相关功能"""
    
    def test_parse_valid_metadata(self):
        """测试解析有效的Prefect元数据"""
        unique_key, attributes = parse_prefect_metadata(VALID_METADATA)
        assert unique_key == "instance_id"
        assert len(attributes) == 4
        assert attributes[0]["name"] == "instance_id"
        assert attributes[0]["type"] == "String"
        assert attributes[0]["desc"] == "实例的唯一ID"

    def test_parse_invalid_status(self):
        """测试解析状态无效的元数据"""
        invalid_metadata = dict(VALID_METADATA)
        invalid_metadata["status"] = "FAILED"
        with pytest.raises(Exception, match="Flow返回值无效或执行未完成"):
            parse_prefect_metadata(invalid_metadata)

    def test_parse_missing_unique_key(self):
        """测试解析缺少unique_key的元数据"""
        invalid_metadata = {
            "status": "COMPLETED",
            "metadata": {"attributes": []}
        }
        with pytest.raises(Exception, match="Flow返回值必须包含unique_key字段"):
            parse_prefect_metadata(invalid_metadata)

    def test_parse_invalid_attributes_format(self):
        """测试解析attributes格式错误的元数据"""
        invalid_metadata = {
            "status": "COMPLETED",
            "metadata": {
                "unique_key": "test_key",
                "attributes": "not_a_list"
            }
        }
        with pytest.raises(Exception, match="Flow返回值必须包含attributes列表"):
            parse_prefect_metadata(invalid_metadata)

class TestPrefectMetadataCheck:
    """测试Prefect元数据检查相关功能"""

    def test_check_missing_plugin_script(self):
        """测试检查缺少plugin_script的情况"""
        with pytest.raises(Exception, match="Prefect规则必须提供plugin_script"):
            check_prefect_metadata(option={}, plugin_script={})

    def test_check_missing_flow_name(self):
        """测试检查缺少flow_name的情况"""
        invalid_script = {"entrypoint": "test_flow.py:my_discovery_flow"}
        with pytest.raises(Exception, match="检查Prefect元数据时发生错误"):
            check_prefect_metadata(option={}, plugin_script=invalid_script)

    def test_check_missing_entrypoint(self):
        """测试检查缺少entrypoint的情况"""
        invalid_script = {"flow_name": "test_flow"}
        with pytest.raises(Exception, match="检查Prefect元数据时发生错误"):
            check_prefect_metadata(option={}, plugin_script=invalid_script)

    def test_check_valid_metadata(self):
        """测试检查有效的元数据"""
        result = check_prefect_metadata(option=BASIC_OPTION, plugin_script=json.dumps(BASIC_PLUGIN_SCRIPT))
        assert result["unique_key"] == "instance_id"
        assert len(result["attributes"]) == 4
        assert result["attributes"][0]["name"] == "instance_id"

class TestAutoDiscoveryRuleCRUD:
    """测试AutoDiscoveryRuleCRUD的功能"""

    def test_add_rule_success(self, db_session, app_context, request_context, auth_user, basic_rule_data):
        """测试成功添加规则"""
        auth_user()
        rule = AutoDiscoveryRuleCRUD().add(**basic_rule_data)
        
        assert rule.name == basic_rule_data["name"]
        assert rule.type == AutoDiscoveryType.PREFECT
        assert rule.is_plugin is True
        
        rule_plugin_script = json.loads(rule.plugin_script)
        assert rule_plugin_script["flow_name"] == BASIC_PLUGIN_SCRIPT["flow_name"]
        assert rule_plugin_script["entrypoint"] == BASIC_PLUGIN_SCRIPT["entrypoint"]

    def test_add_duplicate_rule(self, db_session, app_context, request_context, auth_user, basic_rule_data):
        """测试添加重复规则"""
        auth_user()
        AutoDiscoveryRuleCRUD().add(**basic_rule_data)
        
        with pytest.raises(Exception) as exc_info:
            AutoDiscoveryRuleCRUD().add(**basic_rule_data)
        assert ErrFormat.adr_duplicate.format(basic_rule_data["name"]) in str(exc_info.value)

    def test_update_rule_success(self, db_session, app_context, request_context, auth_user, basic_rule_data):
        """测试成功更新规则"""
        auth_user()
        rule = AutoDiscoveryRuleCRUD().add(**basic_rule_data)
        
        updated_plugin_script = dict(BASIC_PLUGIN_SCRIPT)
        updated_plugin_script.update({
            "flow_name": "updated_flow",
        })
        
        update_data = {
            **basic_rule_data,
            "plugin_script": json.dumps(updated_plugin_script)
        }
        
        updated_rule = AutoDiscoveryRuleCRUD().update(rule.id, **update_data)
        update_data = json.loads(updated_rule.plugin_script)
        assert update_data["flow_name"] == "updated_flow"

    def test_update_rule_invalid_script(self, db_session, app_context, request_context, auth_user, basic_rule_data):
        """测试更新规则时使用无效的脚本"""
        auth_user()
        rule = AutoDiscoveryRuleCRUD().add(**basic_rule_data)
        
        invalid_update = {
            "is_plugin": True,
            "type": AutoDiscoveryType.PREFECT,
            "plugin_script": json.dumps({"flow_name": "test_flow"})  # 缺少entrypoint
        }
        
        with pytest.raises(Exception) as exc_info:
            AutoDiscoveryRuleCRUD().update(rule.id, **invalid_update)
        assert "检查Prefect元数据时发生错误" in str(exc_info.value)

    def test_delete_rule_success(self, db_session, app_context, request_context, auth_user, basic_rule_data):
        """测试成功删除规则"""
        auth_user()
        rule = AutoDiscoveryRuleCRUD().add(**basic_rule_data)
        
        deleted_rule = AutoDiscoveryRuleCRUD().delete(rule.id)
        assert deleted_rule.id == rule.id

    def test_delete_nonexistent_rule(self, db_session, app_context, request_context, auth_user):
        """测试删除不存在的规则"""
        auth_user()
        with pytest.raises(Exception) as exc_info:
            AutoDiscoveryRuleCRUD().delete(999999)
        assert ErrFormat.adr_not_found.format("id=999999") in str(exc_info.value)
