"""
认证相关fixtures
"""
import logging
from typing import Optional, Dict, Any
import pytest
from flask import session
from flask_login import login_user
from api.models.acl import User

logger = logging.getLogger(__name__)

# 测试用户配置
TEST_USER_CONFIGS = {
    'admin': {
        'username': 'admin',
        'session_data': {
            'acl': {
                'parentRoles': ['acl_admin', 'CMDB_ADMIN'],
                'username': 'admin',
                'uid': 1
            }
        }
    },
    'normal': {
        'username': 'test_user',
        'session_data': {
            'acl': {
                'parentRoles': ['cmdb_user'],
                'username': 'test_user',
                'uid': 2
            }
        }
    },
    'viewer': {
        'username': 'viewer',
        'session_data': {
            'acl': {
                'parentRoles': ['cmdb_viewer'],
                'username': 'viewer',
                'uid': 3
            }
        }
    }
}

def get_test_user(username: str) -> Optional[User]:
    """获取测试用户
    
    Args:
        username: 用户名
        
    Returns:
        Optional[User]: 用户对象或None
    """
    return User.query.filter_by(username=username).first()

@pytest.fixture
def auth_user():
    """提供认证用户的 fixture
    
    支持不同的用户角色和自定义 session 数据
    
    Args:
        user_role: 用户角色，默认为 'admin'
        custom_session: 自定义 session 数据
        
    Returns:
        User: 已认证的用户对象
    """
    def _auth_user(user_role: str = 'admin', custom_session: Optional[Dict[str, Any]] = None) -> Optional[User]:
        logger.info(f"开始认证用户，角色: {user_role}")
        
        # 获取用户配置
        user_config = TEST_USER_CONFIGS.get(user_role)
        if not user_config:
            logger.error(f"未找到用户角色配置: {user_role}")
            return None
            
        # 获取用户
        user = get_test_user(user_config['username'])
        if not user:
            logger.warning(f"未找到用户: {user_config['username']}")
            return None
            
        # 设置用户登录状态
        login_user(user)
        
        # 清理并设置新的 session 数据
        session.clear()
        if custom_session:
            session.update(custom_session)
        else:
            session.update(user_config['session_data'])
            
        logger.info(f"用户认证完成: {user.username}")
        logger.info(f"Session数据: {dict(session)}")
        return user
        
    return _auth_user
