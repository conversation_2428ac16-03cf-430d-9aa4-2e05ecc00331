"""
Celery相关fixtures
"""
import logging
import sys
from flask import current_app, has_request_context
import pytest
from api.extensions import celery
from contextlib import contextmanager

# 配置日志
logger = logging.getLogger('celery.task')
handler = logging.StreamHandler(sys.stdout)
handler.setLevel(logging.DEBUG)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
if not logger.handlers:
    logger.addHandler(handler)
logger.setLevel(logging.DEBUG)
logger.propagate = True


@contextmanager
def ensure_request_context():
    """确保在请求上下文中执行代码"""
    if has_request_context():
        yield
    else:
        with current_app.test_request_context():
            yield


def configure_celery(app):
    """配置Celery"""
    logger.info("配置Celery")
    celery.conf.update(app.config)
    TaskBase = celery.Task
    
    class ContextTask(TaskBase):
        abstract = True
        
        def __call__(self, *args, **kwargs):
            
            with ensure_request_context():
                return TaskBase.__call__(self, *args, **kwargs)
    
    celery.Task = ContextTask
    return celery


@pytest.fixture(scope='session')
def celery_worker(app):
    """配置 Celery 为测试模式
    
    在测试环境中，我们需要 Celery 任务同步执行，这样可以：
    1. 确保任务按序执行
    2. 方便调试和错误追踪
    3. 避免数据库会话问题
    """
    logger.info("配置 Celery 为测试模式")
    
    # 配置Celery
    configure_celery(app)
    
    # 保存原始配置
    original_config = {
        'task_always_eager': celery.conf.task_always_eager,
        'task_eager_propagates': celery.conf.task_eager_propagates
    }
    
    # 更新为测试配置
    celery.conf.update(
        task_always_eager=True,  # 强制同步执行
        task_eager_propagates=True,  # 异常直接抛出
        broker_url='memory://',  # 使用内存作为消息代理
        result_backend='cache',  # 使用缓存作为结果后端
        cache_backend='memory',  # 使用内存作为缓存后端
        task_ignore_result=False,  # 不忽略任务结果
        worker_log_level='DEBUG',  # 设置工作进程日志级别
        worker_log_format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',  # 设置日志格式
    )
    logger.info("Celery 已配置为同步执行模式")
    
    yield celery
    
    # 恢复原始配置
    celery.conf.update(original_config)
    logger.info("已恢复 Celery 原始配置") 