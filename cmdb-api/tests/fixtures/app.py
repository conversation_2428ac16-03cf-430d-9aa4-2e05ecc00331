"""
应用相关fixtures
"""
import logging
import sys
import pytest
import redis
from flask import Flask, g, current_app
from flask.testing import FlaskClient
from api.app import create_app
from ..utils.client import CMDBTestClient
from api.extensions import cache
from api.lib.cmdb.cache import LoadAttrCache, CITypeLoadAttrCache
from tests.settings import CACHE_REDIS_HOST, CACHE_REDIS_PORT, CACHE_REDIS_PASSWORD

# 配置日志处理器使用标准输出
logger = logging.getLogger(__name__)
handler = logging.StreamHandler(sys.stdout)
handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
logger.addHandler(handler)
logger.setLevel(logging.WARNING)


def clear_caches():
    """清理所有缓存"""
    try:
        # 清理Flask-Cache
        cache.clear()

        # 清理Redis缓存
        with redis.Redis(
            host=CACHE_REDIS_HOST,
            port=CACHE_REDIS_PORT,
            password=CACHE_REDIS_PASSWORD,
            decode_responses=True
        ) as redis_client:
            redis_client.flushall()
            logger.info("缓存清理完成")
    except Exception as e:
        logger.error(f"缓存清理失败: {str(e)}", exc_info=True)


@pytest.fixture(scope="session")
def app() -> Flask:
    """创建测试应用实例"""
    logger.info("创建测试应用实例")
    _app = create_app("tests.settings")
    _app.test_client_class = CMDBTestClient

    # 配置应用日志使用标准输出
    _app.logger.handlers = []
    _app.logger.addHandler(handler)

    yield _app

    logger.info("测试应用实例清理完成")


@pytest.fixture(autouse=True)
def app_context(app: Flask):
    """提供应用上下文，自动应用于所有测试"""
    logger.info("创建应用上下文")
    with app.app_context():
        # 初始化 g 对象
        if not hasattr(g, 'sqlalchemy_queries'):
            g.sqlalchemy_queries = []

        # 清理缓存
        clear_caches()

        yield

        # 测试结束后清理缓存
        clear_caches()

    logger.info("应用上下文已清理")


@pytest.fixture
def request_context(app_context, app: Flask):
    """提供请求上下文，仅在需要时使用"""
    logger.info("创建请求上下文")
    with app.test_request_context():
        from flask import session
        session.clear()
        logger.info("请求上下文已创建")
        yield
        session.clear()
        logger.info("请求上下文已清理")


@pytest.fixture
def client(app: Flask) -> CMDBTestClient:
    """创建测试客户端

    返回增强版的测试客户端，支持会话管理和上下文复用
    """
    logger.info("创建测试客户端")
    app.test_client_class = CMDBTestClient  # 设置使用我们的客户端类
    with app.test_client() as client:
        yield client
    logger.info("测试客户端已清理")
