"""
数据库相关fixtures

提供数据库连接、会话管理和数据初始化的功能。
支持数据保留、自动重置序号等特性。
"""
import logging
import os
from pathlib import Path
from typing import Optional
import pytest
from flask import Flask
from sqlalchemy.orm import scoped_session
from sqlalchemy import text
from api.extensions import db
from api.models.acl import User, App
from api.models.common_setting import Department, Employee

logger = logging.getLogger(__name__)

# 定义关键路径
FIXTURE_DIR = Path(__file__).parent
TEST_ROOT_DIR = FIXTURE_DIR.parent
SQL_FILE_PATH = TEST_ROOT_DIR / 'data' / 'sql' / 'init_data.sql'

class DBControl:
    """数据库控制配置"""
    INIT_DB = False      # 控制是否执行数据库初始化
    KEEP_DATA = True     # 控制是否保留测试数据

def clean_db():
    """初始化或清理测试数据库

    """
    should_init = DBControl.INIT_DB
    logger.info(f"数据库初始化控制: should_init={should_init}")
    if check_tables_exist():
        logger.info("数据库表已存在,仅清理数据...")
        for table in reversed(db.metadata.sorted_tables):
            try:
                db.session.execute(table.delete())
                db.session.commit()
            except Exception as e:
                logger.error(f"清理表 {table.name} 失败: {str(e)}")
                db.session.rollback()
                raise
        reset_auto_increment()
    if should_init:
        logger.info("进行数据库表初始化")
        teardown_db()
        db.create_all()
        # 导入初始数据
    logger.info("开始导入初始数据...")
    if SQL_FILE_PATH.exists():
        import_sql_file(str(SQL_FILE_PATH))
    else:
        logger.warning(f"SQL文件不存在: {SQL_FILE_PATH}")
    # 验证数据初始化结果
    verify_data_initialization()
    db.session.commit()

def teardown_db():
    """清理测试数据库"""
    logger.info("开始清理测试数据库")
    try:
        db.session.remove()
        db.drop_all()
        # 关闭数据库引擎的连接
        db.engine.dispose()
    except Exception as e:
        logger.error(f"清理数据库失败: {str(e)}")
    finally:
        logger.info("数据库清理完成")


def verify_data_initialization():
    """验证数据初始化结果"""
    logger.info("验证基础数据初始化结果:")
    
    # 用户验证
    all_users = User.query.all()
    logger.info(f"当前所有用户: {[user.username for user in all_users]}")
    logger.info(f"用户总数: {len(all_users)}")
    
    # Employee验证
    all_employees = Employee.query.all()
    logger.info(f"当前所有Employee记录: {[emp.email for emp in all_employees]}")
    logger.info(f"Employee记录总数: {len(all_employees)}")
    
    # 基础表记录验证
    logger.info(f"Department表记录数: {Department.query.count()}")
    logger.info(f"User表记录数: {User.query.count()}")
    logger.info(f"App表记录数: {App.query.count()}")
    


def check_tables_exist() -> bool:
    """检查数据库表是否存在"""
    try:
        db.session.execute(text("SELECT 1 FROM c_value_texts LIMIT 1"))
        return True
    except Exception:
        return False


def import_sql_file(filepath: str) -> None:
    """导入 SQL 文件数据到数据库
    
    Args:
        filepath: SQL文件路径
    """
    logger.info(f"开始导入 SQL 文件: {filepath}")
    try:
        with open(filepath, 'r', encoding='utf-8') as file:
            sql_script = file.read()
        
        for command in sql_script.split(';'):
            command = command.strip()
            if command:
                try:
                    db.session.execute(text(command))
                except Exception as e:
                    logger.error(f"执行 SQL 命令失败: {command}")
                    logger.error(f"错误信息: {str(e)}")
                    db.session.rollback()
                    raise
        
        db.session.commit()
        logger.info(f"SQL 文件导入完成: {filepath}")
    except Exception as e:
        logger.error(f"导入 SQL 文件失败: {filepath}")
        logger.error(f"错误信息: {str(e)}")
        db.session.rollback()
        raise


def verify_data_initialization() -> None:
    """验证数据初始化结果"""
    logger.info("验证基础数据初始化结果:")
    
    # 用户验证
    all_users = User.query.all()
    logger.info(f"当前所有用户: {[user.username for user in all_users]}")
    logger.info(f"用户总数: {len(all_users)}")
    
    # Employee验证
    all_employees = Employee.query.all()
    logger.info(f"当前所有Employee记录: {[emp.email for emp in all_employees]}")
    logger.info(f"Employee记录总数: {len(all_employees)}")
    
    # 基础表记录验证
    logger.info(f"Department表记录数: {Department.query.count()}")
    logger.info(f"User表记录数: {User.query.count()}")
    logger.info(f"App表记录数: {App.query.count()}")


def clean_tables() -> None:
    """清理所有表数据"""
    logger.info("开始清理表数据")
    try:
        # 获取所有表名
        result = db.session.execute(text("SHOW TABLES"))
        tables = [row[0] for row in result]
        
        # 禁用外键检查
        db.session.execute(text("SET FOREIGN_KEY_CHECKS = 0"))
        
        # 清理每个表的数据
        for table in tables:
            try:
                db.session.execute(text(f"TRUNCATE TABLE {table}"))
                logger.info(f"清理表 {table} 完成")
            except Exception as e:
                logger.warning(f"清理表 {table} 失败: {str(e)}")
        
        # 启用外键检查
        db.session.execute(text("SET FOREIGN_KEY_CHECKS = 1"))
        db.session.commit()
        logger.info("所有表数据清理完成")
    except Exception as e:
        logger.error(f"清理表数据时发生错误: {str(e)}")
        db.session.rollback()
        raise


def reset_auto_increment() -> None:
    """重置所有表的自增序号"""
    logger.info("开始重置数据库自增序号")
    try:
        result = db.session.execute(text("SHOW TABLES"))
        tables = [row[0] for row in result]
        
        for table in tables:
            try:
                db.session.execute(text(f"ALTER TABLE {table} AUTO_INCREMENT = 1"))
            except Exception as e:
                logger.warning(f"重置表 {table} 自增序号失败: {str(e)}")
                
        db.session.commit()
        logger.info("所有表自增序号重置完成")
    except Exception as e:
        logger.error(f"重置自增序号时发生错误: {str(e)}")
        db.session.rollback()
        raise

@pytest.fixture(scope="session")
def database(app: Flask):
    """数据库连接管理
    
    使用 Flask-SQLAlchemy 提供的上下文管理，
    确保在测试会话期间数据库连接的正确初始化和清理。
    """
    logger.info("初始化数据库连接")
    db.init_app(app)
    
    with app.app_context():
        try:
            yield db
        finally:
            logger.info("清理数据库连接")
            db.session.remove()
            db.engine.dispose()
            logger.info("数据库连接已清理")


@pytest.fixture
def db_session(database, app, request):
    """创建数据库会话"""
    logger.info("开始新的测试会话")
    logger.info(f"当前测试函数: {request.function.__name__}")
    logger.info(f"当前测试类: {request.cls.__name__ if request.cls else 'None'}")
    
    with app.app_context():
        connection = database.engine.connect()
        session = database.create_scoped_session(
            options={"bind": connection, "binds": {}}
        )
        database.session = session
        try:
            logger.info(f"数据库会话已创建: {id(session)}")
            clean_db()
            yield session
            
        except Exception as e:
            logger.error(f"会话执行异常: {str(e)}")
            logger.error("异常详细信息: ", exc_info=True)
            raise
        finally:
            logger.info(f"准备清理数据库会话: {id(session)}")
            session.close()
            connection.close()
            logger.info("测试会话结束")