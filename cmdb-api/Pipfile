[[source]]
url = "https://mirrors.aliyun.com/pypi/simple"
verify_ssl = true
name = "pypi"

[[source]]
url = "http://mirrors.aliyun.com/pypi/simple"
verify_ssl = true
name = "mirrorsaliyun"

[packages]
# Flask
click = "==8.1.3"
# Api
# Database
redis = "==4.6.0"
python-redis-lock = "==4.0.0"
# Migrations
# Deployment
gunicorn = "==21.0.1"
supervisor = "==4.0.3"
# Auth
ldap3 = "==2.9.1"
pycryptodome = "==3.12.0"
cryptography = ">=41.0.2"
# i18n
flask-babel = "==4.0.0"
# Caching
# Environment variable parsing
environs = "==4.2.0"
marshmallow = "==3.26.1"
# async tasks
celery = "==5.3.1"
more-itertools = "==5.0.0"
kombu = ">=5.3.1"
# common setting
timeout-decorator = "==0.5.0"
email-validator = "==1.3.1"
treelib = "==1.6.1"
flasgger = "==0.9.5"
# other
six = "==1.16.0"
bs4 = "==0.0.1"
toposort = "==1.10"
requests = "==2.31.0"
markdownify = "==0.11.6"
elasticsearch = "==7.17.9"
future = "==0.18.3"
itsdangerous = "==2.1.2"
jinja2schema = "==0.1.4"
msgpack-python = "==0.5.6"
alembic = "==1.7.7"
hvac = "==2.0.0"
colorama = ">=0.4.6"
pycryptodomex = ">=3.19.0"
lz4 = ">=4.3.2"
python-magic = "==0.4.27"
jsonpath = "==0.82.2"
shortuuid = "==1.0.13"
pyotp = "==2.9.0"
networkx = "==3.4.1"
celery-once = "==3.0.1"
flask = "==2.2.5"
flask-bcrypt = "==1.0.1"
flask-caching = "==2.0.2"
flask-cors = "==4.0.0"
flask-login = ">=0.6.2"
flask-migrate = "==2.5.2"
flask-restful = "==0.3.10"
flask-sqlalchemy = "==2.5.0"
jinja2 = "==3.1.2"
jsonschema = "==4.18.0"
mako = "==1.2.4"
markupsafe = "==2.1.3"
pillow = ">=10.0.1"
pyjwt = "==2.4.0"
pymysql = "==1.1.0"
pyyaml = "==6.0.1"
requests-oauthlib = "==1.3.1"
sqlalchemy = "==1.4.49"
werkzeug = "==2.2.3"
wtforms = "==3.0.0"
shamir = "~=17.12.0"

[dev-packages]
# Testing
pytest = "==4.6.5"
WebTest = "==2.0.33"
factory-boy = "==2.12.*"
pdbpp = "==0.10.0"
# Lint and code style
flake8 = "==3.7.7"
flake8-blind-except = "==0.1.1"
flake8-debugger = "==3.1.0"
flake8-docstrings = "==1.3.0"
flake8-isort = "==2.7.0"
isort = "==4.3.21"
pep8-naming = "==0.8.2"
pydocstyle = "==3.0.0"
webtest = "*"
