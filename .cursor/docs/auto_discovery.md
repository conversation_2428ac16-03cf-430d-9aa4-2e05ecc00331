# CMDB Auto Discovery 设计思路

## 1. 核心目标

CMDB 自动发现 (`auto_discovery`) 功能的核心目标是自动化地发现、识别并录入配置项 (CI) 数据到 CMDB 中，减少手动录入的工作量，提高数据的准确性和实时性。它旨在支持多种发现方式（如云平台 API、SNMP、Agent 插件、组件探测等），并提供一个可配置、可扩展的框架。

## 2. 设计原则

*   **CMDB 中心化配置:** CMDB 作为自动发现任务的配置和管理中心。用户在 CMDB UI 中定义发现规则、配置 CI 类型与规则的关联、设置属性映射等。
*   **规则驱动:** 发现过程由明确定义的"规则" (`AutoDiscoveryRule`) 驱动。规则定义了发现的目标类型（如阿里云、SNMP 设备）和发现方式。
*   **类型化配置:** 发现规则需要与具体的 CMDB CI 类型 (`CIType`) 关联配置 (`AutoDiscoveryCIType`) 后才能生效。这个配置层决定了如何将发现的数据映射到目标 CI 类型，以及任务的执行目标和参数。
*   **暂存与接受机制:** 为了防止错误或不期望的数据直接污染 CMDB，设计了一个"待接受区" (`AutoDiscoveryCI`)。通过各种方式发现的原始实例数据首先进入此暂存区，需要经过用户手动"接受"（或配置自动接受）后，才会正式创建或更新到 CMDB 的 CI 实例 (`c_cis`) 中。
*   **Agent/插件模式与 API 模式并存:**
    *   对于需要主动探测或执行脚本的场景（如 SNMP、自定义插件、组件发现），依赖外部的发现 Agent (如 OneAgent) 或执行器。Agent 通过 `/adt/sync` 接口从 CMDB 拉取分配给它的规则配置。
    *   对于云平台等提供 API 的场景，发现逻辑可能由 Agent 执行，也可能由 CMDB 后端（或集成的任务系统）直接调用 API。
*   **元数据驱动 UI:** 利用常量 (`const.py`) 和 JSON 模板文件 (`templates/*.json`) 存储云平台、资源、属性等静态元数据，使得 UI 能够动态地展示配置选项。
*   **可扩展性:** 支持通过编写插件 (`is_plugin=True`) 的方式扩展新的发现能力。
*   **显式关联与映射:** 配置中明确定义发现源属性 (`ad_key`) 到 CMDB 属性 (`cmdb_key`) 的映射关系，以及发现源关系到 CMDB 关系的映射。

## 3. 关键组件及其职责

*   **Models (`models/cmdb.py`):**
    *   `AutoDiscoveryRule`: 定义发现规则（名称、类型、内置/插件、脚本等）。
    *   `AutoDiscoveryCIType`: 配置 CI 类型与发现规则的关联（属性映射、执行目标、参数、调度信息等）。
    *   `AutoDiscoveryCI`: 存储待接受的发现实例原始数据。
    *   `AutoDiscoveryCITypeRelation`: 定义发现的关系如何映射到 CMDB 关系。
    *   `AutoDiscoveryAccount`: 存储规则执行所需的账户/凭证信息。
    *   `AutoDiscoveryRuleSyncHistory`: 记录 Agent 同步规则的历史。
    *   `AutoDiscoveryExecHistory`: 记录发现任务执行的日志/输出。
    *   `AutoDiscoveryCounter`: 存储统计数据。
*   **Lib (`lib/cmdb/auto_discovery/auto_discovery.py`):**
    *   `CRUD` 类 (如 `AutoDiscoveryRuleCRUD`, `AutoDiscoveryCITypeCRUD`): 封装对应模型的数据库操作和核心业务逻辑验证 (`_can_add`, `_can_update`, `_can_delete`)。
    *   `Manager` 类 (如 `AutoDiscoveryHTTPManager`, `AutoDiscoverySNMPManager`): 处理特定发现类型的逻辑，特别是元数据管理和解析。
    *   包含数据处理函数（如插件解析、加解密、数据映射应用等）。
*   **Views (`views/cmdb/auto_discovery.py`):**
    *   `AutoDiscoveryRuleView`: 管理发现规则 (ADR)
        *   `GET /adr`: 查询所有规则或按条件筛选规则
        *   `POST /adr`: 创建新规则，验证必要参数如规则名称
        *   `PUT /adr/<adr_id>`: 更新特定规则
        *   `DELETE /adr/<adr_id>`: 删除规则
    *   `AutoDiscoveryRuleTemplateFileView`: 处理规则模板导入导出
        *   `GET /adr/template/export/file`: 导出规则模板为文件
        *   `POST /adr/template/import/file`: 从文件导入规则模板
    *   `AutoDiscoveryRuleHTTPView`: 提供云平台 HTTP 规则元数据
        *   获取特定云服务商的资源类别、资源类型和属性列表
    *   `AutoDiscoveryCITypeView`: 管理 CI 类型与发现规则的关联配置 (ADT)
        *   `GET /adt/ci_types/<type_id>`: 获取 CI 类型关联的发现配置
        *   `POST /adt/ci_types/<type_id>`: 为 CI 类型创建发现配置
        *   `PUT /adt/ci_types/<adt_id>`: 更新发现配置
        *   `DELETE /adt/ci_types/<adt_id>`: 删除发现配置
    *   `AutoDiscoveryCITypeRelationView`: 管理 CI 类型的关系映射配置
        *   `GET /adt/ci_types/<type_id>/relations`: 获取类型的关系映射配置
        *   `POST /adt/ci_types/<type_id>/relations`: 创建/更新关系映射配置
    *   `AutoDiscoveryCIView`: 管理发现的实例数据 (ADC)
        *   `GET /adc`: 查询待接受的实例数据
        *   `POST /adc`: 上报发现的实例数据（Agent 使用）
        *   `DELETE /adc/<adc_id>`: 删除发现的实例数据
    *   `AutoDiscoveryCIAcceptView`: 处理接受发现的实例
        *   `PUT /adc/<adc_id>/accept`: 接受实例，将数据写入正式 CI 表
    *   `AutoDiscoveryRuleSyncView`: 处理 Agent 同步规则配置
        *   `GET /adt/sync`: Agent 调用获取分配给它的任务配置
    *   `AutoDiscoveryRuleSyncHistoryView`: 管理规则同步历史
        *   `GET /adt/<adt_id>/sync/histories`: 查询同步历史记录
    *   `AutoDiscoveryTestView`: 提供测试发现规则执行功能
        *   `POST /adt/<adt_id>/test`: 启动测试任务
        *   `GET /adt/test/<exec_id>/result`: 获取测试结果
    *   `AutoDiscoveryExecHistoryView`: 管理发现任务执行历史
        *   `GET /adc/exec/histories`: 查询执行历史
        *   `POST /adc/exec/histories`: 记录执行历史和输出
    *   `AutoDiscoveryCounterView`: 提供统计数据
        *   `GET /adc/counter`: 获取特定类型的统计信息
    *   `AutoDiscoveryAccountView`: 管理发现规则需要的账户信息
        *   `GET /adr/accounts`: 获取规则的账户信息
        *   `POST /adr/accounts`: 创建规则的账户信息
        *   `PUT /adr/accounts/<account_id>`: 更新账户信息
        *   `DELETE /adr/accounts/<account_id>`: 删除账户信息

主要工作流相关 API:
1. **规则配置流程**：`POST /adr` → `POST /adt/ci_types/<type_id>` → `POST /adt/ci_types/<type_id>/relations`
2. **Agent 同步与上报流程**：`GET /adt/sync` → `POST /adc`
3. **数据接受流程**：`GET /adc` → `PUT /adc/<adc_id>/accept`

## 4. 核心工作流程

### 4.1 规则与配置流程

1.  **定义规则 (ADR):** 用户在 UI 或通过 API (`POST /adr`) 创建 `AutoDiscoveryRule`，选择类型（HTTP, SNMP, Plugin 等），如果是插件则提供脚本。内置规则随系统提供。
2.  **配置 CI 类型发现 (ADT):** 用户在 UI 或通过 API (`POST /adt/ci_types/<type_id>`) 为某个 `CIType` 创建 `AutoDiscoveryCIType` 配置。
    *   选择关联的 ADR。
    *   配置属性映射（将发现源字段映射到 CMDB 属性）。
    *   配置执行目标（指定 Agent ID 或使用查询表达式 `query_expr` 动态选择 Agent）。
    *   配置执行参数和凭证（可能通过关联 `AutoDiscoveryAccount`）。
    *   配置调度信息（如 Cron 表达式或间隔）。
    *   配置关系映射 (`AutoDiscoveryCITypeRelation`)。
    *   设置是否自动接受。

### 4.2 Agent 执行与同步流程

1.  **Agent 请求同步:** 发现 Agent 定期调用 `GET /adt/sync`，提供自身标识。
2.  **CMDB 返回配置:** CMDB 后端根据 Agent 标识或匹配 `query_expr`，找到该 Agent 需要执行的 ADT 配置列表，并返回给 Agent。记录同步历史 (`AutoDiscoveryRuleSyncHistory`)。
3.  **Agent 执行:** Agent 根据收到的配置，连接目标系统（云 API、SNMP 设备等）或执行脚本。
4.  **Agent 上报数据:** Agent 将发现的原始实例数据（通常包含唯一标识和多个属性值）调用 `POST /adc` 上报给 CMDB。

### 4.3 数据暂存与接受流程

1.  **CMDB 接收数据:** `POST /adc` 接口接收到 Agent 上报的数据。
2.  **存入暂存区:** Lib 层的 `AutoDiscoveryCICRUD` 将原始数据创建或更新到 `AutoDiscoveryCI` 表中，状态为"待接受"。
3.  **用户审核/自动接受:**
    *   如果 ADT 配置了自动接受，系统自动触发接受流程。
    *   否则，用户在 UI 中查看待接受列表 (`GET /adc`)，手动点击"接受"按钮 (`PUT /adc/<adc_id>/accept`)。
4.  **接受处理:** `AutoDiscoveryCICRUD.accept` 方法被调用。
    *   根据 ADT 配置的属性映射，转换原始数据。
    *   调用 `CIManager.add` 或 `update` 方法，将数据写入 CMDB 的 CI 实例表 (`c_cis`) 和对应的属性值表。获取返回的 `ci_id`。
    *   更新 `AutoDiscoveryCI` 记录状态为"已接受"，并记录 `ci_id`。
    *   **异步处理关系:** 触发一个后台任务 (`build_relations_for_ad_accept`)，根据 `AutoDiscoveryCITypeRelation` 配置创建 CMDB 关系 (`c_ci_relations`)。

## 5. Mermaid 流程图

```mermaid
flowchart TD
    subgraph "用户与CMDB_UI"
        User["用户"] --> UI_Config["配置发现规则/类型关联/映射"]
        User --> UI_ViewADC["查看待接受实例"]
        User --> UI_Accept["手动接受实例"]
        User --> UI_Control["控制Prefect任务"]
        UI_Config --> API_PostADR["POST /adr"]
        UI_Config --> API_PostADT["POST /adt/ci_types"]
        UI_ViewADC --> API_GetADC["GET /adc"]
        UI_Accept --> API_PutAccept["PUT /adc/:id/accept"]
        UI_Control --> API_Control["POST /adt/:id/prefect/control"]
    end

    subgraph "CMDB_API"
        API_PostADR --> Lib_RuleCRUD["Rule CRUD"]
        API_PostADT --> Lib_CITypeCRUD["CIType CRUD"]
        API_GetADC --> Lib_CICRUD_Search["CI CRUD (Search ADC)"]
        API_PutAccept --> Lib_CICRUD_Accept["CI CRUD (Accept ADC)"]
        API_Sync["GET /adt/sync"] --> Lib_CITypeCRUD_Get["CIType CRUD (Get Rules for Agent)"]
        API_Report["POST /adc"] --> Lib_CICRUD_Upsert["CI CRUD (Upsert ADC)"]
    end

    subgraph "CMDB_Backend"
        Lib_RuleCRUD --> DB_Rule[(AutoDiscoveryRule)]
        Lib_CITypeCRUD --> DB_CIType[(AutoDiscoveryCIType)]
        Lib_CICRUD_Search --> DB_ADC[(AutoDiscoveryCI)]
        Lib_CITypeCRUD_Get --> DB_CIType
        Lib_CICRUD_Upsert --> DB_ADC
        Lib_CICRUD_Accept --> |转换数据| CIManager["CIManager"]
        CIManager --> |创建/更新CI| DB_CI[(CMDB CI)]
        Lib_CICRUD_Accept --> |更新ADC状态| DB_ADC
        Lib_CICRUD_Accept --> |触发后台任务| RelationTask["异步处理关系"]
        RelationTask --> Lib_RelationCRUD["CITypeRelation CRUD"]
        Lib_RelationCRUD --> DB_Relation[(CMDB Relation)]
    end

    subgraph "Discovery_Agent"
        AgentStart["启动/定时"] --> AgentSync["请求同步配置"]
        AgentSync --> API_Sync
        API_Sync --> AgentSyncResponse["接收配置"]
        AgentSyncResponse --> AgentExec["执行发现"]
        AgentExec --> TargetSystem["目标系统 (API/SNMP/...)"]
        TargetSystem --> AgentExec
        AgentExec --> AgentReport["上报发现数据"]
        AgentReport --> API_Report
    end

    style DB_Rule fill:#f9f,stroke:#333,stroke-width:2px
    style DB_CIType fill:#f9f,stroke:#333,stroke-width:2px
    style DB_ADC fill:#f9f,stroke:#333,stroke-width:2px
    style DB_CI fill:#ccf,stroke:#333,stroke-width:2px
    style DB_Relation fill:#ccf,stroke:#333,stroke-width:2px
```

## 6. 特点与优势

*   **灵活性:** 支持多种发现源和发现方式。
*   **可配置性:** 提供丰富的配置选项，包括属性映射、关系映射、执行目标等。
*   **暂存区控制:** 避免了脏数据直接入库，提供了审核环节。
*   **可扩展性:** 插件机制允许用户自定义发现逻辑。
*   **自动化:** 减少人工干预，提高效率和准确性。

## 7. 局限性

*   **配置复杂度:** 对于复杂的环境，配置过程可能比较繁琐。
*   **Agent 依赖:** 部分发现方式依赖于外部 Agent 的部署和维护。
*   **错误处理:** 发现过程中的错误需要在 Agent 端或 CMDB 后端进行有效的捕获和报告。

*   **Constants (`lib/cmdb/auto_discovery/const.py`):**
    *   `DEFAULT_INNER`: 定义内置规则元数据。
    *   `CLOUD_MAP`: 定义云平台资源、属性模板、映射关系等详细元数据。
*   **Templates (`lib/cmdb/auto_discovery/templates/`):**
    *   JSON 文件，定义特定资源（如 `aliyun_ecs`）可发现的属性列表。
*   **Discovery Agent/Plugin (外部):**
    *   实际执行发现任务的组件（如 OneAgent、自定义脚本）。
    *   通过 `/adt/sync` 获取任务配置。
    *   通过 `/adc` (POST) 上报发现的原始数据。

## 7. 集成Prefect作为自动发现方式的设计

为了进一步扩展CMDB的自动发现能力，我们将Prefect工作流平台作为auto_discovery的一种新增发现方式，与现有的HTTP、SNMP、Plugin等方式并列提供服务。这种集成方案遵循以下原则：

1. **最小变更原则:** 尽量不修改原有代码或表结构，以减少对现有功能的影响。
2. **接口复用:** Prefect类型的发现也应尽量使用已有的接口和数据表，保持架构一致性。
3. **职责清晰:** 明确CMDB与Prefect平台的职责边界，合理分配任务。

### 7.1 核心设计思路

* **新增发现类型:** 在`AutoDiscoveryType`中添加`PREFECT`类型，使其成为标准的发现方式之一。
* **复用现有模型:** 不对`AutoDiscoveryRule`和`AutoDiscoveryCIType`添加新字段，而是利用已有的`option`、`extra_option`等JSON字段存储Prefect相关信息。
* **统一管理流程:** 保持与其他发现方式一致的管理流程（创建规则→关联CI类型→配置映射→执行发现→审核接受）。
* **接口适配:** 新增专门的接口供Prefect Flow获取执行所需的配置信息，参照`AutoDiscoveryRuleSyncView`的设计理念。

### 7.2 组件设计与实现

#### 7.2.1 Model层扩展（无需修改表结构）

1. **`lib/cmdb/auto_discovery/const.py`**:
   * 向`AutoDiscoveryType`枚举添加`PREFECT = 'prefect'`类型
   ```python
   class AutoDiscoveryType(Enum):
       HTTP = 'http'
       SNMP = 'snmp'
       PLUGIN = 'plugin'
       COMPONENTS = 'components'
       PREFECT = 'prefect'  # 新增
   ```

2. **AutoDiscoveryRule使用**:
   * 复用现有字段结构，无需修改表
   * `type = 'prefect'`: 标识该规则为Prefect类型
   * `is_plugin = False`: Prefect类型视为内置规则，非插件
   * `option`字段存储Prefect相关元数据:
     ```json
     {
       "flow_name": "discover_linux_host",
       "entrypoint": "oct_project/flows/discovery.py:discover_linux_host",
       "description": "通过SSH发现Linux主机基础信息"
     }
     ```

3. **AutoDiscoveryCIType使用**:
   * 复用现有字段结构，无需修改表
   * `extra_option`字段存储Prefect特有配置:
     ```json
     {
       "prefect_deployment_id": "部署ID",
       "prefect_params": { "额外参数": "值" },
       "prefect_last_run": {
         "run_id": "最近运行ID",
         "status": "状态",
         "timestamp": "时间戳"
       }
     }
     ```
   * 继续使用已有的`interval`和`cron`字段管理调度

#### 7.2.2 Lib层扩展

1. **扩展`AutoDiscoveryRuleCRUD`类**:
   ```python
   def _can_add(self, valid=True, **kwargs):
       # 现有代码...
       if kwargs.get('type') == AutoDiscoveryType.PREFECT:
           # 验证Prefect规则的必要字段
           if not kwargs.get('option') or 'flow_name' not in kwargs['option'] or 'entrypoint' not in kwargs['option']:
               return False, "Prefect规则必须提供flow_name和entrypoint"
       # 现有代码...
   ```

2. **扩展`AutoDiscoveryCITypeCRUD`类**:
   ```python
   def _can_add(self, valid=True, **kwargs):
       # 现有代码...
       adr = AutoDiscoveryRuleCRUD.get_by_id(kwargs['adr_id'])
       if adr and adr.type == AutoDiscoveryType.PREFECT:
           # 创建Prefect Deployment
           if not kwargs.get('extra_option'):
               kwargs['extra_option'] = {}
               
           deployment_id = PrefectManager.create_deployment(
               adt_id=kwargs['id'],  # 将被创建的ADT ID
               flow_name=adr.option['flow_name'],
               entrypoint=adr.option['entrypoint'],
               schedule_type=kwargs.get('cron') and 'cron' or 'interval',
               schedule_value=kwargs.get('cron') or kwargs.get('interval')
           )
           kwargs['extra_option']['prefect_deployment_id'] = deployment_id
       # 现有代码...
   ```
   
   ```python
   def _can_update(self, valid=True, **kwargs):
       # 现有代码...
       instance = self.get_by_id(kwargs.get('_id'))
       if instance:
           adr = AutoDiscoveryRuleCRUD.get_by_id(instance.adr_id)
           if adr and adr.type == AutoDiscoveryType.PREFECT:
               # 如果调度信息变更，需要更新Deployment
               new_cron = kwargs.get('cron')
               new_interval = kwargs.get('interval')
               if (new_cron and new_cron != instance.cron) or (new_interval and new_interval != instance.interval):
                   # 更新Prefect Deployment
                   deployment_id = instance.extra_option.get('prefect_deployment_id')
                   if deployment_id:
                       PrefectManager.update_deployment_schedule(
                           deployment_id=deployment_id,
                           schedule_type=new_cron and 'cron' or 'interval',
                           schedule_value=new_cron or new_interval
                       )
       # 现有代码...
   ```
   
   ```python
   def _can_delete(self, valid=True, **kwargs):
       # 现有代码...
       instance = self.get_by_id(kwargs.get('_id'))
       if instance and instance.extra_option and 'prefect_deployment_id' in instance.extra_option:
           # 删除关联的Prefect Deployment
           PrefectManager.delete_deployment(instance.extra_option['prefect_deployment_id'])
       # 现有代码...
   ```

3. **新增`PrefectManager`类**:
   ```python
   class PrefectManager:
       @staticmethod
       def create_deployment(adt_id, flow_name, entrypoint, schedule_type, schedule_value):
           """创建Prefect Deployment"""
           # 实现与Prefect API交互，创建Deployment
           # 将adt_id作为Flow的固定参数传入
           # 返回deployment_id
           
       @staticmethod
       def update_deployment_schedule(deployment_id, schedule_type, schedule_value):
           """更新Deployment调度配置"""
           # 实现与Prefect API交互，更新Deployment调度设置
           
       @staticmethod
       def delete_deployment(deployment_id):
           """删除Deployment"""
           # 实现与Prefect API交互，删除Deployment
           
       @staticmethod
       def get_flow_run_status(deployment_id):
           """获取Flow Run状态"""
           # 实现与Prefect API交互，获取最新状态
           
       @staticmethod
       def trigger_flow_run(deployment_id, params=None):
           """手动触发Flow运行"""
           # 实现与Prefect API交互，手动触发运行
           # 返回flow_run_id
           
       @staticmethod
       def pause_deployment(deployment_id):
           """暂停Deployment调度"""
           # 实现与Prefect API交互，暂停调度
           
       @staticmethod
       def resume_deployment(deployment_id):
           """恢复Deployment调度"""
           # 实现与Prefect API交互，恢复调度
   ```

#### 7.2.3 Views层扩展

1. **PrefectSyncView**:
   ```python
   class PrefectSyncView(APIView):
       url_prefix = ("/adt/prefect/sync/<int:adt_id>",)
       
       def get(self, adt_id):
           """供Prefect Flow获取执行配置"""
           # 获取ADT配置
           adt = AutoDiscoveryCITypeCRUD.get_by_id(adt_id)
           if not adt:
               return self.jsonify(error="ADT不存在"), 404
               
           # 获取关联的Rule
           adr = AutoDiscoveryRuleCRUD.get_by_id(adt.adr_id)
           if not adr or adr.type != AutoDiscoveryType.PREFECT:
               return self.jsonify(error="非Prefect类型规则"), 400
               
           # 构建配置响应
           config = {
               "adt_id": adt_id,
               "type_id": adt.type_id,
               "attributes": adt.attributes,  # 属性映射
               "auto_accept": adt.auto_accept,
               # 其他必要配置...
           }
           
           return self.jsonify(config)
   ```

2. **PrefectControlView**:
   ```python
   class PrefectControlView(APIView):
       url_prefix = ("/adt/<int:adt_id>/prefect/control",)
       
       @args_required("action")
       def post(self, adt_id):
           """控制Prefect Deployment（暂停/恢复/触发）"""
           action = request.values.get('action')
           adt = AutoDiscoveryCITypeCRUD.get_by_id(adt_id)
           if not adt or not adt.extra_option or 'prefect_deployment_id' not in adt.extra_option:
               return self.jsonify(error="Deployment不存在"), 404
               
           deployment_id = adt.extra_option['prefect_deployment_id']
           
           if action == 'trigger':
               flow_run_id = PrefectManager.trigger_flow_run(deployment_id)
               return self.jsonify(flow_run_id=flow_run_id)
           elif action == 'pause':
               PrefectManager.pause_deployment(deployment_id)
               return self.jsonify(message="已暂停")
           elif action == 'resume':
               PrefectManager.resume_deployment(deployment_id)
               return self.jsonify(message="已恢复")
           else:
               return self.jsonify(error="无效操作"), 400
   ```

   3. **扩展`AutoDiscoveryTestView`**:
      ```python
      class AutoDiscoveryTestView(APIView):
          url_prefix = ("/adt/<int:adt_id>/test",) # 保持不变
          
          def post(self, adt_id):
              """
              启动测试。
              对于非 Prefect 类型，行为不变。
              对于 Prefect 类型，触发一次性 Flow Run，并根据 Flow 设计获取测试结果或元数据。
              """
              adt = AutoDiscoveryCITypeCRUD.get_by_id(adt_id)
              if not adt:
                  return self.jsonify(error="ADT不存在"), 404
              
              adr = AutoDiscoveryRuleCRUD.get_by_id(adt.adr_id)
              if not adr:
                  return self.jsonify(error="ADR不存在"), 404
              
              # 处理Prefect类型
              if adr.type == AutoDiscoveryType.PREFECT:
                  if not adt.extra_option or 'prefect_deployment_id' not in adt.extra_option:
                      return self.jsonify(error="Prefect Deployment未配置"), 400
                  
                  deployment_id = adt.extra_option['prefect_deployment_id']
                  
                  # 触发 Flow Run，要求返回元数据
                  try:
                      flow_run_id = PrefectManager.trigger_flow_run(
                          deployment_id,
                          params={"get_metadata_only": True} # <--- 新增参数，要求 Flow 返回元数据
                      )
                  except Exception as e:
                      return self.jsonify(error=f"启动 Prefect 测试失败: {str(e)}"), 500

                  # 创建测试执行记录，包含 Flow Run ID 以便后续查询结果
                  exec_id = str(uuid.uuid4())
                  AutoDiscoveryExecHistoryCRUD().add(
                      type_id=adt.type_id,
                      stdout=f"已启动Prefect元数据获取测试，Flow Run ID: {flow_run_id}",
                      extra_option={"prefect_flow_run_id": flow_run_id} # 存储 run_id
                  )
                  
                  return self.jsonify(exec_id=exec_id) # 返回测试记录ID
              
              # 处理其他类型的现有代码...

          def get(self, exec_id):
              """
              获取测试结果。
              对于非 Prefect 类型，行为不变。
              对于 Prefect 类型，查询对应的 Flow Run 状态和结果。
              如果 Flow Run 成功且返回了元数据，则将其返回。
              """
              exec_history = AutoDiscoveryExecHistoryCRUD.get_by_id(exec_id)
              if not exec_history:
                  return self.jsonify(error="测试记录不存在"), 404

              # 处理 Prefect 类型的结果查询
              if exec_history.extra_option and 'prefect_flow_run_id' in exec_history.extra_option:
                  flow_run_id = exec_history.extra_option['prefect_flow_run_id']
                  try:
                      # 查询 Flow Run 状态和结果
                      status, result = PrefectManager.get_flow_run_result(flow_run_id) 
                      
                      response = {
                          "status": status, # Flow Run 的状态 (e.g., SUCCESS, FAILED, RUNNING)
                          "result": result, # Flow Run 的返回值 (成功时应包含元数据)
                          "stdout": exec_history.stdout # 初始启动信息
                      }
                      # 可选：根据状态更新 exec_history 记录

                      return self.jsonify(response)
                  except Exception as e:
                      return self.jsonify(error=f"查询 Prefect 测试结果失败: {str(e)}"), 500

              # 处理其他类型的现有代码...

      ```
      *   **关键变更:**
          *   `POST`: 对于 Prefect 类型，触发 `trigger_flow_run` 时传递 `params={"get_metadata_only": True}`。保存 `flow_run_id` 到执行历史记录中。
          *   `GET`: 对于 Prefect 类型，从历史记录中获取 `flow_run_id`，调用 `PrefectManager.get_flow_run_result` (需要新增此方法) 来查询 Flow Run 的状态和 *返回值*。
          *   **`PrefectManager` 需要新增 `get_flow_run_result(flow_run_id)` 方法**，该方法应使用 Prefect client 查询指定 `flow_run_id` 的状态，并在其完成后获取其返回结果。

4. **扩展UI层**:
   * 通过前端组件为Prefect类型的规则提供特定的UI操作:
     * 查看部署状态
     * 手动触发运行
     * 暂停/恢复调度
     * 查看执行历史

#### 7.2.4 监控组件

1. **实现后台监控任务**:
   ```python
   def monitor_prefect_deployments():
       """监控所有Prefect类型的发现任务状态"""
       # 查询所有Prefect类型的ADT
       prefect_adts = AutoDiscoveryCITypeCRUD.search(
           rule_type=AutoDiscoveryType.PREFECT
       )
       
       for adt in prefect_adts:
           if adt.extra_option and 'prefect_deployment_id' in adt.extra_option:
               # 获取最新状态
               status = PrefectManager.get_flow_run_status(adt.extra_option['prefect_deployment_id'])
               
               # 更新状态记录
               adt.extra_option['prefect_last_run'] = {
                   'status': status.get('state'),
                   'timestamp': datetime.datetime.now().isoformat(),
                   'run_id': status.get('id')
               }
               
               # 更新ADT记录
               AutoDiscoveryCITypeCRUD.update(adt.id, extra_option=adt.extra_option)
   ```

### 7.3 工作流程

#### 7.3.1 管理员配置流程

1. **创建Prefect规则:**
   * 管理员通过UI或API (`POST /adr`) 创建类型为`prefect`的规则。
   * 在`option`中提供`flow_name`、`entrypoint`等Prefect相关信息。

2. **关联CI类型:**
   * 通过现有接口 (`POST /adt/ci_types/<type_id>`) 为CI类型关联Prefect规则。
   * 配置属性映射、调度信息等，与其他发现类型一致。
   * 系统在创建关联时，调用`PrefectManager`创建对应的Prefect Deployment，并将`deployment_id`存储在`extra_option`中。

#### 7.3.2 Flow执行流程

1. **调度触发:**
   * Prefect平台根据Deployment配置的调度计划触发Flow Run。
   * Flow运行时接收包含`adt_id`的参数。

2. **配置同步:**
   * Flow调用新增接口 `GET /adt/prefect/sync/<adt_id>` 获取完整配置。
   * 配置内容包括: CI类型信息、属性映射规则、目标信息、凭证等。

3. **执行发现:**
   * Flow根据配置执行具体的发现逻辑。
   * 处理并映射原始数据。

4. **数据回传:**
   * Flow调用现有的 `POST /adc` 接口将发现数据回传至CMDB。
   * 格式与现有Agent上报格式完全一致: `{type_id, adt_id, instance, unique_value}`。

#### 7.3.3 数据处理流程

1. **数据接收与暂存:**
   * 使用现有的 `/adc` 接口接收数据并调用`AutoDiscoveryCICRUD.upsert`。
   * 数据存入`AutoDiscoveryCI`表，状态为"待接受"。

2. **审核与接受:**
   * 若配置了自动接受，系统自动触发接受流程。
   * 否则，用户通过UI手动接受数据，与其他发现方式的接受流程完全一致。

#### 7.3.4 Prefect Flow 测试与配置流程

1.  **用户发起测试:** 用户在 CMDB UI 中选择一个已配置好的 Prefect 类型的 ADT，点击"测试连接"或"获取字段"按钮。
2.  **CMDB API 调用:** UI 调用 `POST /adt/<adt_id>/test`。
3.  **触发元数据 Flow Run:**
    *   CMDB 后端识别到是 Prefect 类型。
    *   调用 `PrefectManager.trigger_flow_run`，并传递参数 `{"get_metadata_only": True}`。
    *   Prefect 平台基于对应的 Deployment 创建一个 Flow Run。
    *   CMDB API 返回一个测试执行记录 ID (`exec_id`) 给 UI。
4.  **Prefect Flow 执行 (元数据模式):**
    *   Flow 启动，检测到 `get_metadata_only=True` 参数。
    *   Flow **不执行**实际的发现逻辑。
    *   Flow **不调用** `/adt/prefect/sync/<adt_id>` 获取完整配置。
    *   Flow 直接返回一个包含 `unique_key` 和 `attributes` 的字典（详见下文 Flow 规范）。
5.  **UI 查询结果:** UI 定期使用 `exec_id` 调用 `GET /adt/test/<exec_id>/result`。
6.  **CMDB 查询 Prefect:**
    *   CMDB 后端根据 `exec_id` 找到关联的 `flow_run_id`。
    *   调用 `PrefectManager.get_flow_run_result` 查询该 Flow Run 的状态和结果。
7.  **返回元数据:** 如果 Flow Run 成功完成，CMDB 将 Flow 的返回值（包含 `unique_key` 和 `attributes` 的字典）返回给 UI。
8.  **用户配置映射:** UI 收到元数据后，向用户展示可发现的属性列表，用户可以据此配置属性映射 (`adt.attributes`)。

### 7.4 状态监控

* **定期轮询:** CMDB定期调用Prefect API查询Flow Run状态。
* **状态缓存:** 将状态信息缓存在`AutoDiscoveryCIType.extra_option.prefect_last_run`中。
* **UI展示:** 在CMDB UI中展示Flow执行状态，与其他发现方式的状态展示一致。

### 7.5 对比与优势

与传统的发现方式相比，Prefect方式具有以下优势：

* **灵活性:** 利用Prefect的编排能力处理复杂的发现逻辑和依赖关系。
*   **可扩展性:** 轻松添加新的发现Flow，无需修改CMDB核心代码。
*   **可靠性:** 借助Prefect的重试、日志、监控等能力提高发现任务的可靠性。
*   **统一管理:** 复用现有auto_discovery框架，保持统一的用户体验和管理流程。

### 7.6 实现要点

* **无侵入性:** 不修改现有表结构，而是通过JSON字段存储Prefect特有信息。
* **接口兼容:** 新增的接口遵循现有API的设计风格和认证机制。
* **职责边界:** CMDB负责配置管理和数据处理，Prefect负责任务调度和执行。
* **错误处理:** 完善的错误处理机制，确保配置错误或执行失败时有清晰的反馈。

### 7.7 Prefect集成流程图

```mermaid
flowchart TD
    subgraph 用户与CMDB_UI [用户与 CMDB UI]
        User["用户"] --> UI_Config["配置发现规则/类型关联/映射"]
        User --> UI_ViewADC["查看待接受实例"]
        User --> UI_Accept["手动接受实例"]
        User --> UI_Control["控制Prefect任务"]
        UI_Config --> API_PostADR["POST /adr"]
        UI_Config --> API_PostADT["POST /adt/ci_types"]
        UI_ViewADC --> API_GetADC["GET /adc"]
        UI_Accept --> API_PutAccept["PUT /adc/:id/accept"]
        UI_Control --> API_Control["POST /adt/:id/prefect/control"]
    end

    subgraph CMDB_API [CMDB API 层]
        API_PostADR --> Lib_RuleCRUD["Rule CRUD"]
        API_PostADT --> Lib_CITypeCRUD["CIType CRUD"]
        API_GetADC --> Lib_CICRUD_Search["CI CRUD (Search ADC)"]
        API_PutAccept --> Lib_CICRUD_Accept["CI CRUD (Accept ADC)"]
        API_Sync["GET /adt/prefect/sync/:id"] --> Lib_PrefectSync["获取Prefect配置"]
        API_Control --> Lib_PrefectControl["控制Prefect Deployment"]
        API_Report["POST /adc"] --> Lib_CICRUD_Upsert["CI CRUD (Upsert ADC)"]
    end

    subgraph CMDB_Backend [CMDB Backend]
        Lib_RuleCRUD --> DB_Rule[(AutoDiscoveryRule)]
        Lib_CITypeCRUD --> DB_CIType[(AutoDiscoveryCIType)]
        Lib_CICRUD_Search --> DB_ADC[(AutoDiscoveryCI)]
        Lib_PrefectSync --> DB_CIType
        Lib_CICRUD_Upsert --> DB_ADC
        Lib_CICRUD_Accept --> |转换数据| CIManager["CIManager"]
        CIManager --> |创建/更新CI| DB_CI[(CMDB CI)]
        Lib_CICRUD_Accept --> |更新ADC状态| DB_ADC
        Lib_CICRUD_Accept --> |触发后台任务| RelationTask["异步处理关系"]
        RelationTask --> Lib_RelationCRUD["CITypeRelation CRUD"]
        Lib_RelationCRUD --> DB_Relation[(CMDB Relation)]
        
        subgraph CMDB_Prefect_Manager [Prefect Manager]
            Lib_CITypeCRUD --> |创建ADT时| PM_Create["创建Deployment"]
            Lib_PrefectControl --> |控制请求| PM_Control["控制Deployment"]
            Monitor["后台监控任务"] --> PM_Status["获取状态"]
            PM_Status --> |更新状态| DB_CIType
        end
    end

    subgraph Prefect_Platform [Prefect 平台]
        PM_Create --> |API调用| Prefect_API["Prefect API"]
        PM_Control --> |API调用| Prefect_API
        PM_Status --> |查询状态| Prefect_API
        
        Prefect_API --> |创建| Prefect_Deployment[(Prefect Deployment)]
        Prefect_API --> |控制| Prefect_Deployment
        Prefect_Deployment --> |调度触发| Prefect_Flow["Prefect Flow"]
        Prefect_Flow --> |执行| Flow_Sync["获取配置"]
        Flow_Sync --> |调用| API_Sync
        Flow_Exec["执行发现"] --> |处理数据| Flow_Report["上报数据"]
        Flow_Report --> |调用| API_Report
    end

    subgraph Discovery_Target [发现目标]
        Flow_Exec --> Target_System["目标系统 (API/SSH/DB等)"]
        Target_System --> Flow_Exec
    end

    style DB_Rule fill:#f9f,stroke:#333,stroke-width:2px
    style DB_CIType fill:#f9f,stroke:#333,stroke-width:2px
    style DB_ADC fill:#f9f,stroke:#333,stroke-width:2px
    style DB_CI fill:#ccf,stroke:#333,stroke-width:2px
    style DB_Relation fill:#ccf,stroke:#333,stroke-width:2px
    style Prefect_Deployment fill:#f99,stroke:#333,stroke-width:2px
    style Prefect_Flow fill:#f99,stroke:#333,stroke-width:2px
```

## 8. Prefect Flow 开发规范 (用于 CMDB 自动发现)

为了确保 Prefect Flow 能与 CMDB 自动发现框架无缝集成，开发者在编写 Flow 时应遵循以下规范：

### 8.1 Flow 函数签名与核心参数

每个用于 CMDB 自动发现的 Prefect Flow 函数必须接受以下核心参数：

```python
from typing import Optional, List, Dict, Any
from prefect import flow, task # 假设使用 Prefect 2+

@flow(name="my-discovery-flow") # Flow 名称应与 ADR 配置中的 flow_name 一致
def my_discovery_flow(
    adt_id: int,                     # 必须：由 CMDB Deployment 传入的 AutoDiscoveryCIType ID
    get_metadata_only: bool = False, # 必须：控制 Flow 行为模式，默认为 False (执行发现)
    # 其他可选的、由 Deployment 传入的静态参数...
):
    """
    示例 CMDB 自动发现 Flow。

    Args:
        adt_id: 关联的 CMDB AutoDiscoveryCIType 配置 ID。
        get_metadata_only: 如果为 True，仅返回元数据，不执行实际发现。
    """
    # ... Flow 逻辑 ...
```

### 8.2 元数据模式 (`get_metadata_only=True`)

当 Flow 检测到 `get_metadata_only` 参数为 `True` 时，必须满足以下要求：

1.  **不执行实际发现:** Flow 不应连接目标系统、执行探测或调用耗时操作。
2.  **不获取完整配置:** Flow 不应调用 `GET /adt/prefect/sync/<adt_id>`。
3.  **返回元数据:** Flow **必须**返回一个包含以下两个键的 **Python 字典**：
    *   `unique_key` (str): 一个字符串，表示用于唯一标识发现实例的**源字段名称**（与下面 `attributes` 中定义的某个字段名对应）。这类似于 `plugin_script.py` 的 `unique_key` 属性。
    *   `attributes` (List[tuple]): 一个列表，列表中的每个元素是一个元组，包含三个值：`(字段名_str, 字段类型_str, 字段描述_str)`。这定义了该 Flow *可能*发现的所有字段及其元信息，格式同 `plugin_script.py` 的 `attributes()` 方法。字段类型应为 CMDB 可识别的类型（如 `String`, `Integer`, `Float`, `DateTime`, `JSON` 等）。

**示例元数据返回值:**

```python
    if get_metadata_only:
        metadata = {
            "unique_key": "instance_id", # 源系统中的唯一标识字段名
            "attributes": [
                ("instance_id", "String", "实例的唯一ID"),
                ("hostname", "String", "主机名"),
                ("ip_address", "String", "主IP地址"),
                ("os_version", "String", "操作系统版本"),
                ("cpu_cores", "Integer", "CPU核心数"),
                ("memory_gb", "Float", "内存大小 (GB)"),
                ("tags", "JSON", "附加标签信息")
            ]
        }
        print("元数据模式：返回字段信息")
        return metadata
```

### 8.3 发现与上报模式 (`get_metadata_only=False`)

当 Flow 在正常模式下执行时（`get_metadata_only` 为 `False` 或未提供），必须满足以下要求：

1.  **获取配置:** Flow **必须**首先使用传入的 `adt_id` 调用 CMDB API `GET /adt/prefect/sync/<adt_id>` 来获取完整的执行配置，包括属性映射规则、目标信息、凭证引用等。
2.  **执行发现:** 根据获取的配置，连接目标系统、执行探测、调用 API 等，收集所需的原始数据。
3.  **处理数据:** （可选）根据需要对原始数据进行初步处理或转换。
4.  **格式化上报数据:** 将发现的每个实例组织成一个字典列表。每个字典代表一个发现的 CI 实例，其键应该是 Flow 元数据中定义的**源字段名**。
5.  **调用 API 上报:** Flow **必须**调用 CMDB API `POST /adc` 来上报发现的数据。上报的数据 Body 应符合 `/adc` 接口的要求，通常包含 `type_id`, `adt_id`, `instances` (发现的实例字典列表), `unique_value` (可选，用于标识本次发现批次)。

**示例发现与上报逻辑:**

```python
    # --- 正常发现模式 ---
    print(f"正常发现模式：adt_id={adt_id}")

    # 1. 获取配置 (需要实现 get_cmdb_config 函数)
    config = get_cmdb_config(adt_id)
    if not config:
        raise ValueError(f"无法获取 adt_id={adt_id} 的配置")
    
    # 2. 执行发现 (需要实现 discover_resources 函数)
    #    discover_resources 应使用 config 中的目标和凭证信息
    raw_discovered_data: List[Dict[str, Any]] = discover_resources(config)

    # 3. (可选) 数据处理
    processed_data = process_data(raw_discovered_data, config.get('attribute_mapping'))

    # 4. 准备上报数据
    instances_to_report = []
    unique_key_name = config.get('metadata', {}).get('unique_key', 'instance_id') # 从配置获取unique_key
    
    for item in processed_data:
        # 确保 unique_key 存在
        if unique_key_name not in item:
             print(f"警告：跳过缺少唯一键 '{unique_key_name}' 的实例: {item}")
             continue
        instances_to_report.append(item)
        
    if not instances_to_report:
        print("没有发现可上报的实例。")
        return {"status": "no data reported"}

    # 5. 调用 API 上报 (需要实现 report_to_cmdb 函数)
    report_result = report_to_cmdb(
        type_id=config['type_id'],
        adt_id=adt_id,
        instances=instances_to_report
    )

    print(f"上报完成: {report_result}")
    return {"status": "reported", "count": len(instances_to_report)}

# --- 需要实现的辅助函数 ---

@task # 标记为 Task 以便 Prefect 管理
def get_cmdb_config(adt_id: int) -> Optional[Dict[str, Any]]:
    """调用 GET /adt/prefect/sync/<adt_id> 获取配置"""
    # 实现调用 CMDB API 的逻辑
    # 需要处理认证、错误等
    print(f"任务：正在为 adt_id={adt_id} 获取 CMDB 配置...")
    # response = httpx.get(f"{CMDB_API_URL}/adt/prefect/sync/{adt_id}", headers=AUTH_HEADERS)
    # response.raise_for_status()
    # return response.json()
    # 示例返回:
    return {
        "type_id": 1,
        "adt_id": adt_id,
        "attribute_mapping": {"ip_address": "primary_ip", "hostname": "name"},
        "metadata": {"unique_key": "instance_id", "attributes": [...]},
        "target_info": {...},
        "credential_id": "vault:secret/path" # 示例凭证引用
    }

@task
def discover_resources(config: Dict[str, Any]) -> List[Dict[str, Any]]:
    """执行实际的发现逻辑"""
    print("任务：正在执行资源发现...")
    # 实现连接目标、获取数据的逻辑
    # 使用 config['target_info'], config['credential_id']
    # 返回原始数据列表，键为源字段名
    return [
        {"instance_id": "srv-001", "hostname": "host1", "ip_address": "********", "os_version": "Ubuntu 22.04"},
        {"instance_id": "srv-002", "hostname": "host2", "ip_address": "********", "os_version": "CentOS 7"},
    ]

@task
def process_data(data: List[Dict[str, Any]], mapping: Optional[Dict[str, str]]) -> List[Dict[str, Any]]:
     """(可选) 根据映射或其他规则处理数据"""
     print("任务：正在处理发现的数据...")
     # 如果需要，可以在这里应用 mapping 或其他转换逻辑
     # 注意：CMDB 后端在接受时也会应用映射，这里是可选的预处理
     return data

@task
def report_to_cmdb(type_id: int, adt_id: int, instances: List[Dict[str, Any]]):
    """调用 POST /adc 上报数据"""
    print(f"任务：正在向 CMDB 上报 {len(instances)} 条实例数据...")
    payload = {
        "type_id": type_id,
        "adt_id": adt_id,
        "instances": instances
    }
    # response = httpx.post(f"{CMDB_API_URL}/adc", json=payload, headers=AUTH_HEADERS)
    # response.raise_for_status()
    # return response.json()
    # 示例返回
    return {"message": "数据已接收"}

```

### 8.4 编码规范与建议

为了提高 Flow 的可维护性和一致性，建议采用以下方式组织代码：

1.  **使用类封装逻辑 (内嵌 Pydantic 模型):** 将特定发现类型的逻辑和其数据模型封装在一个类中，使 Flow 函数本身保持简洁。

    ```python
    from prefect import flow, task
    from typing import List, Dict, Any, Optional, Tuple, Type
    from pydantic import BaseModel, Field

    # --- 发现逻辑基类 ---
    class BaseDiscoveryLogic:
        """发现逻辑的基类"""
        def __init__(self, config: Dict[str, Any]):
            self.config = config
            # 初始化连接或其他资源

        @classmethod
        def get_instance_model(cls) -> Type[BaseModel]:
            """必须由子类实现，返回其内部定义的 Pydantic 数据模型"""
            raise NotImplementedError("子类必须实现 get_instance_model 并返回 Pydantic 模型")

        @classmethod
        def get_attributes_metadata(cls) -> List[Tuple[str, str, str]]:
            """根据子类提供的 Pydantic 模型自动生成属性元数据"""
            model = cls.get_instance_model() # 获取子类定义的模型
            attributes = []
            schema = model.model_json_schema()
            properties = schema.get('properties', {})
            required = schema.get('required', [])
            
            for name, details in properties.items():
                # ... (类型映射逻辑保持不变) ...
                field_type = details.get('type', 'String') # 默认为 String
                if field_type == 'integer':
                    field_type = 'Integer'
                elif field_type == 'number': # Pydantic 'number' -> CMDB 'Float'
                    field_type = 'Float' 
                elif field_type == 'string' and details.get('format') == 'date-time':
                     field_type = 'DateTime'
                elif field_type == 'object' or field_type == 'array':
                     field_type = 'JSON'
                else: # 默认或无法映射的 string, boolean 等都视为 String
                    field_type = 'String'
                    
                description = details.get('description', f'{name} 字段')
                attributes.append((name, field_type, description))
            return attributes

        @classmethod
        def get_unique_key_name(cls) -> str:
            """返回唯一键的字段名 (基于子类模型)"""
            model = cls.get_instance_model() # 获取子类定义的模型
            # 假设模型的第一个字段是唯一键
            first_field = next(iter(model.model_fields), None)
            if first_field:
                return first_field
            raise NotImplementedError("未能从模型确定唯一键字段")

        def discover(self) -> List[BaseModel]: # 返回模型实例列表
            """执行发现并返回符合子类模型的实例列表"""
            raise NotImplementedError("子类必须实现 discover 方法")
            
        def report_data(self, discovered_data: List[BaseModel]):
            """准备数据并调用上报 Task"""
            print(f"准备上报 {len(discovered_data)} 条数据...")
            # 将模型列表转换为字典列表以符合 /adc 接口要求
            instances_to_report = [instance.model_dump(exclude_unset=True) for instance in discovered_data]
            
            if not instances_to_report:
                 print("没有数据需要上报。")
                 return

            # 调用 report_to_cmdb Task (Task 定义保持不变)
            report_to_cmdb(
                type_id=self.config['type_id'],
                adt_id=self.config['adt_id'],
                instances=instances_to_report
            )


    # --- 实现特定发现逻辑 ---
    class MySpecificDiscoveryLogic(BaseDiscoveryLogic):
        """实现特定发现逻辑，内嵌数据模型定义"""
        
        # --- 在逻辑类内部定义 Pydantic 模型 ---
        class MyDiscoveredInstance(BaseModel):
            instance_id: str = Field(..., description="实例的唯一ID")
            hostname: Optional[str] = Field(None, description="主机名")
            ip_address: Optional[str] = Field(None, description="主IP地址")
            os_version: Optional[str] = Field(None, description="操作系统版本")
            # ... 其他特定字段
        
        @classmethod
        def get_instance_model(cls) -> Type[BaseModel]:
            # 返回内部定义的模型类
            return cls.MyDiscoveredInstance 
        
        # get_attributes_metadata 和 get_unique_key_name 从基类继承，自动生成

        def discover(self) -> List[MyDiscoveredInstance]: # 明确返回内部模型类型
            # 实现具体的发现代码
            print("执行 MySpecificDiscoveryLogic 的发现...")
            # ... 连接目标、获取数据 ...
            raw_data = [
                {"instance_id": "srv-001", "hostname": "host1", "ip_address": "********", "os_version": "Ubuntu 22.04"},
                {"instance_id": "srv-002", "hostname": "host2", "ip_address": "********"}, # os_version 为 None
            ]
            # 使用内部定义的模型进行数据校验和转换
            validated_data = [self.MyDiscoveredInstance(**item) for item in raw_data]
            return validated_data
        
        # report_data 从基类继承，实现了数据转换和调用 Task


    # --- Prefect Flow ---
    @flow(name="my-discovery-flow")
    def my_discovery_flow(adt_id: int, get_metadata_only: bool = False):

        # 元数据模式
        if get_metadata_only:
            # 直接从特定逻辑类获取元数据
            metadata = {
                "unique_key": MySpecificDiscoveryLogic.get_unique_key_name(),
                "attributes": MySpecificDiscoveryLogic.get_attributes_metadata()
            }
            print("元数据模式：返回字段信息")
            return metadata

        # 正常发现模式
        print(f"正常发现模式：adt_id={adt_id}")
        config = get_cmdb_config(adt_id) # 调用 Task 获取配置
        config['adt_id'] = adt_id # 确保 adt_id 在 config 中
        
        logic_instance = MySpecificDiscoveryLogic(config)

        discovered_data: List[MySpecificDiscoveryLogic.MyDiscoveredInstance] = logic_instance.discover()

        # 上报数据
        logic_instance.report_data(discovered_data)

        return {"status": "reported", "count": len(discovered_data)}

    # --- Tasks (get_cmdb_config, report_to_cmdb) 定义保持不变 ---
    # @task
    # def get_cmdb_config(adt_id: int) -> Optional[Dict[str, Any]]: ...
    # @task
    # def report_to_cmdb(type_id: int, adt_id: int, instances: List[Dict[str, Any]]): ...
    ```

2.  **使用 Prefect Tasks:** 将 IO 密集型操作（如 API 调用、设备连接）封装在 `@task` 装饰的函数中，以便 Prefect 进行更好的调度、重试和状态跟踪。
3.  **配置与代码分离:** 避免在 Flow 代码中硬编码 URL、认证信息等。这些应通过 CMDB 配置 (`/adt/prefect/sync`) 获取，或使用 Prefect Blocks 进行管理。
4.  **清晰的日志:** 在 Flow 和 Task 中添加有意义的日志输出，方便调试和追踪问题。
5.  **错误处理:** 在 Flow 和 Task 中实现健壮的错误处理逻辑，例如使用 try-except 块捕获异常，并考虑失败时的处理策略（如重试、记录错误状态）。
6.  **依赖管理:** 如果 Flow 依赖特定的 Python 包，确保运行 Flow 的 Prefect Agent 环境已安装这些依赖。

### 8.5 flow UML

```mermaid
classDiagram
    class BaseDiscoveryLogic {
        <<Abstract>>
        #config: Dict
        +__init__(config: Dict) void
        +get_instance_model()*$ Type~BaseModel~
        +get_attributes_metadata()$ List~Tuple~
        +get_unique_key_name()$ str
        +discover()* List~BaseModel~
        +report_data(discovered_data: List~BaseModel~) void
    }

    class MySpecificDiscoveryLogic {
        +get_instance_model()$ Type~MyDiscoveredInstance~
        +discover() List~MyDiscoveredInstance~
    }

    class MyDiscoveredInstance {
        <<Pydantic, Inner>>
        +instance_id: str
        +hostname: Optional~str~
        +ip_address: Optional~str~
        +os_version: Optional~str~
    }

    BaseDiscoveryLogic <|-- MySpecificDiscoveryLogic
    MySpecificDiscoveryLogic *-- "1" MyDiscoveredInstance : defines >

    %% Notes on interactions (optional)
    %% note for MySpecificDiscoveryLogic "report_data() calls report_to_cmdb task"
    %% note "Prefect flow 'my_discovery_flow' uses MySpecificDiscoveryLogic"
```
### 8.6 SQL

```sql
ALTER TABLE `c_ad_ci_types`
CHANGE `agent_id` `agent_id` varchar(64) COLLATE 'utf8_unicode_ci' NULL AFTER `auto_accept`;
```




通过遵循这些规范，可以确保新开发的 Prefect Discovery Flow 能够可靠地集成到 CMDB 自动发现框架中，并易于维护和扩展。