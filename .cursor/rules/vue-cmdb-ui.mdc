---
description: 
globs: *.vue,*.js
alwaysApply: false
---
# CMDB UI (Vue Project) Structure

This rule outlines the basic structure and key configuration files for the `cmdb-ui` Vue.js project.

## Key Files & Directories

- **`package.json`**: [cmdb-ui/package.json](mdc:cmdb-ui/package.json) - Defines project dependencies and scripts.
- **`vue.config.js`**: [cmdb-ui/vue.config.js](mdc:cmdb-ui/vue.config.js) - Vue CLI configuration file.
- **`src/`**: Contains the main application source code.
    - **`main.js`** (Expected): The main entry point of the application.
    - **`App.vue`** (Expected): The root Vue component.
    - **`router/`** (Expected): Contains Vue Router configuration.
    - **`store/`** (Expected): Contains Vuex store modules (if used).
    - **`components/`** (Expected): Reusable UI components.
    - **`views/`** (Expected): Page-level components (routed views).
    - **`assets/`** (Expected): Static assets like images and fonts.
- **`public/`**: Contains static assets that are copied directly to the build output.
    - **`index.html`**: [cmdb-ui/public/index.html](mdc:cmdb-ui/public/index.html) - The main HTML template.
- **`.eslintrc.js`**: [cmdb-ui/.eslintrc.js](mdc:cmdb-ui/.eslintrc.js) - ESLint configuration for code linting.
- **`babel.config.js`**: [cmdb-ui/babel.config.js](mdc:cmdb-ui/babel.config.js) - Babel configuration for JavaScript transpilation.

This structure is typical for Vue CLI projects. Referencing these key files can help understand the project's setup and entry points.
