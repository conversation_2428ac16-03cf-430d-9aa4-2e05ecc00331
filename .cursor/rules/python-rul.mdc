---
description: python
globs: *.py
alwaysApply: false
---
# 项目开发规范说明
## 您是 Python、Flask 和可扩展 API 开发方面的专家。
## 主要原则
- 用准确的 Python 示例撰写简洁的技术性回复。
- 优先考虑迭代和模块化，而不是代码重复。
- 使用带有助动词的描述性变量名（如 is_active、has_permission）。
- 目录和文件（如 blueprints/user_routes.py）使用小写，下划线。
- 为路线和实用功能命名出口。
- 酌情使用 "接收一个对象，返回一个对象"（RORO）模式。
- 需要在终端执行python3命令时，请使用pipenv run命令执行。
Python/Flask
- 使用 def 进行函数定义。
- 尽可能对所有函数签名使用类型提示。
- 文件结构：Flask 应用程序初始化、蓝图、模型、实用程序、配置。
- 避免在条件语句中使用不必要的大括号。
- 对于条件句中的单行语句，省略大括号。
- 使用简洁的单行语法来编写简单的条件语句（如 if condition: do_somethin()）。
- 新增的代码尽量使用Pydantic，利用类型提示简化数据处理。
错误处理和验证
- 优先处理错误和边缘案例：
- 在函数开始时处理错误和边缘情况。
- 对错误条件使用提前返回，以避免嵌套较深的 if 语句。
- 将路径放在函数的最后，以提高可读性。
- 避免不必要的 else 语句，而应使用 if-return 模式。
- 使用保护子句及早处理先决条件和无效状态。
- 实施适当的错误记录和用户友好型错误信息。
- 使用自定义错误类型或错误工厂进行一致的错误处理。
本文档旨在为参与本项目开发的成员提供一套统一的开发规范，以提高代码质量、维护效率和协作顺畅度。
## 1. 项目结构

项目采用典型的 Flask 应用结构，请遵循以下约定：

- `api/app.py`: 应用工厂，负责创建和配置 Flask 应用实例、注册扩展、蓝图、错误处理等。
- `api/extensions.py`: 集中初始化和管理项目使用的各类 Flask 扩展。
- `api/models/`: 存放 SQLAlchemy 数据库模型定义，按模块（如 `acl`, `cmdb`, `common_setting`）划分。
- `api/lib/`: 存放项目中的各类库、服务、工具类、装饰器等，按功能模块（如 `cmdb`, `perm`, `common_setting`）划分。
- `api/views/`: 定义 API 接口，使用 Flask Blueprint 和 Flask-RESTful 构建，并按模块划分。
- `api/tasks/`: 存放 Celery 异步任务定义。
- `api/commands/`: 存放 Flask CLI 自定义命令。
- `settings.py`: 项目配置文件。

## 2. 代码风格和格式化

遵循 Python PEP 8 编码规范。
本项目推荐使用以下自动化工具进行代码风格检查和格式化：

- **Black**: 代码格式化工具。
- **Flake8**: 代码风格检查工具。
- **isort**: 导入排序工具。

在提交代码前，请确保运行相关工具并修复提示的问题（可参考 `api/commands/common.py` 中的 `lint` 命令）。

## 3. API 设计

- 使用 Flask Blueprint 对不同功能的 API 进行模块化管理。
- 使用 Flask-RESTful 构建 RESTful API 接口，清晰定义资源和对应的 HTTP 方法（GET, POST, PUT, DELETE）。
- API URL 遵循 `/api/<version>/<module>/<resource>` 的格式（例如 `/api/v0.1/cmdb/ci`）。
- 请求参数和响应数据应保持一致性和可预测性。

## 4. 数据库交互

- 统一使用 SQLAlchemy ORM 进行数据库操作。
- 数据库模型应继承自 `api/lib/database.py` 中定义的 `Model` 或其他基础 Mixin 类，以便利用通用的 CRUD、软删除和时间戳等功能。
- 对于复杂查询，优先考虑使用 SQLAlchemy 的查询表达式，避免直接书写原始 SQL 语句，除非必要。
- 数据库模式的变更应通过 Flask-Migrate (Alembic) 生成和执行迁移脚本。

## 5. 异步任务

- 耗时或可延迟执行的操作（如数据同步、批量处理、发送通知等）应定义为 Celery 任务，放入 `api/tasks/` 目录，并通过消息队列进行异步执行。
- 任务定义应具有幂等性，以便在失败重试时不会产生副作用。

## 6. 缓存和性能

- 使用 Redis 进行应用层缓存，提高数据访问速度。
- 利用 Elasticsearch 进行高效的搜索和查询（特别是在 CMDB 中）。
- 在设计数据访问逻辑时，考虑缓存的使用策略，避免缓存雪崩、击穿等问题。

## 7. 认证和授权

- 认证和授权功能通过 `api/lib/perm/acl/` 模块提供的 ACL 框架进行管理。
- 用户认证可根据配置选择不同的后端（本地、LDAP, OAuth2/OIDC）。
- 资源访问权限通过角色和权限模型进行控制。
- 敏感操作应使用 `@role_required` 或 `@has_perm` 等装饰器进行权限检查。

## 8. 错误处理和日志记录

- 统一使用 `api/lib/resp_format.py` 中定义的错误码和错误信息格式。
- 错误处理应集中在 `api/app.py` 中进行，确保 API 返回一致的错误响应格式。
- 应用程序日志应按照 `api/app.py` 中配置的格式和级别进行记录，便于问题排查。

## 9. 依赖管理

- 项目依赖应记录在 `requirements.txt` 文件中，使用 pip 进行管理。
- 推荐使用虚拟环境进行开发。

## 10. 测试

- 编写单元测试和集成测试，确保代码的质量和功能的正确性。
- 测试代码应存放在 `tests/` 目录下。
- 运行测试命令进行验证。

## 11. 注释和文档

- 关键代码、复杂逻辑和公共函数应编写清晰的注释（包括 Docstring），说明其功能、参数、返回值和可能抛出的异常。
- 维护项目的 API 文档和开发文档。

## 12. `const.py` 的使用

项目中在各个模块（如 `cmdb`, `common_setting`, `perm/acl`）下都存在 `const.py` 文件。这些文件用于集中存放模块内部使用的常量、枚举值和固定配置。

- **用途**:
    - 定义具有特定含义的枚举类型（例如 `ValueTypeEnum`, `OperateType`, `HistoryStatusEnum`），提高代码可读性和可维护性。
    - 存放模块内部共享的固定值（例如 Redis Key 前缀 `REDIS_PREFIX_CI`, 特权用户列表 `PRIVILEGED_USERS`）。
    - 定义不同状态、类型、操作等的标准化标识符。
- **设计原则**:
    - 将模块相关的常量集中存放，避免硬编码和分散定义。
    - 使用枚举类型（继承自 `BaseEnum`）来定义有限集合的常量，提供 `is_valid` 和 `all` 等实用方法。
    - 常量命名应清晰、有意义，并使用全大写加下划线的格式。

## 13. `api/lib/cmdb/` 中各类 Manager 的设计原则

在 `api/lib/cmdb/` 目录下存在大量的以 "Manager" 结尾的类（例如 `CIManager`, `CITypeManager`, `AttributeManager`, `CIRelationManager`, `CITypeAttributeManager`, `PreferenceManager`, `AutoDiscoveryRuleManager`, `LoadAttrManager`, `CloudBillingManager` 等）。这些类遵循了以下设计原则：

- **职责单一**: 每个 Manager 类负责管理 CMDB 中特定领域或特定类型的实体及其相关的业务逻辑。例如，`CIManager` 负责 CI 实例的创建、更新、删除和查询，`CITypeManager` 负责 CI 类型的管理。
- **封装业务逻辑**: Manager 类封装了复杂的业务处理流程，将与数据库模型、缓存、权限、历史记录等多种组件的交互细节隐藏在其内部，对外提供简洁易懂的接口。
- **与 Model 分离**: Manager 类负责协调和调用数据库模型（位于 `api/models/cmdb.py`）进行数据存取，但本身不直接继承或扩展 Model 类，保持业务逻辑与数据模型的解耦。
- **与 Cache 协同**: Manager 类与 `api/lib/cmdb/cache.py` 中定义的缓存类紧密协作，负责读写缓存，提高数据访问性能。例如，在创建或更新 CI 后，会调用 `ci_cache` 任务更新缓存。
- **处理验证**: Manager 类负责执行业务层面的数据验证和约束检查，例如唯一性约束、关系约束、权限检查等。
- **记录历史**: 许多 Manager 类负责记录操作历史，调用 `api/lib/cmdb/history.py` 中定义的历史记录管理类。
- **协调异步任务**: 对于耗时的操作，Manager 类会调用 `api/tasks/cmdb.py` 中定义的 Celery 任务进行异步处理。
- **提供统一接口**: Manager 类对外提供高层次、面向业务的接口，供 `api/views/cmdb/` 中的 API 视图调用，避免视图函数中出现过多的业务逻辑。
- **利用 Mixin**: Manager 类本身可能不直接继承自 `CRUDMixin`，但它们通过调用继承了 `CRUDMixin` 的 Model 类来实现基本的 CRUD 操作。

通过这种 Manager 设计模式，项目有效地组织了复杂的 CMDB 业务逻辑，提高了代码的可读性、可维护性和可测试性。